<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner :search-form="searchForm" code="" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="规格名称">
            <el-input v-model="searchForm.specName" placeholder="请输入规格名称" />
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" :table-data="tableList.records" :page-num="tableList.records" :page-size="tableList.size" :total="tableList.total" stripe  @change-page-num="changePageNum">
        <template #btn-inner>
          <el-button size="small" type="primary" @click="batchOperation('add')">
            新增
          </el-button>
        </template>
        <template #table-columns-before>
          <el-table-column label="序号" type="index" :index="indexMethod" width="200" />
          <el-table-column prop="specName" label="规格名称" :formatter="$utils.isEffective" show-overflow-tooltip />
          <el-table-column label="规格值" :formatter="$utils.isEffective" show-overflow-tooltip>
            <template #default="scope">
              <div v-if="scope.row.specValueJson">
                {{ JSON.parse(scope.row.specValueJson).join('，') }}
              </div>
            </template>
          </el-table-column>
        </template>
        <template #table-columns-after>
          <el-table-column label="操作" fixed="right" width="200">
            <template #default="scope">
              <el-link type="primary" @click="batchOperation('edit', scope.row)">
                编辑
              </el-link>
              <el-link type="danger" @click="batchOperation('del', scope.row)">
                删除
              </el-link>
            </template>
          </el-table-column>`
        </template>
      </table-inner>
    </div>
    <div v-if="showDialog">
      <edit-attr-dialog ref="editAttrDialogRef" v-model="editAttrDialogVisable" @submit="editAttrDialogSubmit" @close="editAttrDialogClose" />
    </div>
  </el-main>
</template>
<script>
import {cloneDeep, isEmpty, map} from 'lodash'
import { basePageListMixin } from '@fero/commons-vue'
import editAttrDialog from './components/edit-attr-dialog.vue'

export default {
  components: {
    editAttrDialog
  },
  mixins: [basePageListMixin],
  data () {
    return {
      /** @public */
      baseSearchForm: {
        // 规格属性名称
        specName: '',
      },
      tableList: {},
      loading: {
        list: false
      },
      editAttrDialogVisable: false,
      showDialog: false,
    }
  },
  created () {
  },
  methods: {
    /**
     * @description: 获取页面全部数据（左侧规格属性，右侧商品列表）
    */
    getList() {
      this.loading.list = true
      this.$api.goods.specifications.pageList(this.searchForm).then(result => {
        if (result.data.data && result.data.data.records && result.data.data.records.length > 0) {
          this.tableList = result.data.data
        } else {
          this.tableList = {}
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    /**
     * 新增编辑规格属性
    */
    batchOperation(type, row) {
      switch(type) {
        case 'edit':
          this.showDialog = true
          this.$nextTick(() => {
            this.$refs.editAttrDialogRef.title = '编辑属性'
            if (row.specValueJson) {
              const valueList = []
              const list = JSON.parse(row.specValueJson)
              list.forEach(item => {
                valueList.push({
                  value: item
                })
              })
              row.specValueList = valueList
            } else {
              row.specValueList = []
            }
            this.$refs.editAttrDialogRef.attrForm = row
            this.editAttrDialogVisable = true
          })
          break;
        case 'add':
          this.showDialog = true
          this.$nextTick(() => {
            this.$refs.editAttrDialogRef.attrForm = {
              attrName: '',
              formType: '',
              attrValueJson: '',
              attrValueList: []
            }
            this.$refs.editAttrDialogRef.title = '新增属性'
            this.editAttrDialogVisable = true
          })
          break;
        case 'del':
          this.$confirm(`确认要删除规格属性？`, `确认删除`).then(() => {
            const pidList = [row.pid]
            this.$api.goods.specifications.del(pidList).then(result => {
              this.$utils.resultBaseMessage(result)
              this.getList()
            })
          })
        break;
        default:
        break;
      }
    },
    /**
     * 新增编辑规格属性提交
    */
    editAttrDialogSubmit(data) {
      this.editAttrDialogVisable = false
      this.getList()
      this.showDialog = false
    },
    /**
     * 新增编辑规格属性取消
    */
    editAttrDialogClose() {
      this.editAttrDialogVisable = false
      this.showDialog = false
    }
  },
}
</script>
<style lang="scss" scoped>
</style>
