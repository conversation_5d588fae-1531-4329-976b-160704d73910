<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner :search-form="searchForm" code="" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="品牌编码">
            <el-input v-model="searchForm.brandCode" placeholder="请输入品牌编码" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品牌名称">
            <el-input v-model="searchForm.brandName" placeholder="请输入品牌名称" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态">
              <el-option label="全部" value="" />
              <el-option v-for="val in $utils.getEnableDictStatus('BRAND_STATUS')" :key="val.value" :label="val.label" :value="val.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <!-- show-export-btn -->
      <table-inner v-loading="loading.list" show-set-columns :column-option="columnOption" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe row-key="pid" @selection-change="handleSelectionChange" @change-page-num="changePageNum" @change-page-size="changePageSize" @export-file="exportFile">
        <template #btn-inner>
          <el-button size="small" type="primary" @click="dialog.brand.visible = true">
            新增
          </el-button>
          <el-button size="small" type="primary" @click="batchOperation('enable')">
            启用
          </el-button>
          <el-button size="small" type="danger" @click="batchOperation('disable')">
            禁用
          </el-button>
          <el-button size="small" type="danger" @click="batchOperation('del')">
            删除
          </el-button>
        </template>
        <template #table-columns-before>
          <el-table-column type="selection" />
        </template>
        <template #table-columns-after>
          <el-table-column label="操作" fixed="right">
            <template #default="scope">
              <el-link type="primary" @click="edit(scope.row)">
                编辑
              </el-link>
            </template>
          </el-table-column>`
        </template>
      </table-inner>
    </div>
    <dialog-inner v-model="dialog.brand.visible" :title="dialog.brand.title" :loading="loading.submit" width="50%" @submit="submit" @close="closeDialog">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="brandForm" :rules="rules" :model="brandForm" label-width="120px" label-suffix="：">
            <el-form-item v-if="brandForm.pid" label="品牌编码">
              <p>{{ brandForm.brandCode }}</p>
            </el-form-item>
            <el-form-item label="品牌名称" prop="brandName">
              <el-input v-if="!brandForm.pid" v-model="brandForm.brandName" placeholder="请输入品牌名称" />
              <p v-else>
                {{ brandForm.brandName }}
              </p>
            </el-form-item>
            <el-form-item label="备注" prop="remarks">
              <el-input v-model="brandForm.remarks" placeholder="请输入备注" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
  </el-main>
</template>
<script>
import { basePageListMixin } from '@fero/commons-vue'
import { cloneDeep, isEmpty, map } from 'lodash'
class BrandForm {
  pid
  brandName
  remarks
}
export default {
  mixins: [basePageListMixin],
  data () {
    return {
      /** @public */
      exportApi: this.$api.goods.brand.export,
      /** @public */
      baseSearchForm: {
        status: ''
      },
      columnOption: [
        { label: '品牌编码', prop: 'brandCode', formatter: this.$utils.isEffective },
        { label: '品牌名称', prop: 'brandName', formatter: this.$utils.isEffective },
        { label: '备注', prop: 'remarks', formatter: this.$utils.isEffective },
        { label: '状态', prop: 'status', formatter: this.$utils.tableStatusFormat('BRAND_STATUS', { valueTypeToString: true }) }
      ],
      brandForm: new BrandForm(),
      rules: {
        brandName: this.$rulesToolkit.createRules({ name: '品牌名称', required: true, range: { max: 64 } }),
        remarks: this.$rulesToolkit.createRules({ name: '备注', range: { max: 500 } })
      },
      checkboxDataList: [],
      dialog: {
        brand: {
          title: '新增商品品牌',
          visible: false
        }
      },
      loading: {
        export: false,
      }
    }
  },
  created () {
  },
  methods: {
    /**
     * @public
    */
    getList () {
      this.loading.list = true
      this.$api.goods.brand.pageList(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    /**
     * 列表选择
    */
    handleSelectionChange (val) {
      this.checkboxDataList = val
    },
    /**
     * @description: 禁用启用商品属性
    */
    batchOperation (type) {
      const operation = {
        del: {
          label: '删除',
          api: this.$api.goods.brand.del
        },
        enable: {
          label: '启用',
          api: this.$api.goods.brand.enable
        },
        disable: {
          label: '禁用',
          api: this.$api.goods.brand.disable
        }
      }
      if (isEmpty(this.checkboxDataList)) {
        this.$message.warning(`请选择要${operation[type].label}的品牌`)
        return false
      }
      let pidList = []
      pidList = map(this.checkboxDataList, 'pid')
      this.$confirm(`确认要${operation[type].label}品牌？`, `确认${operation[type].label}`).then(() => {
        operation[type].api(pidList).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    /**
     * 品牌编辑
     * @param row
     */
    edit (row) {
      const { pid, brandName, brandCode, remarks } = row
      this.brandForm = { pid, brandName, brandCode, remarks }
      this.dialog.brand.title = '编辑商品品牌'
      this.dialog.brand.visible = true
    },
    /**
     * 品牌提交
     */
    submit () {
      this.$refs.brandForm.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.brandForm)
        let api = this.$api.goods.brand.add
        if (formData.pid) {
          api = this.$api.goods.brand.edit
        }
        api(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
          this.dialog.brand.visible = false
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    /**
     * 关闭品牌弹窗
     */
    closeDialog () {
      this.dialog.brand.title = '新增商品品牌'
      this.brandForm = new BrandForm()
      this.$refs.brandForm.resetFields()
    },
    // 导出
    exportFile ({ fieldNames }) {
      if (this.exportApi) {
        this.loading.export = true
        this.searchForm.fieldNames = fieldNames || ''
        this.exportApi(this.searchForm).then(result => this.$utils.exportFile(result)).finally(() => {
          this.loading.export = false
        })
      }
    },
  }
}
</script>
<style lang="scss">
</style>
