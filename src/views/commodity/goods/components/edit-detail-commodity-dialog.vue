<!--
 * @Author: songchangfu
 * @Date: 2024-01-10
 * @Description: 解绑绑定商品属性弹窗
-->
<template>
  <dialog-inner v-model="localModelValue" :title="title" :loading="submitLoading" width="50%" @submit="submit" @close="close">
    <div v-loading="contentLoading" class="form-area padding-left-right-big">
      <el-form ref="commodityForm" :model="commodityForm" label-width="120px">
        <el-row :gutter="80">
          <el-col :span="11">
            <el-form-item label="商品分类编码：">
              SP1230123123
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="商品分类名称：">
              <el-input v-if="type !== 'detail'" v-model="commodityForm.name" placeholder="请输入商品分类名称" />
              <p v-else>
                无烟煤
              </p>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="上级分类名称：">
              煤炭
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="分类层级：">
              二级
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="状态：">
              已启用
            </el-form-item>
          </el-col>
          <el-col class="form-border-top" :span="24">
            <el-form-item label="已绑定商品属性：" />
          </el-col>
          <el-col :span="24">
            <div v-if="type !== 'detail'" class="form-table-title">
              <div class="form-table-title-flex">
                已选：{{ chooseData.length }}
              </div>
              <el-button :disabled="chooseData.length === 0" size="small" type="danger" @click="changeCommodityEnable(0)">
                解绑商品属性
              </el-button>
            </div>
            <table-inner ref="multipleTableRef" v-loading="listLoading" :table-header="false" :table-footer="false" :table-data="propertiesList" stripe :row-key="getRowKeys" @selection-change="handleSelectionChange">
              <el-table-column v-if="type !== 'detail'" type="selection" label="选择" width="55" />
              <el-table-column prop="spuNo" label="商品属性编码" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="spuName" label="商品属性名称" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="scopeCode" label="商品属性类型" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="scopeCode" label="商品属性值/格式" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="enable" label="必填" :formatter="$utils.tableStatusFormat('classificationEnableList')" show-overflow-tooltip />
              <el-table-column label="展示位置" fixed="right">
                <template #default="scope">
                  {{ scope.row.scopeCode === '1' ? '商品列表' : '商品详情' }}
                </template>
              </el-table-column>
            </table-inner>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </dialog-inner>
</template>
<script>

export default {
  name: 'EditDetailCommodityDialog',
  props: {
    // 是否显示授权 Dialog
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'submit', 'update:modelValue'],
  data () {
    return {
      commodityForm: {},
      contentLoading: false,
      submitLoading: false,
      listLoading: false,
      propertiesList: [],
      chooseData: [],
      type: 'detail'
    }
  },
  computed: {
    localModelValue: {
      get () {
        return this.modelValue
      },
      set (value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  created () {
  },
  methods: {
    /**
     * 确认方法
    */
    submit () {
      this.commodityForm.attributeType = '自定义属性'
      this.$refs.commodityForm.validate().then(() => {
        this.$emit('submit')
      })
    },
    /**
     * 关闭方法
    */
    close () {
      this.$emit('close')
    }
  }
}
</script>
<style scoped>
.max-width{
  max-width: 250px;
}
.form-border-top {
  border-top: 1px solid #ebeef5;
}
.form-table-title {
  margin: 0 22px 10px;
  font-size: 12px;
  display: flex;
  align-items: center;
}
.form-table-title .form-table-title-flex {
    flex: 1;
  }
</style>
