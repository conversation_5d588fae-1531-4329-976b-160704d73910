<template>
  <fe-page-main>
    <div class="partition-table">
      <table-inner v-loading="loading.list" :table-data="tableData" :table-footer="false" title="通知规则" border :span-method="handleSpan">
        <el-table-column prop="appName" label="应用名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="msgType" label="消息类别" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="pid" label="规则ID" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="msgName" label="推送规则名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column label="规则状态">
          <template #default="scope">
            <el-switch v-model="scope.row.enable" :active-value="1" :inactive-value="0" @change="value => changeStatus(value, scope.row)" />
          </template>
        </el-table-column>
        <el-table-column label="短信通知">
          <template #default="scope">
            <template v-if="scope.row.smsSupport === 1">
              <el-switch v-model="scope.row.smsEnable" class="mr5" :active-value="1" :inactive-value="0" @change="value => changeSmsStatus(value, scope.row)" />
              <!--<el-link type="primary" @click="setting(scope.row, 'sms')">-->
              <!--  设置-->
              <!--</el-link>-->
              <el-link type="primary" @click="preview(scope.row, 'sms')">
                预览
              </el-link>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="邮件通知">
          <template #default="scope">
            <template v-if="scope.row.emailSupport === 1">
              <el-switch v-model="scope.row.emailEnable" class="mr5" :active-value="1" :inactive-value="0" @change="value => changeOtherStatus(value, 'EMAIL', scope.row)" />
              <el-link type="primary" @click="setting(scope.row, 'email')">
                设置
              </el-link>
              <el-link type="primary" @click="preview(scope.row, 'email')">
                预览
              </el-link>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="站内通知">
          <template #default="scope">
            <template v-if="scope.row.inmsgSupport === 1">
              <el-switch v-model="scope.row.inmsgEnable" class="mr5" :active-value="1" :inactive-value="0" @change="value => changeOtherStatus(value, 'INMSG', scope.row)" />
              <el-link type="primary" @click="setting(scope.row, 'pm')">
                设置
              </el-link>
              <el-link type="primary" @click="preview(scope.row, 'pm')">
                预览
              </el-link>
            </template>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <dialog-inner v-model="dialog.setting.visible" :title="dialog.setting.title" :show-submit="false" cancel-title="完成" @close="closeDialog">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="settingForm" :model="settingForm" :rules="rules" label-width="120px" label-suffix="：">
            <el-form-item label="">
              <el-button v-if="!settingForm.pid" :loading="loading.create" type="primary" @click="createDefaultTemplate">
                使用推荐模板
              </el-button>
              <el-button v-if="!settingForm.pid" type="primary" @click="editTemplate">
                创建个性模板
              </el-button>
              <el-button v-if="settingForm.pid" type="primary" @click="editTemplate">
                修改模板
              </el-button>
            </el-form-item>
            <el-form-item label="推送模板">
              <p v-if="settingForm.pid">
                {{ settingForm[typeInfo[settingForm.type].contentPath] }}
              </p>
              <p v-else>
                未配置
              </p>
            </el-form-item>
            <el-form-item v-if="settingForm.type === 'sms'" prop="templateId" label="短信模板ID">
              <el-input v-model="settingForm.smsTemplateId">
                <template #append>
                  <el-button :loading="loading.save" type="primary" @click="saveSmsTemplateId">
                    保存
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
    <dialog-inner v-model="dialog.preview" title="预览详情" :show-submit="false" cancel-title="关闭">
      <div class="form-area">
        <div class="form-inner">
          <el-form label-width="120px" label-suffix="：">
            <el-form-item label="预览">
              <div v-html="settingForm[typeInfo[settingForm.type].contentPath]" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
    <sms-template-dialog ref="smsTemplateDialog" @submit-success="editTemplateSuccess" />
    <template-dialog ref="templateDialog" @submit-success="editTemplateSuccess" />
  </fe-page-main>
</template>
<script>
import SmsTemplateDialog from '@/views/it/message-manage/components/sms-template-dialog.vue'
import TemplateDialog from '@/views/it/message-manage/components/template-dialog.vue'

export default {
  components: { TemplateDialog, SmsTemplateDialog },
  data () {
    return {
      tableData: [],
      settingForm: {
        type: 'email'
      },
      rules: {},
      dialog: {
        setting: {
          visible: false,
          title: ''
        },
        preview: false
      },
      loading: {
        list: false,
        save: false,
        create: false
      },
      typeInfo: {
        sms: {
          title: '短信通知',
          api: this.$api.it.message.rule.getSmsTemplateDetailByRuleId,
          contentPath: 'template'
        },
        email: {
          title: '邮件通知',
          channel: 'EMAIL',
          api: this.$api.it.message.rule.getEmailTemplateDetailByRuleId,
          contentPath: 'contentTemplate'
        },
        pm: {
          title: '站内通知',
          channel: 'INMSG',
          api: this.$api.it.message.rule.getEmailTemplateDetailByRuleId,
          contentPath: 'contentTemplate'
        }
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    changeStatus (value, row) {
      this.$api.it.message.rule.changeStatus({ pid: row.pid, enable: value }).then(result => {
        // this.$utils.resultBaseMessage(result)
      }).catch(e => {
        this.getList()
      })
    },
    changeSmsStatus (value, row) {
      this.$api.it.message.rule.changeSmsStatus({ pushRuleId: row.pid, enable: value }).then(result => {
        // this.$utils.resultBaseMessage(result)
      }).catch(e => {
        this.getList()
      })
    },
    changeOtherStatus (value, type, row) {
      this.$api.it.message.rule.changeEmailStatus({ pushRuleId: row.pid, channel: type, enable: value }).then(result => {
        // this.$utils.resultBaseMessage(result)
      }).catch(e => {
        this.getList()
      })
    },
    setting (row, type) {
      this.dialog.setting.title = `${this.typeInfo[type].title}模板设置`
      this.getTemplateDetail(row, type).then(result => {
        this.settingForm.type = type
        this.dialog.setting.visible = true
      })
    },
    getTemplateDetail (row, type) {
      return new Promise((resolve, reject) => {
        this.typeInfo[type].api({ pushRuleId: row.pid, smsConfigId: row.smsConfigId, channel: this.typeInfo[type].channel }).then(result => {
          this.settingForm = Object.assign(this.settingForm, result.data.data, { pushRuleId: row.pid, appCode: row.appCode, channel: this.typeInfo[type].channel, smsConfigId: row.smsConfigId })
          resolve(result.data.data)
        }).catch(e => {
          reject(e)
        })
      })
    },
    createDefaultTemplate () {
      const apiConfig = {
        sms: this.$api.it.message.rule.createDefaultSmsTemplate,
        email: this.$api.it.message.rule.createDefaultTemplate,
        pm: this.$api.it.message.rule.createDefaultTemplate
      }
      this.loading.create = true
      apiConfig[this.settingForm.type]({ pushRuleId: this.settingForm.pushRuleId, smsConfigId: this.settingForm.smsConfigId, channel: this.typeInfo[this.settingForm.type].channel }).then(result => {
        this.getTemplateDetail({ pid: this.settingForm.pushRuleId, smsConfigId: this.settingForm.smsConfigId }, this.settingForm.type)
      }).finally(() => {
        this.loading.create = false
      })
    },
    editTemplate () {
      if (this.settingForm.type === 'sms') {
        this.$refs.smsTemplateDialog.init(this.settingForm.pid, { source: 'rule', appCode: this.settingForm.appCode, pushRuleId: this.settingForm.pushRuleId, smsConfigId: this.settingForm.smsConfigId })
      } else {
        this.$refs.templateDialog.init(this.settingForm.pid, { source: 'rule', channel: this.settingForm.channel, appCode: this.settingForm.appCode, pushRuleId: this.settingForm.pushRuleId })
      }
    },
    editTemplateSuccess () {
      this.getTemplateDetail({ pid: this.settingForm.pushRuleId, smsConfigId: this.settingForm.smsConfigId }, this.settingForm.type)
    },
    saveSmsTemplateId () {
      this.loading.save = true
      this.$api.it.message.rule.editSmsTemplateId({ pid: this.settingForm.pid, smsTemplateId: this.settingForm.smsTemplateId }).then(result => {
        this.$utils.resultBaseMessage(result)
      }).finally(() => {
        this.loading.save = false
      })
    },
    preview (row, type) {
      this.getTemplateDetail(row, type).then(result => {
        this.settingForm.type = type
        this.dialog.preview = true
      })
    },
    handleSpan ({ row, column, rowIndex, columnIndex }) {
      if (row.spanConfig && row.spanConfig[column.property]) {
        return row.spanConfig[column.property]
      }
    },
    getList () {
      this.loading.list = true
      this.$api.it.message.rule.list().then(result => {
        const resData = result.data.data
        let nowAppCode = ''
        let nowMessageType = ''
        let firstAppIndex
        let firstMessageTypeIndex
        let appNum = 0
        let messageTypeNum = 0
        resData.forEach((item, index) => {
          item.spanConfig = {}
          if (item.appCode !== nowAppCode) {
            // 切换了应用开始重新计算相同应用数量
            if (firstAppIndex !== undefined) {
              // 已经有当前应用第一个条目数，将算出的合并数据写入第一条
              resData[firstAppIndex].spanConfig.appName = { rowspan: appNum, colspan: 1 }
            }
            // 写入当前新应用的所需数据，清理旧数据至默认值（强制消息类别重新计算）
            nowAppCode = item.appCode
            firstAppIndex = index
            appNum = 1
            firstMessageTypeIndex = undefined
            nowMessageType = undefined
            messageTypeNum = 0
          } else {
            // 应用没有切换，正常计数并将各个应用名称都设置为被合并状态
            appNum += 1
            resData[index].spanConfig.appName = { rowspan: 0, colspan: 0 }
            // 已经是最后一条，不会再触发应用切换
            if (index === resData.length - 1 && firstAppIndex !== undefined) {
              // 已经有当前应用第一个条目数，将算出的合并数据写入第一条
              resData[firstAppIndex].spanConfig.appName = { rowspan: appNum, colspan: 1 }
            }
          }
          // 切换了消息类别开始重新计算相同类别数量
          if (item.msgType !== nowMessageType) {
            if (firstMessageTypeIndex !== undefined) {
              // 已经有当前类别第一个条目数，将算出的合并数据写入第一条
              resData[firstMessageTypeIndex].spanConfig.messageType = { rowspan: messageTypeNum, colspan: 1 }
            }
            nowMessageType = item.msgType
            firstMessageTypeIndex = index
            messageTypeNum = 1
          } else {
            // 类别没有切换，正常计数并将各个类别都设置为被合并状态
            messageTypeNum += 1
            resData[index].spanConfig.messageType = { rowspan: 0, colspan: 0 }
            // 已经是最后一条，不会再触发消息类别切换
            if (index === resData.length - 1 && firstMessageTypeIndex !== undefined) {
              // 已经有当前类别第一个条目数，将算出的合并数据写入第一条
              resData[firstMessageTypeIndex].spanConfig.messageType = { rowspan: messageTypeNum, colspan: 1 }
            }
          }
        })
        this.tableData = resData
      }).finally(() => {
        this.loading.list = false
      })
    },
    closeDialog () {
      this.settingForm = {
        type: 'email'
      }
    }
  }
}
</script>
<style lang="scss"></style>
