<template>
  <dialog-inner v-model="dialog.visible" :title="`${!templateForm.pid ? '新增' : '编辑'}模板`" destroy-on-close :loading="loading.submit" @submit="submitTemplate" @close="closeDialog">
    <div class="form-area">
      <div class="form-inner">
        <el-form ref="templateForm" :model="templateForm" :rules="rules" label-width="100px" label-suffix="：">
          <el-form-item prop="channel" label="发送渠道">
            <el-select v-model="templateForm.channel" :disabled="!!templateForm.pid || source === 'rule'" @change="changeChannel">
              <el-option v-for="item in $dictStatus.SEND_CHANNEL_TYPES" :key="item.code" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <!--<el-form-item prop="appCode" label="应用名称">-->
          <!--  <el-select v-model="templateForm.appCode" :disabled="!!templateForm.pid || source === 'rule'" @change="changeApp">-->
          <!--    <el-option v-for="item in appList" :key="item.code" :label="item.name" :value="item.code" />-->
          <!--  </el-select>-->
          <!--</el-form-item>-->
          <el-form-item prop="pushRuleId" label="推送规则">
            <el-select v-model="templateForm.pushRuleId" :disabled="!!templateForm.pid || source === 'rule'" filterable @change="changeRule">
              <el-option v-for="item in ruleList" :key="item.pid" :label="item.msgName" :value="item.pid" />
            </el-select>
          </el-form-item>
          <el-form-item label="数据标签" class="data-tag-list">
            <el-tag v-for="item in dataTagList" :key="item.code" @click="setDataTag(item.code)">
              {{ item.name }}
            </el-tag>
          </el-form-item>
          <el-form-item prop="titleTemplate" label="模板标题">
            <el-input ref="titleTemplate" v-model="templateForm.titleTemplate" placeholder="请输入模板名称" @focus="lastFocus = 'title'" />
          </el-form-item>
          <el-form-item prop="contentTemplate" label="模板内容">
            <div class="w-full">
              <Editor
                ref="editorRef"
                v-model="templateForm.contentTemplate"
                license-key="gpl"
                :init="editorInit"
                @init="onEditorInit"
                @focus="lastFocus = 'content'"
              />
              <el-button :disabled="!templateForm.pushRuleId" class="mt10" type="primary" @click="setDefaultTemplate">
                使用推荐模板
              </el-button>
            </div>
          </el-form-item>
          <el-form-item prop="urlPath" label="跳转链接">
            <el-input v-model="templateForm.urlPath" placeholder="请输入跳转链接" />
          </el-form-item>
        </el-form>
      </div>
    </div>
  </dialog-inner>
</template>

<script>
import Editor from '@tinymce/tinymce-vue'
import { BaseObjectDataModel } from '@/assets/lib/data-model'
import { cloneDeep } from 'lodash'
class TemplateProp extends BaseObjectDataModel {
  channel
  appCode
  pushRuleId
  msgName
  titleTemplate
  contentTemplate
}
export default {
  name: 'TemplateDialog',
  components: { Editor },
  emits: ['submitSuccess'],
  data () {
    return {
      templateForm: new TemplateProp(),
      // 最后聚焦的输入框，用于找到数据标签填入的输入框
      lastFocus: null,
      editorInit: {
        height: 400,
        promotion: false,
        language: 'zh_CN',
        branding: false
      },
      editor: null,
      ruleList: [],
      dataTagList: [],
      rules: {
        channel: this.$rulesToolkit.createRules({ name: '发送渠道', required: true, trigger: 'change' }),
        appCode: this.$rulesToolkit.createRules({ name: '应用名称', required: true, trigger: 'change' }),
        pushRuleId: this.$rulesToolkit.createRules({ name: '推送规则', required: true, trigger: 'change' }),
        titleTemplate: this.$rulesToolkit.createRules({ name: '模板标题', required: true }),
        contentTemplate: this.$rulesToolkit.createRules({ name: '模板内容', required: true })
      },
      dialog: {
        visible: false
      },
      loading: {
        submit: false
      },
      source: ''
    }
  },
  created () {
  },
  methods: {
    /**
     * 初始化
     * @public
     * @param {string} [pid]
     * @param {object} [otherInfo]
     */
    init (pid, otherInfo) {
      if (otherInfo) {
        this.source = otherInfo.source
        this.templateForm.channel = otherInfo.channel
        this.templateForm.appCode = otherInfo.appCode
        this.templateForm.pushRuleId = otherInfo.pushRuleId
        if (!pid) {
          this.getRuleList().then(() => {
            this.changeRule(otherInfo.pushRuleId)
          })
        }
      }
      if (pid) {
        this.getDetail(pid).then(result => {
          this.getRuleList(true).then(() => {
            this.changeRule(result.pushRuleId)
            this.dialog.visible = true
          })
        })
      } else {
        this.dialog.visible = true
      }
    },
    onEditorInit (event, editor) {
      this.editor = editor
    },
    getDetail (pid) {
      return new Promise((resolve, reject) => {
        this.$api.it.message.template.detail(pid).then(result => {
          this.templateForm.updateAllKeys(result.data.data)
          resolve(result.data.data)
        }).catch(e => {
          reject(e)
        })
      })
    },
    submitTemplate () {
      this.$refs.templateForm.validate().then(() => {
        const formData = cloneDeep(this.templateForm)
        this.loading.submit = true
        let api = this.$api.it.message.template.add
        if (formData.pid) {
          api = this.$api.it.message.template.edit
        }
        api(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.dialog.visible = false
          this.$emit('submitSuccess', result.data.data)
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    changeChannel (val, init) {
      return new Promise((resolve, reject) => {
        if (init !== true) {
          this.templateForm.pushRuleId = ''
        }
        this.getRuleList()
      })
    },
    // changeApp (val, init) {
    //   if (init !== true) {
    //     this.templateForm.pushRuleId = ''
    //   }
    //   this.getRuleList()
    // },
    changeRule (val) {
      const ruleData = this.ruleList.find(item => item.pid === val) || {}
      this.templateForm.msgName = ruleData.msgName
      this.dataTagList = []
      if (ruleData.parameters) {
        this.dataTagList = JSON.parse(ruleData.parameters)
      }
    },
    getRuleList (editStatus) {
      return new Promise((resolve, reject) => {
        this.$api.it.message.rule.channelList({ appCode: this.templateForm.appCode, channel: editStatus ? null : this.templateForm.channel }).then(result => {
          this.ruleList = result.data.data || []
          resolve(result.data.data)
        }).catch(e => {
          reject(e)
        })
      })
    },
    setDataTag (code) {
      if (this.lastFocus === 'content') {
        if (this.editor) {
          this.editor.insertContent('${' + code + '}')
        }
        // this.$refs.richEditor.editor.focus()
        // this.$refs.richEditor.editor.insertText('${' + code + '}')
      } else {
        const titleInputDom = this.$refs.titleTemplate.ref
        if (titleInputDom.selectionStart !== undefined) {
          const title = this.templateForm.titleTemplate || ''
          const start = titleInputDom.selectionStart
          this.templateForm.titleTemplate = title.slice(0, start) + '${' + code + '}' + title.slice(start)
        }
      }
    },
    setDefaultTemplate () {
      this.$confirm('使用推荐模板将覆盖现有模板内容，确认使用吗？', '确认使用').then(() => {
        const ruleData = this.ruleList.find(item => item.pid === this.templateForm.pushRuleId) || {}
        const channelTemplatePath = {
          EMAIL: 'emailTemplate',
          INMSG: 'inmsgTemplate'
        }
        this.$refs.richEditor.editor.setHtml(ruleData[channelTemplatePath[this.templateForm.channel]])
      })
    },
    closeDialog () {
      this.templateForm = new TemplateProp()
      this.$refs.templateForm.resetFields()
      this.lastFocus = null
      this.dataTagList = []
    }
  }
}
</script>
<style lang="scss">
.data-tag-list {
  .el-tag {
    margin-right: 10px;
    cursor: pointer;
  }
}
.tox.tox-tinymce-aux,
.tox-fullscreen .tox.tox-tinymce-aux {
  z-index: 4000;
}
</style>
