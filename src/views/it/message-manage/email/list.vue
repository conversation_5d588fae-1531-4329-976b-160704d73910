<template>
  <fe-page-main>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="Email通道" :table-data="tableList" stripe :table-footer="false">
        <template #btn-inner>
          <el-button type="primary" @click="dialog.visible = true">
            新增配置
          </el-button>
        </template>
        <!--<el-table-column label="序号" type="index" />-->
        <el-table-column prop="pid" label="配置ID" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="display" label="发件人名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="emailAddress" label="邮件地址" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="host" label="服务器" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="port" label="端口号" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="enable" label="是否启用" :formatter="$utils.tableTrueFalseFormant()" />
        <el-table-column fixed="right" label="操作">
          <template #default="scope">
            <el-link v-if="scope.row.enable === 1" type="danger" @click="action(scope.row,0)">
              禁用
            </el-link>
            <el-link v-if="scope.row.enable === 0" type="primary" @click="action(scope.row,1)">
              启用
            </el-link>
            <el-link type="primary" @click="testEmail(scope.row)">
              调试
            </el-link>
            <el-link type="primary" @click="edit(scope.row)">
              编辑
            </el-link>
            <el-link type="danger" @click="remove(scope.row.pid)">
              删除
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <!-- Email弹层-->
    <dialog-inner v-model="dialog.visible" title="编辑Email配置" :loading="loading.submit" @submit="submitEmail" @close="closeDialog">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="emailForm" :model="emailForm" :rules="rules" label-width="120px" label-suffix="：">
            <el-form-item label="配置ID">
              <p>{{ $utils.isEffectiveCommon(emailForm.pid) }}</p>
            </el-form-item>
            <el-form-item prop="display" label="发件人名称">
              <el-input v-model="emailForm.display" placeholder="请输入发件人名称" />
            </el-form-item>
            <el-form-item prop="emailAddress" label="邮件地址">
              <el-input v-model="emailForm.emailAddress" placeholder="请输入邮件地址" />
            </el-form-item>
            <el-form-item prop="host" label="服务器">
              <el-input v-model="emailForm.host" placeholder="请输入服务器" />
            </el-form-item>
            <el-form-item prop="port" label="端口号">
              <el-input v-model="emailForm.port" placeholder="请输入端口" />
            </el-form-item>
            <el-form-item prop="user" label="用户名">
              <el-input v-model="emailForm.user" placeholder="请输入用户名" />
            </el-form-item>
            <el-form-item prop="password" label="密码">
              <el-input v-model="emailForm.password" type="password" placeholder="请输入密码" />
            </el-form-item>
            <el-form-item prop="enableSsl" label="是否启用SSL">
              <el-radio-group v-model="emailForm.enableSsl">
                <el-radio :label="1">
                  是
                </el-radio>
                <el-radio :label="0">
                  否
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
  </fe-page-main>
</template>

<script>
import { BaseObjectDataModel } from '@/assets/lib/data-model'
import { cloneDeep, get } from 'lodash'
import FePageMain from '@/components/base/fe-page-main.vue'
class EmailProp extends BaseObjectDataModel {
  pid
  display
  emailAddress
  host
  port
  user
  password
  enableSsl
}
export default {
  components: { FePageMain },
  mixins: [],
  data () {
    return {
      emailForm: new EmailProp(),
      tableList: [],
      rules: {
        display: this.$rulesToolkit.createRules({ name: '发件人名称', required: true }),
        emailAddress: this.$rulesToolkit.createRules({ name: '邮件地址', required: true, email: true }),
        host: this.$rulesToolkit.createRules({ name: '服务器', required: true }),
        port: this.$rulesToolkit.createRules({ name: '端口号', required: true, range: { number: true, min: 1, max: 65535 } }),
        user: this.$rulesToolkit.createRules({ name: '用户名', required: true, range: { max: 64 } }),
        password: this.$rulesToolkit.createRules({ name: '密码', required: true, range: { max: 64 } }),
        enableSsl: this.$rulesToolkit.createRules({ name: '是否启用SSL', required: true, trigger: 'change' })
      },
      dialog: {
        visible: false,
        test: false
      },
      loading: {
        test: false
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    edit (row) {
      this.emailForm = cloneDeep(row)
      this.rules.password[0].required = false
      this.dialog.visible = true
    },
    getList () {
      this.loading.list = true
      this.$api.it.message.email.allList().then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    // 启用禁用
    action (data, status) {
      const statusTxt = {
        1: '启用',
        0: '禁用'
      }
      this.$confirm(`确认${statusTxt[status]}此Email通道?`, `确认${statusTxt[status]}`).then(() => {
        const formData = {
          pid: data.pid,
          enable: status
        }
        this.$api.it.message.email.changeStatus(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    // 删除Email
    remove (pid) {
      this.$confirm('确认要删除该Email通道吗？', '确认删除').then(() => {
        this.$api.it.message.email.deleteMail(pid).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    saveEmail () {
      return new Promise((resolve, reject) => {
        this.$refs.emailForm.validate().then(() => {
          const formData = cloneDeep(this.emailForm)
          this.loading.submit = true
          let api = this.$api.it.message.email.addMail
          if (formData.pid) {
            api = this.$api.it.message.email.updateMail
          }
          api(formData).then(result => {
            this.$utils.resultBaseMessage(result)
            resolve(result.data.data)
          }).catch(e => {
            reject(e)
          }).finally(() => {
            this.loading.submit = false
          })
        }).catch(e => {
          reject(e)
        })
      })
    },
    testEmail (row) {
      this.$prompt('调试接收邮箱：', '调试', {
        confirmButtonText: '发送',
        customClass: 'require-confirm',
        inputPattern: /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
        inputErrorMessage: '邮箱格式错误'
      }).then(({ value }) => {
        this.$api.it.message.email.useTest({ receiveEmail: value, configId: row.pid }).then(result => {
          this.$utils.resultBaseMessage(result)
        }).catch(e => {
          this.$confirm(get(e, 'data.msg', ''), '错误信息')
        })
      })
    },
    // 提交Email
    submitEmail () {
      this.saveEmail().then(() => {
        this.getList()
        this.dialog.visible = false
      })
    },
    // 关闭弹层
    closeDialog () {
      this.emailForm = new EmailProp()
      this.$refs.emailForm.resetFields()
      this.rules.password[0].required = true
    }
  }
}
</script>
