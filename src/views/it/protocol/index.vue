<script setup>
import Editor from '@tinymce/tinymce-vue'
import FePageMain from '@/components/base/fe-page-main.vue'
import { onMounted, ref } from 'vue'
import { api } from '@/assets/lib/api'
import { utils } from '@/assets/lib/utils'
import { cloneDeep } from 'lodash'

const infoForm = ref({
  agreementContent: ''
})
const editorHeight = ref()
const setHeight = () => {
  const windowHeight = window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
  let header = document.getElementsByClassName('header')
  let pageTitle = document.getElementsByClassName('page-title')
  if (header) {
    header = document.getElementsByClassName('header')[0]
  }
  if (pageTitle) {
    pageTitle = document.getElementsByClassName('page-title')[0]
  }
  const headerHeight = header.offsetHeight || 0
  const pageTitleHeight = pageTitle.offsetHeight || 0
  // 82 是协议文本信息这个标题的高度
  editorHeight.value = windowHeight - headerHeight - pageTitleHeight - 36
}
const autoSetHeight = true
const editorInit = {
  height: autoSetHeight ? '100%' : 600,
  promotion: false,
  language: 'zh_CN',
  branding: false
}
const loading = ref({
  edit: false,
  detail: false
})
const visible = ref(false)
const getDetail = async () => {
  loading.value.detail = true
  api.config.agreement.info({ agreementType: 'USER_SHARING' }).then(res => {
    infoForm.value = res.data.data ?? {}
  }).finally(() => {
    loading.value.detail = false
  })
}
const save = () => {
  visible.value = true
}
const confirm = (requiresReview) => {
  const formData = cloneDeep(infoForm.value)
  formData.requiresReview = requiresReview
  loading.value.edit = true
  api.config.agreement.edit(formData).then(result => {
    utils.resultBaseMessage(result)
    visible.value = false
  }).finally(() => {
    loading.value.edit = false
  })
}
onMounted(() => {
  getDetail()
  if (autoSetHeight) {
    setHeight()
  }
})
</script>

<template>
  <fe-page-main>
    <template #btn-inner>
      <el-button :loading="loading.edit" type="primary" @click="save">
        保存
      </el-button>
    </template>
    <div v-loading="loading.detail" class="m-4" :style="{ height: `${editorHeight}px` }">
      <Editor
        ref="editorRef"
        v-model="infoForm.agreementContent"
        license-key="gpl"
        :init="editorInit"
      />
    </div>
    <el-dialog v-model="visible" width="35%" title="确认保存">
      <p class="m-4">
        您已更新授权协议内容，是否需要重置客户的“已同意”状态，让客户重新确认并同意新的协议？
      </p>
      <template #footer>
        <el-button :loading="loading.edit" @click="visible = false">
          取消操作
        </el-button>
        <el-button :loading="loading.edit" type="primary" @click="confirm(1)">
          重新确认同意
        </el-button>
        <el-button :loading="loading.edit" type="primary" plain @click="confirm(0)">
          仅保存协议内容
        </el-button>
      </template>
    </el-dialog>
  </fe-page-main>
</template>

<style lang="scss">

</style>
