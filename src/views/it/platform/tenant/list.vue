<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="租户名称">
            <el-input v-model="searchForm.name" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="租户类型">
            <el-select v-model="searchForm.type">
              <el-option label="请选择" value="" />
              <el-option v-for="item in $utils.getEnableDictStatus('tenantType')" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="关联主体">
            <el-input v-model="searchForm.mainName" />
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-area">
      <table-inner v-loading="loading.list" title="租户列表" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe row-key="pid" @changePageNum="changePageNum">
        <template #btn-inner>
          <el-button type="primary" size="small" @click="$router.push({ name: 'platformTenantAdd' })">
            新建
          </el-button>
        </template>
        <el-table-column prop="name" label="租户名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="type" label="租户类型" :formatter="$utils.tableStatusFormat('tenantType')" show-overflow-tooltip />
        <el-table-column prop="mainName" label="关联主体" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="mainType" label="主体类型" :formatter="$utils.tableStatusFormat('mainType')" show-overflow-tooltip />
        <el-table-column prop="enable" label="状态" :formatter="$utils.tableStatusFormat('disableEnable')" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作">
          <template #default="scope">
            <table-link :to="{ name: 'platformTenantDetail', query: { pid: scope.row.pid }}" type="primary">
              详情
            </table-link>
            <table-link :to="{ name: 'platformTenantEdit', query: { pid: scope.row.pid }}" type="primary">
              编辑
            </table-link>
            <template v-if="scope.row.type !== 'OWNER'">
              <el-link v-if="scope.row.enable === 0" type="primary" @click="changeStatus(scope.row)">
                启用
              </el-link>
              <el-link v-if="scope.row.enable === 1" type="danger" @click="changeStatus(scope.row)">
                禁用
              </el-link>
            </template>
            <el-link type="danger" @click="resetPassword(scope.row)">
              重置密码
            </el-link>
            <!--<el-link type="danger" @click="del(scope.row)">-->
            <!--  删除-->
            <!--</el-link>-->
          </template>
        </el-table-column>
      </table-inner>
    </div>
  </el-main>
</template>
<script>
import { basePageListMixin } from '@fero/commons-vue'

export default {
  mixins: [basePageListMixin],
  data () {
    return {
      /** @public */
      baseSearchForm: {
        name: '',
        type: '',
        mainName: ''
      },
      subjectList: []
    }
  },
  created () {
  },
  methods: {
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.it.platform.tenant.pageList(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    changeStatus (row) {
      const changeStatus = {
        0: {
          label: '启用',
          api: this.$api.it.platform.tenant.enable
        },
        1: {
          label: '禁用',
          api: this.$api.it.platform.tenant.disable
        }
      }
      this.$confirm(`确认${changeStatus[row.enable].label}此租户？`, `确认${changeStatus[row.enable].label}`).then(() => {
        changeStatus[row.enable].api(row.pid).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    resetPassword (row) {
      this.$confirm('确定要重置密码吗？ 系统初始密码为123456', '重置密码').then(() => {
        this.$api.it.organization.user.resetPassword({ pid: row.adminId, password: '123456' }).then(result => {
          this.$utils.resultBaseMessage(result)
        })
      })
    }
    // del (row) {
    //   this.$confirm('确认删除此租户？', '确认删除').then(() => {
    //     this.$api.it.platform.tenant.del(row.pid).then(result => {
    //       this.$utils.resultBaseMessage(result)
    //       this.getList()
    //     })
    //   })
    // }
  }
}
</script>
<style lang="scss"></style>
