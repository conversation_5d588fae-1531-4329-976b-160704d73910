<template>
  <el-main>
    <page-title show-back-btn />
    <div class="partition-area">
      <div class="detail-area">
        <el-form label-width="120px" label-suffix="：">
          <div class="area-title">
            <p class="title">
              租户信息
            </p>
          </div>
          <div class="detail-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="租户名称" prop="name">
                  <p>{{ $utils.isEffectiveCommon(tenantForm.name) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="租户类型" prop="type">
                  <p>{{ $utils.statusFormat(tenantForm.type, 'tenantType') }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="租户域名" prop="url">
                  <p>{{ $utils.isEffectiveCommon(tenantForm.url) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="有效期至">
                  <p>{{ $utils.dateFormat(tenantForm.expireTime) }}</p>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              租户权益
            </p>
          </div>
          <div class="detail-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="租户数量" prop="limit">
                  <p>{{ $utils.isEffectiveCommon(tenantForm.limit) }}</p>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              主体信息
            </p>
          </div>
          <div class="detail-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="租户类型" prop="mainType">
                  <p>{{ $utils.statusFormat(tenantForm.mainType, 'mainType') }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="关联主体">
                  <p>{{ $utils.isEffectiveCommon(tenantForm.mainName) }}</p>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              管理员信息
            </p>
          </div>
          <div class="detail-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item prop="name" label="用户名称">
                  <p>{{ $utils.isEffectiveCommon(tenantForm.userAddRequest.name) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="mobile" label="登录手机">
                  <p>{{ $utils.isEffectiveCommon(tenantForm.userAddRequest.mobile) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="name" label="账号名称">
                  <p>{{ $utils.isEffectiveCommon(tenantForm.userAddRequest.account) }}</p>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              开通平台应用
            </p>
          </div>
          <div class="detail-inner">
            <el-tree
              ref="platformAppTree"
              :data="platformAppTree"
              :props="treeProps"
              :expand-on-click-node="false"
              :default-checked-keys="defaultChecked"
              node-key="pid"
              show-checkbox
              default-expand-all
            />
          </div>
          <div class="area-title">
            <p class="title">
              开通客户应用
            </p>
          </div>
          <div class="detail-inner">
            <el-tree
              ref="customerAppTree"
              :data="customerAppTree"
              :props="treeProps"
              :expand-on-click-node="false"
              :default-checked-keys="defaultChecked"
              node-key="pid"
              show-checkbox
              default-expand-all
            />
          </div>
        </el-form>
      </div>
    </div>
  </el-main>
</template>
<script>
import { TenantAttr } from '@/assets/lib/data-model'
export default {
  data () {
    return {
      id: this.$route.query.pid,
      tenantForm: new TenantAttr(),
      platformAppTree: [],
      customerAppTree: [],
      defaultChecked: [],
      treeProps: {
        label: 'name',
        class: this.setTreeItemClass,
        disabled: 'pid'
      },
      loading: {
        detail: false
      }
    }
  },
  created () {
    this.getPlatformAppList()
    this.getCustomerAppList()
    if (this.id) {
      this.getDetail()
    }
  },
  methods: {
    getDetail () {
      this.loading.detail = true
      this.$api.it.platform.tenant.detail(this.id).then(result => {
        this.tenantForm = new TenantAttr(result.data.data)
        const checkIds = []
        this.tenantForm.appRoleList.forEach(item => {
          checkIds.push(item.appRoleId)
        })
        this.defaultChecked = checkIds
      }).finally(() => {
        this.loading.detail = false
      })
    },
    getPlatformAppList (appType) {
      this.$api.it.application.role.roleTree({ appType: 'PLATFORM' }).then(result => {
        this.platformAppTree = result.data.data || []
      })
    },
    getCustomerAppList (appType) {
      this.$api.it.application.role.roleTree({ appType: 'CUSTOMER' }).then(result => {
        this.customerAppTree = result.data.data || []
      })
    },
    setTreeItemClass (data, node) {
      if (data.level === 0) {
        return 'next-inline-node'
      }
    }
  }
}
</script>
<style lang="scss"></style>
