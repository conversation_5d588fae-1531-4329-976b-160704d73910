<template>
  <el-main>
    <page-title>
      <el-button :loading="loading.submit" type="primary" @click="confirmEdit">
        保存
      </el-button>
    </page-title>
    <div class="partition-area">
      <div v-loading="loading.detail" class="form-area">
        <el-form ref="tenantForm" :model="tenantForm" :rules="rules" label-width="120px" label-suffix="：">
          <div class="area-title">
            <p class="title">
              租户信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="租户名称" prop="name">
                  <el-input v-model="tenantForm.name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="租户类型" prop="type">
                  <el-select v-model="tenantForm.type" :disabled="editStatus">
                    <el-option v-for="item in $status.tenantType" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="租户域名" prop="url">
                  <el-input v-model="tenantForm.url" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="validStartTime" label="有效期至">
                  <el-date-picker
                    v-model="tenantForm.expireTime"
                    :default-time="defaultTime"
                    :disabled="tenantForm.type === 'OWNER'"
                    type="date"
                    value-format="x"
                    range-separator="至"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              租户权益
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="租户数量" prop="limit">
                  <el-input-number v-model="tenantForm.limit" v-bind="inputNumberIntAttr" :disabled="tenantForm.type === 'OWNER'" placeholder="0为无限制" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              主体信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="主体类型" prop="mainType">
                  <el-select v-model="tenantForm.mainType" @change="changeMainType">
                    <el-option v-for="item in $utils.getEnableDictStatus('mainType')" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="关联主体" prop="mainId">
                  <el-select v-model="tenantForm.mainId" @change="changeMain">
                    <el-option v-for="item in subjectList" :key="item.pid" :label="item.name" :value="item.pid" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              管理员信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item prop="userAddRequest.name" label="用户名称">
                  <el-input v-model="tenantForm.userAddRequest.name" :disabled="editStatus" placeholder="请输入用户名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="userAddRequest.mobile" label="登录手机">
                  <el-input v-model="tenantForm.userAddRequest.mobile" :disabled="editStatus" placeholder="请输入手机" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="userAddRequest.account" label="账号名称">
                  <el-input v-model="tenantForm.userAddRequest.account" :disabled="editStatus" placeholder="请输入账号名称" />
                </el-form-item>
              </el-col>
              <el-col v-if="!editStatus" :span="12">
                <el-form-item prop="userAddRequest.password" label="初始密码">
                  <el-input v-model="tenantForm.userAddRequest.password" type="password" placeholder="请输入初始密码" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              开通平台应用
            </p>
          </div>
          <div class="form-inner">
            <el-tree
              ref="platformAppTree"
              :data="platformAppTree"
              :props="treeProps"
              :expand-on-click-node="false"
              :default-checked-keys="defaultChecked"
              default-expand-all
              node-key="pid"
              show-checkbox
            />
          </div>
          <div class="area-title">
            <p class="title">
              开通客户应用
            </p>
          </div>
          <div class="form-inner">
            <el-tree
              ref="customerAppTree"
              :data="customerAppTree"
              :props="treeProps"
              :expand-on-click-node="false"
              :default-checked-keys="defaultChecked"
              default-expand-all
              node-key="pid"
              show-checkbox
            />
          </div>
        </el-form>
      </div>
    </div>
  </el-main>
</template>
<script>
import { inputNumberIntAttr } from '@/assets/lib/misc'
import { cloneDeep, find } from 'lodash'
import { TenantAttr } from '@/assets/lib/data-model'

export default {
  data () {
    return {
      editStatus: this.$route.name === 'platformTenantEdit',
      id: this.$route.query.pid,
      inputNumberIntAttr,
      tenantForm: new TenantAttr(),
      rules: {
        name: this.$rulesToolkit.createRules({ name: '租户名称', required: true, range: { max: 64 } }),
        type: this.$rulesToolkit.createRules({ name: '租户类型', required: true, trigger: 'change' }),
        url: this.$rulesToolkit.createRules({ name: '租户域名', range: { max: 128 } }),
        mainType: this.$rulesToolkit.createRules({ name: '主体类型', required: true, trigger: 'change' }),
        mainId: this.$rulesToolkit.createRules({ name: '关联主体', required: true, trigger: 'change' }),
        userAddRequest: {
          name: this.$rulesToolkit.createRules({ name: '用户名称', required: true, range: { max: 64 } }),
          mobile: this.$rulesToolkit.createRules({ name: '手机号', required: true, mobilePhone: true }),
          account: this.$rulesToolkit.createRules({ name: '用户名称', required: true, range: { max: 64 } }),
          password: this.$rulesToolkit.createRules({ name: '密码', required: true, range: { max: 64 } })
        }
      },
      platformAppTree: [],
      customerAppTree: [],
      subjectList: [],
      defaultChecked: [],
      defaultTime: new Date('1970/01/01 23:59:59'),
      treeProps: {
        label: 'name',
        class: this.setTreeItemClass
      },
      loading: {
        submit: false,
        detail: false
      }
    }
  },
  created () {
    this.getPlatformAppList()
    this.getCustomerAppList()
    if (this.id) {
      this.getDetail()
    }
  },
  methods: {
    getDetail () {
      this.loading.detail = true
      this.$api.it.platform.tenant.detail(this.id).then(result => {
        this.tenantForm = new TenantAttr(result.data.data)
        this.changeMainType(this.tenantForm.mainType, true)
        const checkIds = []
        this.tenantForm.appRoleList.forEach(item => {
          checkIds.push(item.appRoleId)
        })
        this.defaultChecked = checkIds
      }).finally(() => {
        this.loading.detail = false
      })
    },
    changeMain (pid) {
      const mainItem = find(this.subjectList, { pid }) || {}
      this.tenantForm.mainName = mainItem.name
    },
    confirmEdit () {
      this.$refs.tenantForm.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.tenantForm)
        const customerRoles = this.$refs.customerAppTree.getCheckedNodes(true)
        const platformRoles = this.$refs.platformAppTree.getCheckedNodes(true)
        const customerRoleIds = []
        customerRoles.forEach(item => {
          if (item.level === 1) {
            customerRoleIds.push({
              roleId: item.pid,
              appId: item.appId
            })
          }
        })
        const platformRoleIds = []
        platformRoles.forEach(item => {
          if (item.level === 1) {
            platformRoleIds.push({
              roleId: item.pid,
              appId: item.appId
            })
          }
        })
        formData.appRoleList = platformRoleIds
        formData.appRole4ClientList = customerRoleIds
        let api = this.$api.it.platform.tenant.add
        if (formData.pid) {
          api = this.$api.it.platform.tenant.edit
        }
        api(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    getPlatformAppList (appType) {
      this.$api.it.application.role.roleTree({ appType: 'PLATFORM', enable: 1 }).then(result => {
        this.platformAppTree = result.data.data || []
      })
    },
    getCustomerAppList (appType) {
      this.$api.it.application.role.roleTree({ appType: 'CUSTOMER', enable: 1 }).then(result => {
        this.customerAppTree = result.data.data || []
      })
    },
    changeMainType (val, init) {
      if (init !== true) {
        this.tenantForm.mainId = ''
      }
      if (!val) {
        return false
      }
      const type = {
        PERSON: '20',
        ENTERPRISE: '10'
      }
      this.$api.principal.allList({ type: type[val], status: '40' }).then(result => {
        this.subjectList = result.data.data
      })
    },
    setTreeItemClass (data, node) {
      if (data.level === 0) {
        return 'next-inline-node'
      }
    }
  }
}
</script>
<style lang="scss"></style>
