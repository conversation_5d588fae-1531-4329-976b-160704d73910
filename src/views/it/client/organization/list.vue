<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="名称">
            <el-input v-model="searchForm.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态">
              <el-option v-for="item in $utils.getEnableDictStatus('disableEnable')" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="类型">
            <el-select v-model="searchForm.type" placeholder="请选择类型">
              <el-option v-for="item in $utils.getEnableDictStatus('customerType')" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <!--<el-col :span="6">
          <el-form-item label="主体类型">
            <el-select v-model="searchForm.type" placeholder="请选择主体类型">
              <el-option v-for="item in $utils.getEnableDictStatus('mainType')" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>-->
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="账号列表" :cell-class-name="setStatusColor" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe row-key="pid" @change-page-num="changePageNum" @change-page-size="changePageSize">
        <template #btn-inner>
          <auth-manage code="">
            <el-button type="primary" @click="addAccount">
              新建
            </el-button>
          </auth-manage>
        </template>
        <el-table-column prop="name" label="名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="account" label="账号" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="mainName" label="企业" :formatter="$utils.isEffective" show-overflow-tooltip />
        <!--<el-table-column prop="mainType" label="主体类型" :formatter="$utils.tableStatusFormat('mainType')" show-overflow-tooltip />-->
        <el-table-column prop="mainType" label="类型" :formatter="$utils.tableStatusFormat('customerType')" show-overflow-tooltip width="100" />
        <el-table-column prop="status" label="状态" :formatter="$utils.tableStatusFormat('disableEnable')" show-overflow-tooltip width="100" />
        <el-table-column prop="updateTime" label="更新时间" :formatter="$utils.tableDateTimeFormat" show-overflow-tooltip />
        <el-table-column :fixed="'right'" label="操作" min-width="80px">
          <template #default="scope">
            <el-link type="primary" @click="view(scope.row)">
              详情
            </el-link>
            <el-link type="primary" @click="edit(scope.row)">
              编辑
            </el-link>
            <!--<el-link type="primary" @click="setAppRole(scope.row)">-->
            <!--  应用授权-->
            <!--</el-link>-->
            <el-link type="danger" @click="resetPassword(scope.row)">
              重置密码
            </el-link>
            <el-link v-if="scope.row.status === 0" type="primary" @click="changeStatus(scope.row)">
              启用
            </el-link>
            <el-link v-else-if="scope.row.status === 1" type="danger" @click="changeStatus(scope.row)">
              禁用
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <!-- 客户弹层-->
    <dialog-inner v-model="dialog.add" :title="dialogTitle" :loading="loading.submit" @submit="submitDepartment" @close="closeDialog">
      <!--<el-alert :closable="false" description="请选择已认证的主体客户名称，选择业务角色，填写企业管理员用户信息，提交后系统将自动生成独立的组织客户根节点，生成企业管理员角色，生成企业管理员用户及账号信息。" type="warning" show-icon />-->
      <div v-loading="loading.account" class="form-area">
        <el-form ref="departmentForm" :model="departmentForm" :rules="rules" label-width="100px" label-suffix="：" autocomplete="off">
          <div class="area-title">
            <p class="title">
              选择客户
            </p>
          </div>
          <div class="form-inner">
            <!-- 隐藏字段，浏览器认为这是一个需要填充的字段 -->
            <input type="text" name="username" style="display:none">
            <input type="password" name="password" style="display:none">
            <el-form-item label="企业类型" prop="mainType">
              <el-radio-group v-model="departmentForm.mainType" :disabled="departmentForm.pid" @change="getCompanyList">
                <el-radio v-for="item in $utils.getEnableDictStatus('customerType')" :key="item.value" :value="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <!--<el-form-item label="主体类型" prop="mainType">
              <el-radio-group v-model="departmentForm.mainType">
                <el-radio v-for="item in $utils.getEnableDictStatus('mainType')" :key="item.value" :value="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>-->
            <el-form-item label="企业名称" prop="mainId">
              <el-select v-model="departmentForm.mainId" filterable placeholder="请选择企业名称" autocomplete="off" name="custom_company_name" :disabled="departmentForm.pid" @change="changeEntity">
                <el-option v-for="item in subjectList" :key="item.pid" :label="item.companyName" :value="String(item.pid)" />
              </el-select>
            </el-form-item>
          </div>
          <div class="area-title">
            <p class="title">
              账号信息
            </p>
          </div>
          <div class="form-inner">
            <el-form-item prop="name" label="名称">
              <el-input v-model.trim="departmentForm.name" name="custom_username" placeholder="请输入名称" autocomplete="off" />
            </el-form-item>
            <el-form-item prop="mobile" label="手机">
              <el-input v-model.trim="departmentForm.mobile" placeholder="请输入手机" autocomplete="off" />
            </el-form-item>
            <el-form-item prop="account" label="账号">
              <el-input v-model.trim="departmentForm.account" placeholder="请输入账号" autocomplete="off" :disabled="departmentForm.pid" />
            </el-form-item>
            <el-form-item prop="email" label="邮箱">
              <el-input v-model.trim="departmentForm.email" placeholder="请输入邮箱" autocomplete="off" />
            </el-form-item>
            <el-form-item v-if="!departmentForm.pid" prop="password" label="密码">
              <el-input v-model="departmentForm.password" type="password" placeholder="请输入密码" name="custom_password" autocomplete="new-password" />
            </el-form-item>
          </div>
        </el-form>
      </div>
    </dialog-inner>
    <dialog-inner v-model="dialog.view" :show-submit="false" title="账号详情" cancel-title="关闭" @close="closeDetailDialog">
      <div class="detail-area">
        <el-form :model="departmentForm" label-width="100px" label-suffix="：">
          <div class="area-title">
            <p class="title">
              企业信息
            </p>
          </div>
          <div class="detail-inner">
            <!--<el-form-item label="主体类型">-->
            <!--  <p>{{ $utils.statusFormat(departmentForm.mainType, 'mainType') }}</p>-->
            <!--</el-form-item>-->
            <!--<el-form-item label="云商代码">-->
            <!--  <p>{{ $utils.isEffectiveCommon(departmentForm.mainCode) }}</p>-->
            <!--</el-form-item>-->
            <el-form-item label="企业名称">
              <p>{{ $utils.isEffectiveCommon(departmentForm.mainName) }}</p>
            </el-form-item>
            <el-form-item label="组织代码">
              <p>{{ $utils.isEffectiveCommon(departmentForm.mainIdentifier) }}</p>
            </el-form-item>
            <!--<el-form-item label="客户名称">
              <p>{{ $utils.isEffectiveCommon(departmentForm.name) }}</p>
            </el-form-item>-->
          </div>
          <div class="area-title">
            <p class="title">
              账号信息
            </p>
          </div>
          <div class="detail-inner">
            <el-form-item label="名称">
              <p>{{ $utils.isEffectiveCommon(departmentForm.name) }}</p>
            </el-form-item>
            <el-form-item label="手机">
              <p>{{ $utils.isEffectiveCommon(departmentForm.mobile) }}</p>
            </el-form-item>
            <el-form-item label="账号">
              <p>{{ $utils.isEffectiveCommon(departmentForm.account) }}</p>
            </el-form-item>
            <el-form-item label="邮箱">
              <p>{{ $utils.isEffectiveCommon(departmentForm.email) }}</p>
            </el-form-item>
            <el-form-item label="状态">
              <p>{{ $utils.statusFormat(departmentForm.status, 'disableEnable') }}</p>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </dialog-inner>
    <dialog-inner v-model="dialog.package" :loading="loading.submit" title="应用授权" width="30%" @submit="confirmAppRuleAuth">
      <el-alert :closable="false" title="请选择应用角色，支持多选。" type="warning" show-icon />
      <div class="form-area">
        <div class="area-title">
          <p class="title">
            开通客户应用
          </p>
        </div>
        <div class="form-inner">
          <el-tree
            ref="customerAppTree"
            :data="customerAppTree"
            :props="treeProps"
            :expand-on-click-node="false"
            default-expand-all
            node-key="pid"
            show-checkbox
          />
        </div>
      </div>
    </dialog-inner>
  </el-main>
</template>

<script>
import { basePageListMixin } from '@fero/commons-vue'
import { cloneDeep, find, get } from 'lodash'
class DepartmentProp {
  pid
  // 用户类型 默认主用户
  type = '0'
  source = '0'
  mainType = 'CUSTOMER'
}
export default {
  components: {},
  mixins: [basePageListMixin],
  data () {
    return {
      /** @public */
      baseSearchForm: {
        // 客户名称
        name: ''
      },
      subjectList: [],
      // 创建客户
      departmentForm: new DepartmentProp(),
      activeOrgId: '',
      canEdit: false,
      rules: {
        // mainType: this.$rulesToolkit.createRules({ name: '主体类型', required: true, trigger: 'change' }),
        mainId: this.$rulesToolkit.createRules({ name: '企业名称', required: true, trigger: 'change' }),
        name: this.$rulesToolkit.createRules({ name: '名称', required: true, range: { max: 64 } }),
        account: this.$rulesToolkit.createRules({ name: '账号', required: true, range: { max: 64 } }),
        // password: this.$rulesToolkit.createRules({ name: '密码', required: true, range: { max: 64 } }),
        mobile: this.$rulesToolkit.createRules({ name: '手机', required: true, mobilePhone: true }),
        isDepartment: this.$rulesToolkit.createRules({ name: '是否是部门', required: true, trigger: 'change' })
      },
      customerAppTree: [],
      loading: {
        submit: false,
        account: false
      },
      dialogTitle: '新增账号',
      dialog: {
        add: false,
        view: false,
        package: false
      },
      treeProps: {
        label: 'name',
        class: this.setTreeItemClass
      }
    }
  },
  created () {
    this.getCustomerAppList()
  },
  methods: {
    addAccount () {
      this.getCompanyList(this.departmentForm.mainType)
      this.dialogTitle = '新增账号'
      this.dialog.add = true
    },
    setAppRole (row) {
      this.activeOrgId = row.pid
      this.$api.it.client.organization.detail(row.pid).then(result => {
        this.dialog.package = true
        const checkIds = []
        const appRoleList = get(result, 'data.data.appRoleList', [])
        appRoleList.forEach(item => {
          checkIds.push(item.clientRoleId)
        })
        this.$nextTick(() => {
          this.$refs.customerAppTree.setCheckedKeys(checkIds)
        })
      })
    },
    getCustomerAppList (appType) {
      this.$api.it.application.role.roleTree({ appType: 'CUSTOMER' }).then(result => {
        this.customerAppTree = result.data.data || []
      })
    },
    confirmAppRuleAuth () {
      this.loading.submit = true
      const formData = {
        clientUserId: this.activeOrgId,
        clientRoleList: []
      }
      const customerRoles = this.$refs.customerAppTree.getCheckedNodes(true)
      customerRoles.forEach(item => {
        if (item.level === 1) {
          formData.clientRoleList.push({
            roleId: item.pid,
            appId: item.appId
          })
        }
      })
      this.$api.it.client.organization.appRoleAuth(formData).then(result => {
        this.$utils.resultBaseMessage(result)
        this.dialog.package = false
      }).finally(() => {
        this.loading.submit = false
      })
    },
    getCompanyList (type, init) {
      // const type = {
      //   PERSON: '20',
      //   ENTERPRISE: '10'
      // }
      if (init !== true) {
        this.departmentForm.mainId = ''
        this.departmentForm.mainName = ''
        this.departmentForm.mainCode = ''
        this.departmentForm.name = ''
        this.departmentForm.mobile = ''
        this.departmentForm.account = ''
        this.departmentForm.password = ''
        this.departmentForm.mainIdentifier = ''
      }
      this.$api.business.getList({ type }).then(result => {
        this.subjectList = result.data.data
      })
      if (init !== true) {
        this.canEdit = this.departmentForm.mainType === 'FUND'
      } else {
        this.canEdit = true
      }
    },
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.it.client.organization.pageList(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    // 查看客户详情
    view (row) {
      this.departmentForm = row
      this.dialog.view = true
    },
    edit (row) {
      this.departmentForm = cloneDeep(row)
      this.dialogTitle = '编辑账号'
      this.getCompanyList(this.departmentForm.mainType, true)
      this.dialog.add = true
    },
    changeEntity (pid) {
      const mainInfo = find(this.subjectList, { pid: Number(pid) }) ?? {}
      this.departmentForm.mainName = mainInfo.companyName ?? ''
      this.departmentForm.mainCode = mainInfo.companyCode ?? ''
      this.departmentForm.mainIdentifier = mainInfo.socialCreditCode ?? ''
      this.departmentForm.mobile = ''
      this.departmentForm.account = ''
      this.departmentForm.password = ''
      this.departmentForm.name = mainInfo.companyName
      // if (this.departmentForm.mainType === 'CUSTOMER') {
      //   this.$api.it.client.organization.asyncAccount({ custNo: mainInfo.companyCode }).then(result => {
      //     const accountInfo = result.data.data
      //     if (!accountInfo) {
      //       this.departmentForm.name = mainInfo.companyName
      //       this.canEdit = true
      //     } else {
      //       this.canEdit = false
      //       this.departmentForm.name = accountInfo.nickname
      //       this.departmentForm.mobile = accountInfo.mobile
      //       this.departmentForm.account = accountInfo.username
      //     }
      //   })
      // } else {
      //   this.departmentForm.name = mainInfo.companyName
      // }
    },
    changeStatus (row) {
      const changeStatus = {
        0: {
          label: '启用',
          api: this.$api.it.client.organization.enable
        },
        1: {
          label: '禁用',
          api: this.$api.it.client.organization.disable
        }
      }
      this.$confirm(`确认${changeStatus[row.status].label}此客户？`, `确认${changeStatus[row.status].label}`).then(() => {
        changeStatus[row.status].api(row.pid).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    resetPassword (row) {
      this.$confirm('确定要重置企业管理员密码吗？系统初始密码为123456', '重置密码').then(() => {
        this.$api.it.client.organization.changePassword({ pid: row.pid, password: '123456' }).then(result => {
          this.$utils.resultBaseMessage(result)
        })
      })
    },
    // 提交客户
    submitDepartment () {
      this.$refs.departmentForm.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.departmentForm)
        let api = this.$api.it.client.organization.add
        if (formData.pid) {
          api = this.$api.it.client.organization.edit
        }
        api(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
          this.dialog.add = false
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    // 关闭弹层
    closeDialog () {
      this.departmentForm = new DepartmentProp()
      this.$refs.departmentForm.resetFields()
      this.canEdit = false
      this.subjectList = []
    },
    closeDetailDialog () {
      this.departmentForm = new DepartmentProp()
    },
    setTreeItemClass (data, node) {
      if (data.level === 0) {
        return 'next-inline-node'
      }
    },
    setStatusColor ({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'status') {
        switch (row.status) {
          case 1:
            return 'success'
          case 0:
            return 'danger'
        }
      }
    }
  }
}
</script>

<style>

</style>
