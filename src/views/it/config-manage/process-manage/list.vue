<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="应用名称">
            <el-select v-model="searchForm.appCode" placeholder="请选择应用名称" @change="getSearchDefinitionList">
              <el-option label="全部" value="" />
              <el-option v-for="item in appList" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="流程定义名称">
            <el-select v-model="searchForm.defKey" placeholder="请选择流程定义">
              <el-option label="全部" value="" />
              <el-option v-for="item in searchDefinitionList" :key="item.pid" :label="item.procName" :value="item.procKey" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="流程名称">
            <el-input v-model="searchForm.name" placeholder="请输入流程名称" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态">
            <el-select v-model="searchForm.enable" placeholder="请选择状态">
              <el-option label="全部" value="" />
              <el-option value="1" label="启用" />
              <el-option value="0" label="禁用" />
            </el-select>
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="流程管理" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe @change-page-num="changePageNum">
        <template #btn-inner>
          <el-button type="primary" @click="dialog.process.visible = true">
            新增流程
          </el-button>
        </template>
        <el-table-column label="序号" type="index" />
        <el-table-column prop="appName" label="应用名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="name" label="流程名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="defName" label="流程定义名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="defKey" label="流程定义编码" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="enable" label="状态" :formatter="$utils.tableTrueFalseFormant()" show-overflow-tooltip>
          <template #default="scope">
            <el-switch v-model="scope.row.enable" :active-value="1" :inactive-value="0" @change="value => changeEnableStatus(value, scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="type" label="流程类型" :formatter="$utils.tableStatusFormat('workflowType')" show-overflow-tooltip />
        <el-table-column prop="scopeName" label="适用部门" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="scope">
            <el-link type="primary" @click="edit(scope.row)">
              编辑
            </el-link>
            <table-link type="primary" :to="{ name: 'configManageProcessManageProcessEdit', query: { pid: scope.row.pid } }">
              流程设计
            </table-link>
            <el-link type="danger" @click="del(scope.row)">
              删除
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <dialog-inner v-model="dialog.process.visible" :loading="loading.submit" :title="dialog.process.title" @submit="confirmProcess" @close="closeDialog">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="processForm" :model="processForm" :rules="rules" label-width="120px" label-position="left" label-suffix="：">
            <el-form-item prop="appCode" label="应用名称">
              <el-select v-model="processForm.appCode" :disabled="!!processForm.pid" placeholder="请选择应用名称" filterable @change="getDefinitionList">
                <el-option v-for="item in appList" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item prop="defKey" label="流程定义名称">
              <el-select v-model="processForm.defKey" :disabled="!!processForm.pid" filterable placeholder="请选择流程类型">
                <el-option v-for="item in definitionList" :key="item.pid" :label="item.procName" :value="item.procKey" />
              </el-select>
            </el-form-item>
            <el-form-item label="流程定义编码">
              <p>{{ $utils.isEffectiveCommon(processForm.defKey) }}</p>
            </el-form-item>
            <el-form-item prop="name" label="流程名称">
              <el-input v-model="processForm.name" />
            </el-form-item>
            <el-form-item prop="type" label="流程类型">
              <el-radio-group v-model="processForm.type" @change="changeType">
                <el-radio v-for="item in $utils.getEnableDictStatus('workflowType')" :key="item.value" :label="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <!--<el-form-item prop="enable" label="状态">-->
            <!--  <el-select v-model="processForm.enable" placeholder="请选择状态">-->
            <!--    <el-option :value="1" label="启用" />-->
            <!--    <el-option :value="0" label="禁用" />-->
            <!--  </el-select>-->
            <!--</el-form-item>-->
            <el-form-item v-if="processForm.type === 0" label="适用组织">
              <el-tree-select
                v-model="processForm.scopeList"
                :data="orgTreeList"
                :props="treeProps"
                :check-strictly="true"
                :render-after-expand="false"
                node-key="pid"
                multiple
                show-checkbox
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
  </el-main>
</template>
<script>
import { basePageListMixin } from '@fero/commons-vue'
import { getAppListMixin } from '@/assets/lib/mixins'
import { cloneDeep } from 'lodash'

export default {
  mixins: [basePageListMixin, getAppListMixin],
  data () {
    return {
      processForm: {
        type: 0,
        scopeList: []
      },
      rules: {
        appCode: this.$rulesToolkit.createRules({ name: '应用名称', required: true, trigger: 'change' }),
        defKey: this.$rulesToolkit.createRules({ name: '流程定义名称', required: true, trigger: 'change' }),
        enable: this.$rulesToolkit.createRules({ name: '状态', required: true, trigger: 'change' }),
        type: this.$rulesToolkit.createRules({ name: '流程类型', required: true, trigger: 'change' }),
        name: this.$rulesToolkit.createRules({ name: '流程名称', required: true })
      },
      treeProps: {
        label: 'name',
        value: 'pid'
      },
      searchDefinitionList: [],
      definitionList: [],
      orgTreeList: [],
      dialog: {
        process: {
          title: '新增流程',
          visible: false
        }
      },
      loading: {
        list: false,
        submit: false
      }
    }
  },
  created () {
    this.getDeptList()
  },
  methods: {
    changeEnableStatus (value, row) {
      this.$api.workflow.processManage.changeStatus({ id: row.pid, enable: value }).then(result => {
        this.$utils.resultBaseMessage(result)
      }).catch(e => {
        this.getList()
        console.error(e)
      })
    },
    confirmProcess () {
      this.$refs.processForm.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.processForm)
        let api = this.$api.workflow.processManage.add
        if (formData.pid) {
          api = this.$api.workflow.processManage.edit
        }
        formData.scope = formData.scopeList.join(',')
        // delete formData.scopeList
        api(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
          this.dialog.process.visible = false
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    edit (row) {
      this.dialog.process.title = '编辑流程'
      const formData = cloneDeep(row)
      if (formData.scope === 'ALL') {
        delete formData.scope
      }
      if (formData.scope) {
        formData.scopeList = formData.scope.split(',')
      }
      this.processForm = formData
      this.getDefinitionList(formData.appCode, true)
      this.dialog.process.visible = true
    },
    del (row) {
      this.$confirm('确认删除此流程？', '确认删除').then(() => {
        this.$api.workflow.processManage.del({ id: row.pid }).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    getSearchDefinitionList (appCode) {
      this.$api.workflow.processManage.definitionList({ appCode }).then(result => {
        this.searchDefinitionList = result.data.data
      })
    },
    getDefinitionList (appCode, init = false) {
      this.$api.workflow.processManage.definitionList({ appCode }).then(result => {
        this.definitionList = result.data.data
        if (init !== true) {
          this.processForm.defKey = ''
        }
      })
    },
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.workflow.processManage.pageList(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    getDeptList () {
      this.$api.it.organization.department.treeList().then(result => {
        this.orgTreeList = result.data.data || []
      })
    },
    changeType () {
      if (this.processForm.type === 1) {
        this.processForm.scopeList = []
      }
    },
    closeDialog () {
      this.dialog.process.title = '新建流程'
      this.$refs.processForm.resetFields()
      this.processForm = {
        type: 0,
        scopeList: []
      }
    }
  }
}
</script>
<style lang="scss"></style>
