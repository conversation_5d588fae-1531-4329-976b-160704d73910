<!--
 * @Author: x<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-07-27 11:59:45
 * @Description: 流程定义
-->
<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="应用名称">
            <el-select v-model="searchForm.appCode" placeholder="请选择应用名称">
              <el-option v-for="item in applicationsOptions" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="流程名称">
            <el-input v-model="searchForm.procName" placeholder="请输入流程名称" />
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="流程定义" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe @change-page-num="changePageNum">
        <template #btn-inner>
          <el-button type="primary" @click="addPartition">
            新增流程
          </el-button>
        </template>
        <el-table-column label="序号" type="index" />
        <el-table-column prop="appName" label="应用名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="procName" label="流程定义名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="procKey" label="流程定义编码" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="130">
          <template #default="scope">
            <el-link v-if="scope.row.isPublish ===0" type="danger" @click="tableClick(scope.row,'del')">
              删除
            </el-link>
            <el-link v-else type="primary" @click="tableClick(scope.row,'versionManage')">
              版本管理
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <table-confirm v-if="passDialog.visible" v-model="passDialog.visible" :title="passDialog.title" :sure-word="passDialog.sureWord" :word="passDialog.word" :submit-loading="loading.delSubmit" @submit="submitDelDia" @close="closeDelDia">
      <template v-if="passDialog.childSlot === 'disabled'" #disabled>
        <el-form ref="submitForm" :model="submitForm" label-width="70px">
          <el-form-item label="应用名称">
            <el-input v-model="submitForm.appName" disabled />
          </el-form-item>
          <el-form-item label="流程名称">
            <el-input v-model="submitForm.procName" disabled />
          </el-form-item>
          <el-form-item label="流程编码">
            <el-input v-model="submitForm.procKey" disabled />
          </el-form-item>
        </el-form>
      </template>
    </table-confirm>
    <!-- 新增流程弹窗 -->
    <dialog-inner v-model="dialog.partitionVisible" title="新增流程" :loading="loading.partition" width="50%" @submit="submitPartition" @close="closePartition">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="partitionForm" :model="partitionForm" :rules="rules" label-width="100px">
            <el-form-item prop="appCode" label="应用名称">
              <el-select v-model="partitionForm.appCode" placeholder="请选择应用名称" clearable>
                <el-option v-for="item in applicationsOptions" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </el-form-item>
            <el-form-item prop="procName" label="流程定义名称">
              <el-input v-model.trim="partitionForm.procName" placeholder="请输入流程定义名称" />
            </el-form-item>
            <el-form-item prop="procKey" label="流程定义编码">
              <el-input v-model.trim="partitionForm.procKey" placeholder="请输入流程定义编码" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
  </el-main>
</template>

<script>
import tableConfirm from '@/components/table-click/confirm-dialog.vue'
import { basePageListMixin } from '@/assets/lib/mixins'
export default {
  components: { tableConfirm },
  mixins: [basePageListMixin],
  data () {
    return {
      searchForm: {
        appCode: '', // 应用名称
        procName: '', // 流程名称
        procType: '', // 流程状态
        procPreResult: '' // 上一节点结果
      },
      applicationsOptions: [], // 应用名称列表
      procTypeList: [], // 流程状态list
      procPreResultList: [], // 上一节点结果
      tableList: {},
      loading: {
        list: false,
        delSubmit: false,
        partition: false
      },
      passDialog: {
        visible: false,
        title: '',
        submitLoading: false
      },
      dialog: {
        partitionVisible: false
      },
      partitionForm: {
        appName: '',
        procName: '',
        partitionCode: ''
      },
      submitForm: {
        appName: '',
        procName: '',
        procCode: ''
      },
      rules: {
        appCode: this.$rulesToolkit.createRules({ name: '应用名称', required: true, trigger: 'change' }),
        procName: this.$rulesToolkit.createRules({ name: '流程名称', required: true, range: { max: 50 } }),
        procKey: this.$rulesToolkit.createRules({ name: '流程编码', required: true, pattern: this.$constants.patternProcKey, range: { max: 50 } })
      }
    }
  },
  created () {
    this.getApplicationsList()
  },
  methods: {
    // 获取应用名称列表
    getApplicationsList () {
      this.$api.it.application.apps.allList().then(result => {
        if (result.data.data) {
          this.applicationsOptions = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    // 获取流程定义列表
    getList () {
      this.loading.list = true
      this.$api.workflow.definitionList(this.searchForm).then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    // 表单操作按钮
    tableClick (row, tab) {
      switch (tab) {
        case 'versionManage':
          this.$router.push({
            name: 'configManageProcessDefinitionVersionManageList',
            query: { defKey: row.procKey }
          })
          break
        case 'del':
          this.passDialog.visible = true
          this.passDialog.title = '删除流程'
          this.passDialog.sureWord = '此流程删除'
          this.passDialog.word = '删除'
          this.passDialog.childSlot = 'disabled'
          this.submitForm = {
            appName: row.appName,
            procName: row.procName,
            procKey: row.procKey,
            pid: row.pid
          }
          break
      }
    },
    submitDelDia () {
      this.$api.workflow.deleteDefinition(this.submitForm.pid).then(result => {
        this.$message.success(result.data.msg || '操作成功')
        this.search()
      }).finally(() => {
      })
      this.passDialog.visible = false
    },
    closeDelDia () {
      this.passDialog.visible = false
    },
    // 新增流程
    addPartition () {
      this.dialog.partitionVisible = true
    },
    // 提交流程
    submitPartition () {
      this.$refs.partitionForm.validate((valid) => {
        if (valid) {
          this.$api.workflow.addDefinition(this.partitionForm).then(result => {
            this.$message.success(result.data.msg || '操作成功')
            this.search()
          }).finally(() => {
          })
          this.dialog.partitionVisible = false
        }
      })
    },
    closePartition () {
      this.dialog.partitionVisible = false
      this.$refs.partitionForm.resetFields()
    }
    //
  }
}
</script>

<style>

</style>
