<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="流程标题">
            <el-input v-model="searchForm.instName" placeholder="请输入流程标题" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="发起人">
            <el-input v-model="searchForm.startUserName" placeholder="请输入发起人" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="流程状态">
            <el-select v-model="searchForm.status">
              <el-option label="全部" value="" />
            </el-select>
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="流程跟踪列表" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe @change-page-num="changePageNum">
        <el-table-column label="序号" type="index" />
        <el-table-column prop="instName" label="流程任务标题" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="status" label="流程状态" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="startUserName" label="发起人" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="instStartTime" label="发起时间" :formatter="$utils.tableDateTimeFormat" show-overflow-tooltip />
        <el-table-column prop="instEndTime" label="完成时间" :formatter="$utils.tableDateTimeFormat" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="250">
          <template #default="scope">
            <el-link type="primary" @click="goDetail(scope.row)">
              查看
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
    </div>
  </el-main>
</template>
<script>
import Url from 'url-parse'
import { basePageListMixin } from '@fero/commons-vue'
import { get } from 'lodash'

export default {
  mixins: [basePageListMixin],
  data () {
    return {}
  },
  methods: {
    goDetail (row) {
      window.location.href = this.handlingPaths(row.instPageUrl, row, 'view')
    },
    /**
     * 处理跳转路径拼接参数
     * @param src 路径地址
     * @param params 要拼接的参数来源
     * @param type type view：审批进度,audit：审批操作，默认为view
     * @returns {string}
     */
    handlingPaths (src, params, type = 'view') {
      const url = new Url(src, true)
      // 大部分详情页都只要个pid,如果有特殊要根据自定义参数进行拼接
      url.query.pid = params.businessKey
      // customVariables：前端自定义变量拼接
      const variables = get(params, 'variables.customVariables', '')
      if (variables) {
        const customVariables = JSON.parse(variables)
        url.query = { ...url.query, ...customVariables }
      }
      if (type === 'audit') {
        // 审批所需的任务id
        url.query.taskId = params.pid
      }
      // 流程定义KEY：为了区分前置后置审批流和正常审批流procKey
      url.query.procKey = params.procKey
      return url.toString()
    }
  }
}
</script>
<style lang="scss"></style>
