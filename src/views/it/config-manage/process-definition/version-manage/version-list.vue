<template>
  <el-main>
    <page-title :show-back-btn="$route.query.noBack === 'noBack' ? false : true" />
    <div class="partition-area">
      <search-inner @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="版本号">
            <el-input v-model="searchForm.version" placeholder="请输入版本号" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态">
            <el-select v-model="searchForm.state" placeholder="请选择状态">
              <el-option v-for="item in $dictStatus.PROCESS_DEF_STATUS" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="版本管理" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe @change-page-num="changePageNum">
        <template #btn-inner>
          <el-button type="primary" @click="addPartition">
            新增版本
          </el-button>
        </template>
        <el-table-column label="序号" type="index" />
        <el-table-column prop="appName" label="应用名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="procName" label="流程定义名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="procKey" label="流程定义编码" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="version" label="版本号" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="state" label="状态" :formatter="$utils.tableStatusFormat('PROCESS_DEF_STATUS', {valueTypeToString:true})" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="330">
          <template #default="scope">
            <!--<el-link type="primary" @click="tableClick(scope.row,'copy')">-->
            <!--  复制-->
            <!--</el-link>-->
            <!--<el-link v-if="scope.row.state ===2" type="success" @click="tableClick(scope.row,'active')">-->
            <!--  激活-->
            <!--</el-link>-->
            <div v-if="scope.row.state === 0" class="inline">
              <!--<el-link type="primary" @click="tableClick(scope.row,'modeManage')">-->
              <!--  模型管理-->
              <!--</el-link>-->
              <!--<el-link type="primary" @click="tableClick(scope.row,'rulesManage')">-->
              <!--  规则设置-->
              <!--</el-link>-->
              <el-link type="primary" @click="tableClick(scope.row,'processManage')">
                流程编辑
              </el-link>
              <el-link type="success" @click="tableClick(scope.row,'release')">
                发布
              </el-link>
              <!--<el-link type="danger" @click="tableClick(scope.row,'del')">-->
              <!--  删除-->
              <!--</el-link>-->
            </div>
            <el-link type="primary" @click="tableClick(scope.row,'processManageDetail')">
              查看设计
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <table-confirm v-if="handleDialog.visible" v-model="handleDialog.visible" :title="handleDialog.title" :sure-word="handleDialog.sureWord" :show-list="handleDialog.showList" :word="handleDialog.word" :submit-loading="loading.delSubmit" @submit="submitDelDia" @close="closeDelDia">
      <template v-if="handleDialog.childSlot === 'disabled'" #disabled>
        <el-form ref="submitForm" :model="submitForm" label-width="80px" label-suffix="：">
          <el-form-item label="应用名称">
            {{ $utils.isEffectiveCommon(submitForm.appName) }}
          </el-form-item>
          <el-form-item label="流程名称">
            {{ $utils.isEffectiveCommon(submitForm.procName) }}
          </el-form-item>
          <el-form-item label="流程编码">
            {{ $utils.isEffectiveCommon(submitForm.procKey) }}
          </el-form-item>
          <el-form-item label="版本号">
            {{ $utils.isEffectiveCommon(submitForm.version) }}
          </el-form-item>
        </el-form>
      </template>
      <template v-if="handleDialog.childSlot === 'confirm'" #confirm>
        <el-form ref="submitForm" :model="submitForm" :rules="submitFormRules" label-width="80px" label-suffix="：" @submit.prevent>
          <el-form-item label="应用名称">
            {{ $utils.isEffectiveCommon(submitForm.appName) }}
          </el-form-item>
          <el-form-item label="流程名称">
            {{ $utils.isEffectiveCommon(submitForm.procName) }}
          </el-form-item>
          <el-form-item label="流程编码">
            {{ $utils.isEffectiveCommon(submitForm.procKey) }}
          </el-form-item>
          <el-form-item prop="version" label="版本号">
            <el-input v-model="submitForm.version" placeholder="请输入版本号" />
          </el-form-item>
        </el-form>
      </template>
    </table-confirm>
    <!-- 规则设置 -->
    <dialog-inner v-model="dialog.rulesVisible" title="规则设置" :loading="loading.rules" width="50%" @submit="submitRules" @close="closeRules">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="rulesForm" :model="rulesForm" label-width="60px">
            <el-card class="box-card" header="通知成员">
              流程发布后，将提醒可见范围内的成员有新审批流程可用。
            </el-card>
            <el-card class="box-card mt20" header="自动审批">
              当同一个审批人重复审批同一单据时：
              <el-form-item label-width="0">
                <el-radio-group v-model="rulesForm.repeatType">
                  <el-radio v-for="item in $status.processRepeatType" :key="item.value" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-card>
            <el-card class="box-card mt20" header="异常处理">
              审批节点内成员离职、为空等情况的处理方式：
              <el-form-item label-width="0">
                <el-radio-group v-model="rulesForm.invalidType" @change="changeInvalidType(rulesForm.invalidType)">
                  <el-radio v-for="item in $status.processInvalidType" :key="item.value" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-button v-if="rulesForm.invalidType === 2" type="primary" @click="addMember">
                添加成员
              </el-button>
              <div v-if="chooseName" class="mt20">
                <el-tag>{{ chooseName }}</el-tag>
              </div>
            </el-card>
          </el-form>
        </div>
      </div>
    </dialog-inner>
    <!-- 添加成员 -->
    <person-choose v-if="dialog.personVisible" v-model="dialog.personVisible" :title="dialog.title" @submit="submitChooseDia" @close="closeChooseDia" />
    <model-edit-dialog ref="modelEdit" @confirm="confirmModeManage" />
  </el-main>
</template>

<script>
import tableConfirm from '@/components/table-click/confirm-dialog.vue'
import modelEditDialog from '@/components/model-edit-dialog/index.vue'
import personChoose from '@/components/person-choose/choose-dialog.vue'
import { basePageListMixin } from '@/assets/lib/mixins'
import ModelEditDialog from '@/components/model-edit-dialog/index.vue'
export default {
  components: { ModelEditDialog, tableConfirm, modelEditDialog, personChoose },
  mixins: [basePageListMixin],
  data () {
    return {
      searchForm: {
        procKey: '', // 流程编码
        version: '', // 版本号
        state: '' // 状态
      },
      procPreResultList: [], // 上一节点结果
      tableList: {},
      loading: {
        list: false,
        delSubmit: false,
        rules: false
      },
      handleDialog: {
        visible: false,
        title: '',
        sureWord: '',
        word: '',
        childSlot: '',
        submitLoading: false
      },
      dialog: {
        rulesVisible: false,
        modelVisible: false,
        personVisible: false,
        title: ''
      },
      submitForm: {
        appName: '',
        procName: '',
        procKey: '',
        versionCode: ''
      },
      rulesPid: '', // 规则pid
      chooseName: '', // 已选人员名称
      // 规则设置表单
      rulesForm: {
        repeatType: 1, // 自动审批
        invalidType: 1 // 异常处理
      },
      procKey: '', // 流程编码
      rules: {
        version: this.$rulesToolkit.createRules({ name: '版本号', required: true, range: { max: 50 } })
      },
      submitFormRules: {
        version: this.$rulesToolkit.createRules({ name: '版本号', required: true, range: { max: 50 } })
      }
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    // 获取版本详情
    getDetail () {
      this.loading.list = true
      this.$api.workflow.definitionDetailByKey(this.$route.query.defKey).then(result => {
        if (result.data.data) {
          this.searchForm.procKey = result.data.data.procKey
          this.searchForm.state = ''
          this.procKey = result.data.data.procKey
          this.submitForm.appName = result.data.data.appName
          this.submitForm.procName = result.data.data.procName
          this.submitForm.procKey = result.data.data.procKey
          this.getVersionList()
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    // 搜索
    search () {
      this.getVersionList()
    },
    // 清空
    clearSearch () {
      this.searchForm = {
        procKey: this.procKey,
        state: ''
      }
      this.getVersionList()
    },
    // 分页
    changePageNum (pageInfo) {
      console.log(pageInfo)
      this.searchForm.pageNo = pageInfo.pageNum
      this.getVersionList()
    },
    // 获取工作流版本列表
    getVersionList () {
      if (this.searchForm.pid) delete this.searchForm.pid
      this.loading.list = true
      this.$api.workflow.versionList(this.searchForm).then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    // 表单操作按钮
    tableClick (row, tab) {
      switch (tab) {
        // 复制
        case 'copy':
          this.handleDialog.visible = true
          this.handleDialog.title = '复制版本'
          this.handleDialog.sureWord = '复制此版本，包括模型管理、规则设置、流程编辑'
          this.handleDialog.word = '复制'
          this.handleDialog.childSlot = 'confirm'
          this.submitForm.version = ''
          this.submitForm.pid = row.pid
          break
        // 流程激活
        case 'active':
          this.$confirm('此操作将此流程激活，是否继续？', '确定').then(() => {
            this.$api.workflow.activeVersion(row.pid).then(result => {
              this.$message.success(result.data.msg || '操作成功')
              this.search()
            }).finally(() => {
            })
          })
          break
        // 模型管理
        case 'modeManage':
          this.$api.workflow.getFormLayoutData(row.pid).then(result => {
            this.$refs.modelEdit.init(row.pid, JSON.parse(result.data.data.formJson))
          })
          break
        // 规则设置
        case 'rulesManage':
          this.dialog.rulesVisible = true
          this.rulesPid = row.pid
          this.$api.workflow.getModel(row.pid).then(result => {
            if (result.data.data) {
              this.rulesForm = result.data.data
              this.chooseName = result.data.data.invalidAssigneeUserName
              if (this.rulesForm.invalidType === 1) {
                this.chooseName = ''
              }
            }
          }).finally(() => {
          })
          break
        // 流程编辑
        case 'processManage':
          this.$router.push({ name: 'configManageProcessDefinitionVersionManageProcessEdit', query: { pid: row.pid } })
          break
          // 流程查看
        case 'processManageDetail':
          this.$router.push({ name: 'configManageProcessDefinitionVersionManageProcessDetail', query: { pid: row.pid } })
          break
        // 发布
        case 'release':
          this.handleDialog.visible = true
          this.handleDialog.title = '发布版本'
          this.handleDialog.sureWord = '发布此版本'
          this.handleDialog.word = '发布'
          this.handleDialog.childSlot = 'disabled'
          this.submitForm.version = row.version
          this.submitForm.pid = row.pid
          break
        // 删除
        case 'del':
          this.handleDialog.visible = true
          this.handleDialog.title = '删除版本'
          this.handleDialog.sureWord = '删除此版本'
          this.handleDialog.word = '删除'
          this.handleDialog.childSlot = 'disabled'
          this.submitForm.version = row.version
          this.submitForm.pid = row.pid
          break
      }
    },
    // 新增，复制，发布，删除版本
    submitDelDia () {
      if (this.handleDialog.childSlot === 'confirm') {
        this.$refs.submitForm.validate((valid) => {
          if (valid) {
            // 新增版本
            if (this.handleDialog.title === '新增版本') {
              const param = {
                procKey: this.submitForm.procKey,
                version: this.submitForm.version
              }
              this.$api.workflow.addVersion(param).then(result => {
                this.dialog.partitionVisible = false
                this.$message.success(result.data.msg || '操作成功')
                this.search()
              }).finally(() => {
                this.handleDialog.visible = false
              })
            } else {
              // 复制版本
              this.$api.workflow.copyVersion(this.submitForm.pid, { version: this.submitForm.version }).then(result => {
                this.$message.success(result.data.msg || '操作成功')
                this.search()
              }).finally(() => {
                this.handleDialog.visible = false
              })
            }
          }
        })
      } else if (this.handleDialog.childSlot === 'disabled') {
        let api = null
        if (this.handleDialog.word === '删除') {
          // 删除版本
          api = this.$api.workflow.deleteVersion(this.submitForm.pid, { version: this.submitForm.pid })
        } else if (this.handleDialog.word === '发布') {
          api = this.$api.workflow.deployVersion(this.submitForm.pid)
        }
        api.then(result => {
          this.$message.success(result.data.msg || '操作成功')
          this.search()
        }).finally(() => {
          this.handleDialog.visible = false
        })
      }
    },
    closeDelDia () {
      this.handleDialog.visible = false
    },
    // 新增版本
    addPartition () {
      this.handleDialog.visible = true
      this.handleDialog.title = '新增版本'
      this.handleDialog.sureWord = ''
      this.handleDialog.word = ''
      this.handleDialog.childSlot = 'confirm'
      this.submitForm = {
        appName: this.submitForm.appName,
        procName: this.submitForm.procName,
        procKey: this.submitForm.procKey,
        version: ''
      }
    },
    // 添加成员
    addMember () {
      this.dialog.personVisible = true
      this.dialog.title = '添加成员'
    },
    // 获取选择成员
    submitChooseDia (val) {
      this.dialog.personVisible = false
      this.chooseName = val.name
      this.rulesForm.invalidAssigneeUserId = val.pid
    },
    closeChooseDia (val) {
      this.dialog.personVisible = false
    },
    // 规则设置提交
    submitRules () {
      this.dialog.rulesVisible = false
      delete this.rulesForm.invalidAssigneeUserName
      this.$api.workflow.saveModel(this.rulesPid, this.rulesForm).then(result => {
        this.$message.success(result.data.msg || '操作成功')
        this.search()
      }).finally(() => {
        this.dialog.rulesVisible = false
      })
    },
    closeRules () {
      this.dialog.rulesVisible = false
      this.chooseName = ''
    },
    /**
     * 异常处理改变审批节点处理方式
     */
    changeInvalidType (val) {
      if (val === 1) {
        this.chooseName = ''
      }
    },
    // 模型管理提交
    confirmModeManage (data) {
      console.log(JSON.stringify(data), '模型数据')
      this.$api.workflow.saveFormLayoutData(data.pid, { formJson: JSON.stringify(data.formLayout) }).then(result => {
        this.$message.success(result.data.msg || '操作成功')
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .inline {
    display: inline;
  }
</style>
