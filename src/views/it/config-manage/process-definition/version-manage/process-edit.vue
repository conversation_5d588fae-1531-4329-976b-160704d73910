<template>
  <el-main>
    <page-title show-back-btn>
      <debounce-button @click="cancel()">
        取消
      </debounce-button>
      <debounce-button :loading="loading.saveXml" type="primary" @click="saveXml">
        保存
      </debounce-button>
    </page-title>
    <div class="partition-area" v-loading="loading.detail">
      <process-editor ref="processEditor" :xml-data="xmlData" :process-data="processData" />
    </div>
  </el-main>
</template>

<script>
import ProcessEditor from '@/components/workflow-process/process-editor.vue'
export default {
  components: { ProcessEditor },
  data () {
    return {
      xmlData: '',
      // 流程所有信息
      processData: {},
      loading: {
        detail: false,
        saveXml: false
      }
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    /**
     * 获取详情
     * @param type cancel是取消重新获取xml并重新初始化bpmn编辑器
     */
    getDetail (type) {
      this.loading.detail = true
      this.$api.workflow.getWorkflowDetail({ id: this.$route.query.pid }).then(result => {
        this.xmlData = result.data.data.bpmnXml || ''
        this.processData = result.data.data
        if (type) this.$refs.processEditor.importXML()
      }).finally(() => {
        this.loading.detail = false
      })
    },
    /**
     * 取消
     */
    cancel () {
      this.getDetail('cancel')
    },
    /**
     * 保存
     */
    saveXml () {
      this.$refs.processEditor.getXml().then(result => {
        console.log(result, 'xml')
        this.loading.saveXml = true
        this.$api.workflow.saveWorkFlowDetail(this.$route.query.pid, result).then(body => {
          this.loading.saveXml = false
          this.$utils.resultBaseMessage(body)
          this.$router.back()
        })
      }).catch(() => {
        this.loading.saveXml = false
      })
    }
  }
}
</script>

<style scoped>
.partition-area{
  width: calc(100% - 48px);
  height: calc(100% - 48px);
}
</style>
