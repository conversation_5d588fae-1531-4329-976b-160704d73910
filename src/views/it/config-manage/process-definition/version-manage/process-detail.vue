<template>
  <el-main>
    <page-title show-back-btn />
    <div v-loading="loading.detail" class="partition-area">
      <process-detail ref="processDetail" :xml-data="xmlData" :process-data="processData" />
    </div>
  </el-main>
</template>

<script>
import ProcessDetail from '@/components/workflow-process/process-detail/detail.vue'
export default {
  components: { ProcessDetail },
  data () {
    return {
      xmlData: '',
      // 流程所有信息
      processData: {},
      loading: {
        detail: false,
        saveXml: false
      }
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    /**
     * 获取详情
     * @param type cancel是取消重新获取xml并重新初始化bpmn编辑器
     */
    getDetail (type) {
      this.loading.detail = true
      this.$api.workflow.getWorkflowDefinition(this.$route.query.pid).then(result => {
        this.loading.detail = false
        this.xmlData = result.data.data.bpmnXml || ''
        this.processData = result.data.data
        if (type) this.$refs.processDetail.importXML()
      }).finally(() => {
        this.loading.detail = false
      })
    }
  }
}
</script>

<style scoped>
.partition-area{
  width: calc(100% - 48px);
  height: calc(100% - 48px);
}
</style>
