<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="流程定义名称">
            <el-input v-model="searchForm.procName" placeholder="请输入流程定义名称" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="当前节点">
            <el-input v-model="searchForm.taskName" placeholder="请输入当前节点" />
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="任务管理" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe @change-page-num="changePageNum">
        <el-table-column label="序号" type="index" />
        <el-table-column prop="instName" label="标题" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="procName" label="流程名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="taskName" label="当前节点" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="startUserName" label="提审人" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="instStartTime" label="提审时间" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="250">
          <template #default="scope">
            <el-link type="primary" @click="tableClick(scope.row,'schedule')">
              查看
            </el-link>
            <el-link type="primary" @click="tableClick(scope.row,'transfer')">
              移交
            </el-link>
            <!--<el-popover :ref="`onpopover-${scope.$index}`" placement="top" :width="200" trigger="click">-->
            <!--  <template #reference>-->
            <!--    <el-link type="success">-->
            <!--      快速通过-->
            <!--    </el-link>-->
            <!--  </template>-->
            <!--  <p class="mb10">-->
            <!--    <el-icon style="vertical-align: middle" :size="20" color="#409EFC" class="mr5">-->
            <!--      <Warning />-->
            <!--    </el-icon>请确认此任务通过？-->
            <!--  </p>-->
            <!--  <div style="text-align: right; margin: 0">-->
            <!--    <el-button size="small" text @click="tableClick(scope,'cancelPass')">-->
            <!--      取消-->
            <!--    </el-button>-->
            <!--    <el-button size="small" type="primary" @click="tableClick(scope,'pass')">-->
            <!--      确认-->
            <!--    </el-button>-->
            <!--  </div>-->
            <!--</el-popover>-->
            <!--<el-popover :ref="`refuse-${scope.$index}`" placement="top" :width="400" trigger="click">-->
            <!--  <template #reference>-->
            <!--    <el-link type="danger">-->
            <!--      快速拒绝-->
            <!--    </el-link>-->
            <!--  </template>-->
            <!--  <p class="mb10">-->
            <!--    请确认拒绝此任务？-->
            <!--  </p>-->
            <!--  <el-form ref="reasonForm" :model="reasonForm" :rules="rules" label-width="0px">-->
            <!--    <el-form-item prop="comment">-->
            <!--      <el-input v-model.trim="reasonForm.comment" type="textarea" placeholder="请输入拒绝理由" :autosize="{ minRows: 4, maxRows: 6}" maxlength="1000" show-word-limit />-->
            <!--    </el-form-item>-->
            <!--  </el-form>-->
            <!--  <div style="text-align: right; margin: 0">-->
            <!--    <el-button size="small" text @click="tableClick(scope,'cancelRefuse')">-->
            <!--      取消-->
            <!--    </el-button>-->
            <!--    <el-button size="small" type="primary" @click="tableClick(scope,'refuse')">-->
            <!--      确认-->
            <!--    </el-button>-->
            <!--  </div>-->
            <!--</el-popover>-->
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <!-- <person-choose v-if="personDialog.visible" v-model="personDialog.visible" :title="personDialog.title" :submit-loading="loading.choose" @submit="submitChooseDia" @close="closeChooseDia" /> -->
    <choose-handler-dialog ref="chooseHandlerDialog" v-model="personDialog.visible" v-model:local-permissions-information-list="memberForm.selected" @submit="submitChooseDia" @close="closeChooseDia" />
  </el-main>
</template>

<script>
import Url from 'url-parse'
import personChoose from '@/components/person-choose/choose-dialog.vue'
import { basePageListMixin } from '@/assets/lib/mixins'
import ChooseHandlerDialog from '@/components/person-choose/choose-handler-dialog.vue'
import { get } from 'lodash'
export default {
  components: { ChooseHandlerDialog, personChoose },
  mixins: [basePageListMixin],
  data () {
    return {
      searchForm: {
        procName: '', // 流程名称
        taskName: '' // 当前节点
      },
      loading: {
        list: false,
        choose: false
      },
      memberForm: {
        selected: []
      },
      reasonForm: {
        comment: ''
      },
      personDialog: {
        visible: false,
        title: '',
        submitLoading: false
      },
      rowPid: '',
      rules: {
        comment: this.$rulesToolkit.createRules({ name: '拒绝理由', required: true, range: { max: 1000 } })
      }
    }
  },
  created () {
  },
  methods: {
    /**
     * @public 获取任务列表
     */
    getList () {
      this.loading.list = true
      this.$api.workflow.taskList(this.searchForm).then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    // 表单操作按钮
    tableClick (row, tab) {
      switch (tab) {
        // 审批进度
        case 'schedule':
          window.location.href = this.handlingPaths(row.instPageUrl, row)
          // this.$router.push({ name: 'configManageTaskManageApproval', query: { pid: row.instPid, approvalType: 'process' } })
          break
        // 任务移交
        case 'transfer':
          // this.personDialog.visible = true
          // this.personDialog.title = '任务移交'
          // this.rowPid = row.pid
          this.personDialog.visible = true
          this.$refs.chooseHandlerDialog.addPermissionsInformation()
          this.rowPid = row.pid
          break
        // 快速通过
        case 'pass':
          this.$refs[`onpopover-${row.$index}`].hide()
          this.$api.workflow.taskApprove(row.row.pid, { type: 'INTERFER' }).then(result => {
            this.$utils.resultBaseMessage(result)
            this.search()
          }).finally(() => {
          })
          break
        // 快速通过pop关闭
        case 'cancelPass':
          this.$refs[`onpopover-${row.$index}`].hide()
          break
        // 快速拒绝
        case 'refuse':
          this.$refs.reasonForm.validate((valid) => {
            if (valid) {
              const param = {
                comment: this.reasonForm.comment,
                type: 'INTERFER'
              }
              this.$api.workflow.taskReject(row.row.pid, param).then(result => {
                this.$utils.resultBaseMessage(result)
                this.search()
                this.$refs[`refuse-${row.$index}`].hide()
              }).finally(() => {
                this.$refs.reasonForm.resetFields()
                this.$refs[`refuse-${row.$index}`].hide()
              })
            }
          })
          break
        // 快速拒绝pop关闭
        case 'cancelRefuse':
          this.$refs.reasonForm.resetFields()
          this.$refs[`refuse-${row.$index}`].hide()
          break
      }
    },
    /**
     * 任务管理移交
     */
    submitChooseDia (value) {
      if (value.length > 1) {
        this.$message.error('转交人只能选择一个')
        return false
      }
      const userInfo = value[0]
      this.$api.workflow.processTransfer(this.rowPid, userInfo.pid).then(result => {
        this.$utils.resultBaseMessage(result)
        this.search()
        this.personDialog.visible = false
      }).finally(() => {
        this.personDialog.visible = false
      })
    },
    // 关闭移交
    closeChooseDia () {
      this.personDialog.visible = false
    },
    /**
     * 处理跳转路径拼接参数
     * @param src 路径地址
     * @param params 要拼接的参数来源
     * @param type type view：审批进度,audit：审批操作，默认为view
     * @returns {string}
     */
    handlingPaths (src, params, type = 'view') {
      const url = new Url(src, true)
      // 大部分详情页都只要个pid,如果有特殊要根据自定义参数进行拼接
      url.query.pid = params.businessKey
      // customVariables：前端自定义变量拼接
      const variables = get(params, 'variables.customVariables', '')
      if (variables) {
        const customVariables = JSON.parse(variables)
        url.query = { ...url.query, ...customVariables }
      }
      if (type === 'audit') {
        // 审批所需的任务id
        url.query.taskId = params.pid
      }
      // 流程定义KEY：为了区分前置后置审批流和正常审批流procKey
      url.query.procKey = params.procKey
      return url.toString()
    }
  }
}
</script>

<style>

</style>
