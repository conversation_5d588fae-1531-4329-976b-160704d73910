<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner :search-form="searchForm" code="" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="用户">
            <el-input v-model="searchForm.userName" placeholder="请输入用户" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="类型">
            <el-input v-model="searchForm.type" placeholder="请输入类型" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="登录时间">
            <el-date-picker
              v-model="searchOperateDate"
              type="datetimerange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="shortcuts"
              format="YYYY-MM-DD HH:mm:ss"
              :default-time="[new Date(1970, 1, 1, 0, 0, 0), new Date(1970, 1, 1, 23, 59, 59)]"
              value-format="x"
              placeholder="请选择登录时间"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" :table-data="tableList.records" stripe :page-num="tableList.current" :page-size="tableList.size"  :total="tableList.total" @change-page-num="changePageNum" @change-page-size="changePageSize">
        <el-table-column prop="type" label="类型" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="account" label="账号" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="userName" label="用户" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="platform" label="平台" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="loginTime" label="登录时间" :formatter="$utils.tableDateTimeFormat" show-overflow-tooltip />
        <el-table-column prop="method" label="登录方式" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="remoteIp" label="登录IP" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="remoteAddress" label="登录地址" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="os" label="系统" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="client" label="客户端" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column :fixed="'right'" label="操作" min-width="40">
          <template #default="scope">
            <el-link type="primary" @click="view(scope.row)">
              详情
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <dialog-inner v-model="dialog.detail" :show-submit="false" cancel-title="完成" title="日志详情" >
      <div class="detail-area mt30">
        <el-form label-width="100px" label-suffix="：">
          <div class="detail-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="类型">
                  <p>{{ $utils.isEffectiveCommon(logDetail.type) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="登录时间">
                  <p>{{ $utils.dateTimeFormat(logDetail.loginTime) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="登录平台">
                  <p>{{ $utils.isEffectiveCommon(logDetail.platform) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="登录方式">
                  <p>{{ $utils.isEffectiveCommon(logDetail.method) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户">
                  <p>{{ $utils.isEffectiveCommon(logDetail.userName) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="账号">
                  <p>{{ $utils.isEffectiveCommon(logDetail.account) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="登录IP">
                  <p>{{ $utils.isEffectiveCommon(logDetail.remoteIp) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="登录地址">
                  <p>{{ $utils.isEffectiveCommon(logDetail.remoteAddress) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="系统">
                  <p>{{ $utils.isEffectiveCommon(logDetail.os) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户端">
                  <p>{{ $utils.isEffectiveCommon(logDetail.client) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备">
                  <p>{{ $utils.isEffectiveCommon(logDetail.device) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="用户代理">
                  <p>{{ $utils.isEffectiveCommon(logDetail.userAgent) }}</p>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </dialog-inner>
  </el-main>
</template>
<script>
import { basePageListMixin } from '@/assets/lib/mixins'
import { utils } from '@/assets/lib/utils'
import { shortcuts } from '@/assets/lib/misc'

export default {
  mixins: [basePageListMixin],
  data () {
    return {
      shortcuts,
      /** @public */
      baseSearchForm: {
        userName: '',
        type: '',
        loginBeginTime: '',
        loginEndTime: ''
      },
      logDetail: {},
      dialog: {
        detail: false
      },
      loading: {
        list: false
      }
    }
  },
  computed: {
    searchOperateDate: utils.computedDate('loginBeginTime', 'loginEndTime')
  },
  methods: {
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.it.application.loginLog.pageList(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    view (row) {
      this.$api.it.application.loginLog.detail(row.pid).then(result => {
        this.logDetail = result.data.data
        this.dialog.detail = true
      })
    }
  }
}
</script>
<style lang="scss"></style>
