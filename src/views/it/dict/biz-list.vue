<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" code="" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="字典名称">
            <el-input v-model="searchForm.name" placeholder="请输入字典名称" />
          </el-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="数据字典列表" :table-data="tableList.records" stripe :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" @change-page-num="changePageNum">
        <el-table-column prop="code" label="字典编码" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="name" label="字典名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column :fixed="'right'" label="操作" min-width="60">
          <template #default="scope">
            <auth-manage code="">
              <el-link type="primary" @click="deploy(scope.row)">
                字典配置
              </el-link>
            </auth-manage>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <dialog-inner v-model="dialog.deploy" title="字典配置" cancel-title="完成" :show-submit="false">
      <div class="form-area">
        <div class="detail-inner">
          <el-form>
            <el-row :gutter="80">
              <el-col :span="8">
                <el-form-item label="字典编码">
                  <p>{{ $utils.isEffectiveCommon(deployForm.code) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="字典名称">
                  <p>{{ $utils.isEffectiveCommon(deployForm.name) }}</p>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <table-inner v-loading="loading.itemList" :table-data="deployList" title="字典项列表" :table-footer="false">
        <template #btn-inner>
          <el-button type="primary" size="small" @click="addItem()">
            新增
          </el-button>
        </template>
        <el-table-column prop="itemName" label="字典项名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="itemValue" label="字典项键值" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="sort" label="排序" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="enable" label="状态" :formatter="$utils.tableTrueFalseFormant({ true: '启用', false: '禁用' })" show-overflow-tooltip />
        <el-table-column label="操作" min-width="60">
          <template #default="scope">
            <el-link type="primary" @click="editItem(scope.row)">
              编辑
            </el-link>
            <el-link v-if="scope.row.type ==='BIZ'" type="danger" @click="delItem(scope.row)">
              删除
            </el-link>
            <el-link v-if="scope.row.type ==='REW'" type="danger" @click="delItem(scope.row)">
              还原
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
    </dialog-inner>
    <dialog-inner v-model="dialog.item" :loading="loading.submit" :title="`${itemForm.pid ? '编辑' : '新增'}字典项`" width="30%" @submit="confirmEditItem" @close="closeItemDialog">
      <div class="form-area">
        <div class="detail-inner">
          <el-form>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="字典编码">
                  <p>{{ $utils.isEffectiveCommon(deployForm.code) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字典名称">
                  <p>{{ $utils.isEffectiveCommon(deployForm.name) }}</p>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="form-inner">
          <el-form ref="itemForm" :model="itemForm" :rules="rules" label-suffix="：" label-width="120px">
            <el-form-item label="字典项名称" prop="itemName">
              <el-input v-model="itemForm.itemName" placeholder="请输入字典项名称" />
            </el-form-item>
            <el-form-item label="字典项键值" prop="itemValue">
              <el-input v-model="itemForm.itemValue" placeholder="请输入字典项键值" :disabled="itemForm.type && itemForm.type!=='BIZ'" />
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input-number v-model="itemForm.sort" :controls="false" :step="1" :step-strictly="true" :min="1" :max="99" placeholder="请输入字典项键值" />
            </el-form-item>
            <el-form-item label="状态" prop="enable">
              <el-radio-group v-model="itemForm.enable">
                <el-radio :value="true">
                  启用
                </el-radio>
                <el-radio :value="false">
                  禁用
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
  </fe-page-main>
</template>
<script>

import { basePageListMixin } from '@fero/commons-vue'
import { cloneDeep } from 'lodash'
import FePageMain from '@/components/base/fe-page-main.vue'

export default {
  components: { FePageMain },
  mixins: [basePageListMixin],
  data () {
    return {
      dialog: {
        deploy: false,
        item: false
      },
      deployForm: {},
      deployList: [],
      itemForm: {},
      rules: {
        itemName: this.$rulesToolkit.createRules({ name: '字典项名称', required: true, range: { max: 64 } }),
        itemValue: this.$rulesToolkit.createRules({ name: '字典项键值', required: true, range: { max: 64 } }),
        sort: this.$rulesToolkit.createRules({ name: '排序', required: true }),
        enable: this.$rulesToolkit.createRules({ name: '状态', required: true, trigger: 'change' })
      },
      loading: {
        itemList: false,
        submit: false
      }
    }
  },
  methods: {
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.it.application.bizDict.pageList(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    // changeApp () {
    //   this.search()
    // },
    deploy (row) {
      this.deployForm = cloneDeep(row)
      this.getItemList().then(() => {
        this.dialog.deploy = true
      })
    },
    getItemList () {
      return new Promise((resolve, reject) => {
        this.loading.itemList = true
        this.$api.it.application.bizDict.itemList({ code: this.deployForm.code }).then(result => {
          this.deployList = result.data.data
          resolve(result.data.data)
        }).catch(error => {
          reject(error)
        }).finally(() => {
          this.loading.itemList = false
        })
      })
    },
    addItem () {
      this.itemForm = {
        code: this.deployForm.code,
        name: this.deployForm.name,
        appCode: this.deployForm.appCode
      }
      this.dialog.item = true
    },
    editItem (row) {
      this.itemForm = cloneDeep(row)
      this.dialog.item = true
    },
    confirmEditItem () {
      this.$refs.itemForm.validate().then(() => {
        this.loading.submit = true
        let api = this.$api.it.application.bizDict.addItem
        if (this.itemForm.pid) {
          api = this.$api.it.application.bizDict.editItem
        }
        api(this.itemForm).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getItemList()
          this.dialog.item = false
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    delItem (row) {
      this.$confirm('确定删除该字典项?', '确认删除').then(() => {
        this.$api.it.application.bizDict.delItem(row.pid).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getItemList()
        })
      })
    },
    closeItemDialog () {
      this.$refs.itemForm.resetFields()
      this.itemForm = {}
    }
  }
}
</script>
<style lang="scss"></style>
