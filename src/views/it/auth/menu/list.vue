<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner :search-form="searchForm" code="" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="应用名称">
            <el-select v-model="searchForm.appId" placeholder="请选择应用名称" @change="changeApp">
              <el-option v-for="val in appList" :key="val.pid" :label="val.name" :value="val.pid" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="资源名称">
            <el-input v-model="searchForm.name" placeholder="请输入资源名称" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="资源编码">
            <el-input v-model="searchForm.code" placeholder="请输入资源编码" />
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item label="显示权限节点">
            <el-switch v-model="searchForm.showPermissionNode" active-value="ON" inactive-value="OFF" @change="getList()" />
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-form-item label="展开全部节点">
            <el-switch v-model="searchForm.expandAll" active-value="ON" inactive-value="OFF" @change="handleAllRowKeys()" />
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="菜单列表" stripe :table-data="menuList" :table-footer="false" :expand-row-keys="expandRowKeys" row-key="pid" :show-drag-column="false" :tree-props="{ children: 'children', hasChildren: 'load-all-not-required' }">
        <template #btn-inner>
          <auth-manage code="">
            <el-button type="primary" @click="openAddMenuDialog()">
              新建
            </el-button>
          </auth-manage>
        </template>
        <el-table-column prop="name" label="资源名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="code" label="资源编码" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="enable" label="资源状态" :formatter="$utils.tableTrueFalseFormant({ true: '显示', false: '隐藏' })" show-overflow-tooltip />
        <el-table-column prop="appName" label="应用名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="pageInfo.url" label="路由地址" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="sort" label="排序" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" :formatter="$utils.tableStatusFormat('resourceType')" show-overflow-tooltip />
        <el-table-column :fixed="'right'" label="操作" min-width="150">
          <template #default="scope">
            <el-link :disabled="scope.row.type === 'AUTH'" type="primary" @click="addMenu(scope.row)">
              新建子项
            </el-link>
            <el-link type="primary" @click="editMenu(scope.row)">
              编辑
            </el-link>
            <el-link type="primary" @click="menuDetail(scope.row)">
              详情
            </el-link>
            <el-link type="danger" @click="remove(scope.row)">
              删除
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <!-- 菜单弹层-->
    <dialog-inner v-model="dialog.edit.visible" :title="dialog.edit.title" :loading="loading.submit" @submit="submitMenu" @close="closeDialog">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="menuForm" :model="menuForm" :rules="rules" label-suffix="：" label-width="120px">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="应用名称" prop="appId">
                  <el-select v-model="menuForm.appId" placeholder="请选择应用名称" @change="changeAppCode">
                    <el-option v-for="item in appList" :key="item.pid" :label="item.name" :value="item.pid" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上级菜单">
                  <el-cascader v-model="menuForm.parentId" :options="resourceList" :props="resourceProps" @change="changeParentMenu" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资源名称" prop="name">
                  <el-input v-model="menuForm.name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资源编码" prop="code">
                  <el-input v-model="menuForm.code" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资源类型" prop="resourceType">
                  <el-radio-group v-model="menuForm.resourceType" @change="changeResourceType">
                    <el-radio-button v-for="item in $utils.getEnableDictStatus('resourceType')" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="sort" label="排序">
                  <el-input-number v-model="menuForm.sort" v-bind="inputNumberIntAttr" placeholder="请输入排序" />
                </el-form-item>
              </el-col>
              <el-col v-if="['GROUP'].includes(menuForm.resourceType)" :span="12">
                <el-form-item label="资源图标" prop="icon">
                  <el-input v-model="menuForm.icon" />
                </el-form-item>
              </el-col>
              <el-col v-if="['AUTH'].includes(menuForm.resourceType)" :span="12">
                <el-form-item label="权限标识" prop="permission">
                  <el-input v-model="menuForm.permission" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="enable" label="资源状态">
                  <el-radio-group v-model="menuForm.enable">
                    <el-radio :value="1">
                      显示
                    </el-radio>
                    <el-radio :label="0">
                      隐藏
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col v-if="['MENU'].includes(menuForm.resourceType)" :span="12">
                <el-form-item prop="openType" label="打开方式">
                  <el-radio-group v-model="menuForm.openType">
                    <el-radio v-for="item in $utils.getEnableDictStatus('openType')" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col v-if="['MENU'].includes(menuForm.resourceType)" :span="24">
                <el-form-item label="路由地址" prop="url">
                  <el-input v-model="menuForm.url" />
                </el-form-item>
              </el-col>
              <el-col v-if="['AUTH'].includes(menuForm.resourceType)" :span="24">
                <el-form-item label="API权限" prop="" class="item-tag-list">
                  <el-tag
                    v-for="tag in menuForm.apiList"
                    :key="tag"
                    closable
                    size="large"
                    @close="removeApi(tag)"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-button @click="addApi">
                    新增API
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </dialog-inner>
    <dialog-inner v-model="dialog.api.visible" title="接口授权" @submit="confirmAuthApi" @close="closeApiDialog">
      <table-inner :table-data="unauthorizedApiList" :table-footer="false" title="未受控接口" @current-change="addAuthApi">
        <el-table-column prop="serviceId" label="服务名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="name" label="接口资源名" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="method" label="类型" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="url" label="接口路径" :formatter="$utils.isEffective" show-overflow-tooltip />
      </table-inner>
      <table-inner :table-data="authorizedApiList" :table-footer="false" title="已授权接口" @current-change="removeAuthApi">
        <el-table-column prop="serviceId" label="服务名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="name" label="接口资源名" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="method" label="类型" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="url" label="接口路径" :formatter="$utils.isEffective" show-overflow-tooltip />
      </table-inner>
    </dialog-inner>
    <dialog-inner v-model="dialog.detail.visible" :show-submit="false" cancel-title="关闭" title="菜单详情">
      <div class="form-area">
        <div class="detail-inner">
          <el-form ref="menuForm" label-suffix="：" label-width="120px">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="应用名称" prop="appId">
                  <p>{{ $utils.isEffectiveCommon(menuForm.appName) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上级菜单">
                  <p>{{ $utils.isEffectiveCommon(menuForm.parentName) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资源名称" prop="name">
                  <p>{{ $utils.isEffectiveCommon(menuForm.name) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资源编码" prop="code">
                  <p>{{ $utils.isEffectiveCommon(menuForm.code) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资源类型" prop="resourceType">
                  <p>{{ $utils.statusFormat(menuForm.resourceType, 'resourceType') }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="sort" label="排序">
                  <p>{{ $utils.isEffectiveCommon(menuForm.sort) }}</p>
                </el-form-item>
              </el-col>
              <el-col v-if="['GROUP'].includes(menuForm.resourceType)" :span="12">
                <el-form-item label="资源图标" prop="icon">
                  <p>{{ $utils.isEffectiveCommon(menuForm.icon) }}</p>
                </el-form-item>
              </el-col>
              <el-col v-if="['AUTH'].includes(menuForm.resourceType)" :span="12">
                <el-form-item label="权限标识" prop="permission">
                  <p>{{ $utils.isEffectiveCommon(menuForm.permission) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="enable" label="资源状态">
                  <p>{{ $utils.trueFalseFormant(menuForm.enable, { true: '显示', false: '隐藏' }) }}</p>
                </el-form-item>
              </el-col>
              <el-col v-if="['MENU'].includes(menuForm.resourceType)" :span="12">
                <el-form-item prop="openType" label="打开方式">
                  <p>{{ $utils.statusFormat(menuForm.openType, 'openType') }}</p>
                </el-form-item>
              </el-col>
              <el-col v-if="['MENU'].includes(menuForm.resourceType)" :span="24">
                <el-form-item label="路由地址" prop="url">
                  <p>{{ $utils.isEffectiveCommon(menuForm.url) }}</p>
                </el-form-item>
              </el-col>
              <el-col v-if="['AUTH'].includes(menuForm.resourceType)" :span="24">
                <el-form-item label="API权限" prop="" class="item-tag-list">
                  <el-tag
                    v-for="tag in menuForm.apiList"
                    :key="tag"
                    size="large"
                  >
                    {{ tag.name }}
                  </el-tag>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </dialog-inner>
  </el-main>
</template>

<script>
import { basePageListMixin } from '@fero/commons-vue'
import { getAppListMixin } from '@/assets/lib/mixins'
import { inputNumberIntAttr } from '@/assets/lib/misc'
import { cloneDeep, find, findIndex, get } from 'lodash'
class MenuAttr {
  enable = 1
  appId
  appName
  parentName
  functionId
  parentId = ''
  sort
  resourceIds = []
  pageInfo
  openType
  url
  parentType = ''
  resourceType
  apiList = []

  constructor (data = {}) {
    Object.assign(this, data)
    this.resourceType = data.type
  }
}
export default {
  components: {},
  mixins: [basePageListMixin, getAppListMixin],
  data () {
    return {
      autoAddDefaultAuth: true,
      inputNumberIntAttr,
      createdInit: false,
      /** @public */
      baseSearchForm: {
        // 应用名称
        appId: '',
        // 资源编码
        code: '',
        // 资源名称
        name: '',
        showPermissionNode: 'OFF',
        expandAll: 'OFF'
      },
      expandRowKeys: [],
      // 菜单
      menuList: [],
      // 菜单
      menuForm: new MenuAttr(),
      resourceList: [],
      rules: {
        appId: this.$rulesToolkit.createRules({ name: '应用名称', required: true, trigger: 'change' }),
        name: this.$rulesToolkit.createRules({ name: '资源名称', required: true, range: { max: 64 } }),
        code: this.$rulesToolkit.createRules({ name: '资源编码', required: true, pattern: this.$constants.patternEnglishNumber, range: { max: 64 } }),
        resourceType: this.$rulesToolkit.createRules({ name: '资源类型', required: true, trigger: 'change' }),
        sort: this.$rulesToolkit.createRules({ name: '排序', required: true }),
        enable: this.$rulesToolkit.createRules({ name: '资源状态', required: true }),
        openType: this.$rulesToolkit.createRules({ name: '打开方式', required: true }),
        icon: this.$rulesToolkit.createRules({ name: '资源图标', range: { max: 64 } }),
        permission: this.$rulesToolkit.createRules({ name: '权限标识', required: true, range: { max: 64 } }),
        url: this.$rulesToolkit.createRules({ name: '路由地址', required: true, range: { max: 256 } })
      },
      dialog: {
        edit: {
          title: '新建菜单',
          visible: false
        },
        detail: {
          visible: false
        },
        api: {
          visible: false
        }
      },
      resourceProps: {
        checkStrictly: true,
        emitPath: false,
        value: 'pid',
        label: 'name'
      },
      apiList: [],
      unauthorizedApiList: [],
      authorizedApiList: [],
      // apiSearchForm: {},
      loading: {
        submit: false
      }
    }
  },
  created () {
    this.getApiList()
  },
  methods: {
    handleAllRowKeys () {
      if (this.searchForm.expandAll === 'ON') {
        const extractPid = (data) => {
          const pidList = []

          function traverse (item) {
            // 如果 item 中有 pid 属性，将其添加到数组中
            if (item.pid !== undefined) {
              pidList.push(item.pid)
            }
            // 如果有子元素，递归遍历每个子元素
            if (item.children) {
              item.children.forEach(child => traverse(child))
            }
          }

          // 开始遍历数据
          data.forEach(item => traverse(item))
          return pidList
        }
        this.expandRowKeys = extractPid(this.menuList)
      } else {
        this.expandRowKeys = []
      }
    },
    getAppListSuccess (appList) {
      if (appList.length > 0) {
        if (!this.searchForm.appId) this.searchForm.appId = appList[0].pid
        this.recoverSearch()
      }
    },
    openAddMenuDialog () {
      const sort = this.menuList.length + 1
      this.menuForm = new MenuAttr({ appId: this.searchForm.appId, sort })
      this.dialog.edit.visible = true
    },
    getApiList () {
      this.$api.it.application.apiResource.allList().then(result => {
        this.apiList = result.data.data || []
      })
    },
    addAuthApi (row) {
      if (row) {
        this.authorizedApiList.push(row)
        const delIndex = findIndex(this.unauthorizedApiList, { pid: row.pid })
        this.unauthorizedApiList.splice(delIndex, 1)
      }
    },
    removeAuthApi (row) {
      if (row) {
        this.unauthorizedApiList.push(row)
        const delIndex = findIndex(this.authorizedApiList, { pid: row.pid })
        this.authorizedApiList.splice(delIndex, 1)
      }
    },
    confirmAuthApi () {
      this.menuForm.apiList = cloneDeep(this.authorizedApiList)
      this.dialog.api.visible = false
    },
    removeApi (api) {
      const delIndex = findIndex(this.menuForm.apiList, { pid: api.pid })
      this.menuForm.apiList.splice(delIndex, 1)
    },
    addApi () {
      this.apiList.forEach(item => {
        if (find(this.menuForm.apiList, { pid: item.pid })) {
          this.authorizedApiList.push(item)
        } else {
          this.unauthorizedApiList.push(item)
        }
      })
      this.dialog.api.visible = true
    },
    changeApp () {
      this.search()
    },
    /** @public 菜单列表 */
    getList () {
      this.loading.list = true
      this.$api.it.application.menu.menuPageTree(this.searchForm).then(result => {
        const resultData = result.data.data || []
        const handleItem = (menu) => {
          menu.forEach(item => {
            if (item.appItemList) {
              item.appItemList.forEach(auth => {
                auth.type = 'AUTH'
                auth.appName = item.appName
                auth.parentId = item.pid
                auth.parentPath = `${item.parentPath}|${item.pid}`
              })
              item.children = item.appItemList
            } else {
              if (item.children) {
                handleItem(item.children)
              }
            }
          })
        }
        if (this.searchForm.showPermissionNode !== 'OFF') {
          handleItem(resultData)
        }
        this.menuList = resultData
      }).finally(() => {
        this.loading.list = false
      })
    },
    // 删除菜单
    remove (row) {
      this.$confirm('确认要删除该菜单吗？', '删除').then(() => {
        const apiType = {
          AUTH: this.$api.it.application.menu.delAuth,
          MENU: this.$api.it.application.menu.delPage,
          GROUP: this.$api.it.application.menu.delGroup
        }
        const pidPath = {
          AUTH: 'pid',
          MENU: 'pageInfo.pid',
          GROUP: 'pid'
        }
        apiType[row.type](get(row, pidPath[row.type])).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        }).finally(() => {
        })
      })
    },
    // 新建子项
    addMenu (data) {
      this.changeAppCode(data.appId).then(() => {
        this.changeParentMenu(data.pid)
      })
      let sort = 1
      if (data.children) {
        sort = data.children.length + 1
      }
      this.menuForm = new MenuAttr({ appId: data.appId, parentId: data.pid, functionId: data.pid, parentType: data.type, sort })
      this.dialog.edit.visible = true
    },
    changeAppCode (val) {
      return new Promise((resolve, reject) => {
        this.$api.it.application.menu.menuPageTree({ appId: val }).then(result => {
          this.resourceList = result.data.data || []
          resolve(result.data.data)
        }).catch(e => {
          reject(e)
        })
      })
    },
    // 编辑菜单
    editMenu (data) {
      this.changeAppCode(data.appId).then(() => {
        this.changeParentMenu(data.parentId)
      })
      let formData = data
      if (data.type === 'MENU') {
        formData = data.pageInfo
        formData.type = data.type
        formData.parentId = data.parentId
        formData.sort = data.sort
      }
      if (data.type === 'AUTH') {
        const apiList = []
        if (data.resourceList && data.resourceList.length > 0) {
          data.resourceList.forEach(item => {
            apiList.push({
              pid: item.resourceId,
              name: item.resourceName
            })
          })
        }
        formData.apiList = apiList
      }
      this.menuForm = new MenuAttr(formData)
      this.dialog.edit.title = '编辑菜单'
      this.dialog.edit.visible = true
    },
    // 查看菜单详情
    menuDetail (data) {
      let formData = cloneDeep(data)
      if (data.type === 'MENU') {
        formData = Object.assign(formData, data.pageInfo)
        formData.type = data.type
        formData.parentId = data.parentId
        formData.sort = data.sort
      }
      if (data.type === 'AUTH') {
        const apiList = []
        if (data.resourceList && data.resourceList.length > 0) {
          data.resourceList.forEach(item => {
            apiList.push({
              pid: item.resourceId,
              name: item.resourceName
            })
          })
        }
        formData.apiList = apiList
      }
      const parentIds = data.parentPath.split('|')
      let parentName = ''
      let parentItem = this.menuList
      for (const parentId of parentIds) {
        if (parentId !== '-1') {
          parentItem = find(parentItem, { pid: parentId })
          parentName += parentItem.name + ' '
          parentItem = parentItem.children
          console.log(parentItem, 'parentItem')
        }
      }
      formData.parentName = parentName
      this.menuForm = new MenuAttr(formData)
      this.dialog.detail.visible = true
    },
    // 提交菜单
    submitMenu () {
      this.$refs.menuForm.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.menuForm)
        console.log(formData, 'submit data')
        const resourceApi = {
          GROUP: {
            add: this.$api.it.application.menu.addGroup,
            edit: this.$api.it.application.menu.editGroup
          },
          MENU: {
            add: this.$api.it.application.menu.addPage,
            edit: this.$api.it.application.menu.editPage
          },
          AUTH: {
            add: this.$api.it.application.menu.addAuth,
            edit: this.$api.it.application.menu.editAuth
          }
        }
        const method = formData.pid ? 'edit' : 'add'
        delete formData.children
        delete formData.appItemList
        delete formData.pageInfo
        if (formData.apiList) {
          const ids = []
          formData.apiList.forEach(item => {
            ids.push(item.pid)
          })
          formData.resourceIds = ids
        }
        resourceApi[formData.resourceType][method](formData).then(result => {
          this.$utils.resultBaseMessage(result)
          if (formData.resourceType === 'MENU' && method === 'add' && this.autoAddDefaultAuth) {
            // 自动创建默认权限
            const authData = {
              appId: formData.addId,
              code: formData.code + 'View',
              enable: 1,
              functionId: result.data.data,
              name: '查看权限',
              parentId: result.data.data,
              parentType: 'MENU',
              permission: formData.code + 'View',
              sort: 1
            }
            resourceApi.AUTH.add(authData).then(result => {
              this.$message.success('自动创建默认权限成功')
            }).catch(e => {
              this.$message.error('自动创建默认权限失败，请手动创建')
            }).finally(() => {
              this.getList()
            })
          } else {
            this.getList()
          }
          this.dialog.edit.visible = false
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    handleResourceTypeDisabled (item) {
      // 没有上级菜单类型 只有分组可选
      if (!this.menuForm.parentType && item.value !== 'GROUP') {
        return true
      } else if (this.menuForm.parentType === 'GROUP' && item.value !== 'MENU') {
        // 上级菜单类型是分组 只有分组可选
        return true
      } else if (this.menuForm.parentType === 'MENU' && item.value !== 'AUTH') {
        // 上级菜单类型是菜单 只有分组权限
        return true
      }
    },
    changeResourceType (value) {
      if (value === 'MENU') {
        this.menuForm.openType = 'ROUTE'
      } else {
        this.menuForm.openType = ''
      }
    },
    changeParentMenu (val) {
      const getMenu = () => {
        for (const item of this.resourceList) {
          if (item.pid === val) {
            return item
          } else if (item.children) {
            for (const sub of item.children) {
              if (sub.pid === val) {
                return sub
              } else if (sub.children) {
                for (const subSub of sub.children) {
                  if (subSub.pid === val) {
                    return subSub
                  }
                }
              }
            }
          }
        }
        return {}
      }
      const menuInfo = getMenu()
      this.menuForm.functionId = menuInfo.pid
      this.menuForm.parentType = menuInfo.type
    },
    // 关闭弹层
    closeDialog () {
      this.dialog.edit.title = '新建菜单'
      this.$refs.menuForm.resetFields()
      this.menuForm = new MenuAttr()
    },
    closeApiDialog () {
      this.unauthorizedApiList = []
      this.authorizedApiList = []
    }
  }
}
</script>

<style lang="scss">
</style>
