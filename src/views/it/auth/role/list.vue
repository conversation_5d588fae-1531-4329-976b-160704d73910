<template>
  <el-main>
    <page-title />
    <div class="tabs-inner">
      <el-tabs v-model="activeName" type="card" @tabChange="getList()">
        <el-tab-pane v-for="item in appList" :key="item.pid" :label="item.name" :name="item.pid" lazy>
          <search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
            <el-col :span="6">
              <el-form-item label="应用角色名称">
                <el-input v-model="searchForm.name" placeholder="请输入应用角色名称" />
              </el-form-item>
            </el-col>
          </search-inner>
          <table-inner v-loading="loading.list" title="租户列表" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe row-key="pid">
            <template #btn-inner>
              <slot name="btn-inner" />
              <el-button type="primary" size="small" @click="dialog.edit.visible = true">
                新建
              </el-button>
            </template>
            <el-table-column label="应用角色名称" prop="name" :formatter="$utils.isEffective" show-overflow-tooltip />
            <el-table-column label="描述" prop="description" :formatter="$utils.isEffective" show-overflow-tooltip />
            <el-table-column label="状态" prop="enable" :formatter="$utils.tableStatusFormat('disableEnable')" show-overflow-tooltip />
            <el-table-column fixed="right" label="操作">
              <template #default="scope">
                <el-link type="primary" @click="view(scope.row)">
                  详情
                </el-link>
                <el-link type="primary" @click="auth(scope.row)">
                  授权
                </el-link>
                <el-link type="primary" @click="edit(scope.row)">
                  编辑
                </el-link>
                <el-link v-if="scope.row.enable === 0" type="primary" @click="changeStatus(scope.row)">
                  启用
                </el-link>
                <el-link v-else-if="scope.row.enable === 1" type="danger" @click="changeStatus(scope.row)">
                  禁用
                </el-link>
                <el-link type="danger" @click="del(scope.row)">
                  删除
                </el-link>
              </template>
            </el-table-column>
          </table-inner>
        </el-tab-pane>
      </el-tabs>
    </div>
    <dialog-inner v-model="dialog.edit.visible" :title="dialog.edit.title" :loading="loading.submit" @submit="submitAppRole" @close="closeDialog">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="roleForm" :model="roleForm" :rules="rules" label-width="120px" label-suffix="：">
            <el-form-item prop="name" label="应用角色名称">
              <el-input v-model="roleForm.name" />
            </el-form-item>
            <el-form-item prop="description" label="角色描述">
              <el-input v-model="roleForm.description" />
            </el-form-item>
            <el-form-item prop="enable" label="状态">
              <el-radio-group v-model="roleForm.enable">
                <el-radio v-for="val in $utils.getEnableDictStatus('disableEnable')" :key="val.value" :value="Number(val.value)">
                  {{ val.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
    <dialog-inner v-model="dialog.auth.visible" :loading="loading.submit" title="授权" @submit="submitAppRoleAuth" @close="closeAuthDialog">
      <div class="form-area">
        <div class="form-inner">
          <el-tree
            ref="roleAuthTree"
            node-key="pid"
            :data="menuTreeList"
            :props="treeProps"
            :expand-on-click-node="false"
            default-expand-all
            show-checkbox
          />
        </div>
      </div>
    </dialog-inner>
    <dialog-inner v-model="dialog.detail.visible" :show-submit="false" cancel-title="完成" title="应用角色详情" @close="closeDetailDialog">
      <div class="form-area">
        <div class="area-title">
          <p class="title">
            应用角色信息
          </p>
        </div>
        <div class="detail-inner">
          <el-form ref="roleForm" label-width="120px" label-suffix="：">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="应用角色名称">
                  <p>{{ $utils.isEffectiveCommon(roleForm.name) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="描述">
                  <p>{{ $utils.isEffectiveCommon(roleForm.description) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态">
                  <p>{{ $utils.statusFormat(roleForm.enable, 'disableEnable') }}</p>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="area-title">
          <p class="title">
            授权
          </p>
        </div>
        <div class="detail-inner">
          <el-tree
            ref="roleAuthDetailTree"
            node-key="pid"
            :data="menuTreeList"
            :props="detailTreeProps"
            default-expand-all
            show-checkbox
          />
        </div>
      </div>
    </dialog-inner>
  </el-main>
</template>
<script>
import { cloneDeep, get } from 'lodash'
import { basePageListMixin } from '@fero/commons-vue'
export default {
  mixins: [basePageListMixin],
  data () {
    return {
      /** @public */
      createdInit: false,
      activeName: '',
      activeRoleId: '',
      appList: [],
      roleForm: {},
      rules: {
        name: this.$rulesToolkit.createRules({ name: '应用角色名称', required: true, range: { max: 64 } }),
        description: this.$rulesToolkit.createRules({ name: '描述', range: { max: 2000 } }),
        enable: this.$rulesToolkit.createRules({ name: '状态', required: true, trigger: 'change' })
      },
      menuTreeList: [],
      dialog: {
        edit: {
          title: '新增应用角色',
          visible: false
        },
        auth: {
          visible: false
        },
        detail: {
          visible: false
        }
      },
      treeProps: {
        label: 'name'
      },
      detailTreeProps: {
        label: 'name',
        disabled: 'pid'
      },
      loading: {
        submit: false
      }
    }
  },
  created () {
    this.getAppList()
  },
  methods: {
    // 获取应用
    getAppList () {
      this.$api.it.application.apps.allList().then(result => {
        this.appList = result.data.data || []
        this.activeName = get(this.appList, '[0].pid', '')
        this.getList()
      })
    },
    getMenuList () {
      return new Promise((resolve, reject) => {
        this.$api.it.application.menu.menuPageTree({ appId: this.activeName }).then(result => {
          this.menuTreeList = result.data.data || []
          resolve(result.data.data)
        }).catch(e => {
          reject(e)
        })
      })
    },
    getList () {
      this.searchForm.appId = this.activeName
      this.loading.list = true
      this.$api.it.application.role.pageList(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    auth (row) {
      this.activeRoleId = row.pid
      Promise.all([
        this.$api.it.application.role.detail(row.pid),
        this.getMenuList()
      ]).then(result => {
        this.dialog.auth.visible = true
        const checkKeys = []
        const authList = result[0].data.data || []
        authList.forEach(item => {
          if (item.level !== '0') {
            checkKeys.push(item.functionId)
          }
        })
        this.$nextTick(() => {
          this.$refs.roleAuthTree.setCheckedKeys(checkKeys)
        })
      })
    },
    view (row) {
      this.roleForm = cloneDeep(row)
      Promise.all([
        this.$api.it.application.role.detail(row.pid),
        this.getMenuList()
      ]).then(result => {
        this.dialog.detail.visible = true
        const checkKeys = []
        const authList = result[0].data.data || []
        authList.forEach(item => {
          checkKeys.push(item.functionId)
        })
        this.$nextTick(() => {
          this.$refs.roleAuthDetailTree.setCheckedKeys(checkKeys)
        })
      })
    },
    edit (row) {
      this.dialog.edit.title = '编辑角色应用'
      this.roleForm = cloneDeep(row)
      this.dialog.edit.visible = true
    },
    changeStatus (row) {
      const label = {
        1: '禁用',
        0: '启用'
      }
      const formData = {
        pid: row.pid,
        enable: Number(!row.enable)
      }
      this.$confirm(`确认${label[row.enable]}此应用角色？`, `确认${label[row.enable]}`).then(() => {
        this.$api.it.application.role.edit(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    del (row) {
      this.$confirm('确认删除此应用角色？', '确认删除').then(() => {
        this.$api.it.application.role.del(row.pid).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    submitAppRole () {
      this.$refs.roleForm.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.roleForm)
        formData.appId = this.activeName
        let api = this.$api.it.application.role.add
        if (formData.pid) {
          api = this.$api.it.application.role.edit
        }
        api(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
          this.dialog.edit.visible = false
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    submitAppRoleAuth () {
      const chooseNodes = this.$refs.roleAuthTree.getCheckedNodes(false, true)
      if (chooseNodes.length === 0) {
        this.$message.error('请选择要授权的功能')
        return false
      }
      this.loading.submit = true
      const chooseIds = []
      chooseNodes.forEach(item => {
        chooseIds.push(item.pid)
      })
      const formData = {
        appRoleId: this.activeRoleId,
        functionIds: chooseIds
      }
      this.$api.it.application.role.auth(formData).then(result => {
        this.$utils.resultBaseMessage(result)
        this.dialog.auth.visible = false
      }).finally(() => {
        this.loading.submit = false
      })
    },
    closeDialog () {
      this.dialog.edit.title = '新增角色应用'
      this.$refs.roleForm.resetFields()
      this.roleForm = {}
    },
    closeAuthDialog () {
      this.activeRoleId = ''
    },
    closeDetailDialog () {
      this.roleForm = {}
    }
  }
}
</script>
<style lang="scss"></style>
