<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner :search-form="searchForm" code="" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="应用编码">
            <el-input v-model="searchForm.code" placeholder="请输入应用编码" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="应用名称">
            <el-input v-model="searchForm.name" placeholder="请输入应用名称" />
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" :table-data="tableList.records" stripe :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" @changePageNum="changePageNum">
        <template #btn-inner>
          <auth-manage code="">
            <el-button type="primary" @click="addApp()">
              新建
            </el-button>
          </auth-manage>
        </template>
        <el-table-column prop="code" label="应用编码" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="name" label="应用名称" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column prop="appType" label="应用类型" :formatter="$utils.tableStatusFormat('appType')" show-overflow-tooltip />
        <el-table-column prop="sort" label="应用排序" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column :fixed="'right'" label="操作" width="370">
          <template #default="scope">
            <el-link type="primary" @click="editApp(scope.row)">
              编辑
            </el-link>
            <el-link type="primary" @click="appDetail(scope.row)">
              详情
            </el-link>
            <el-link type="danger" @click="remove(scope.row.pid)">
              删除
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <!-- 应用弹层-->
    <dialog-inner v-model="dialog.visible" :title="dialog.title" destroy-on-close :loading="loading.submit" :show-submit="!viewStatus" @submit="submitApp" @close="closeDialog">
      <div class="form-area">
        <div :class="viewStatus ? 'detail-inner' : 'form-inner'">
          <el-form ref="appForm" :model="appForm" :rules="rules" label-width="100px" :label-suffix="$constants.labelSuffix" :hide-required-asterisk="viewStatus">
            <el-form-item prop="code" label="应用编码">
              <el-input v-if="viewEdit()" v-model="appForm.code" placeholder="请输入应用编码" />
              <p v-else>
                {{ $utils.isEffectiveCommon(appForm.code) }}
              </p>
            </el-form-item>
            <el-form-item prop="name" label="应用名称">
              <el-input v-if="viewEdit()" v-model="appForm.name" placeholder="请输入应用名称" />
              <p v-else>
                {{ $utils.isEffectiveCommon(appForm.name) }}
              </p>
            </el-form-item>
            <el-form-item prop="appType" label="应用类型">
              <el-select v-if="viewEdit()" :disabled="appForm.pid" v-model="appForm.appType" placeholder="请选择">
                <el-option v-for="item in $status.appType" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
              <p v-else>
                {{ $utils.statusFormat(appForm.appType,'appType') }}
              </p>
            </el-form-item>
            <!--<el-form-item prop="topFlag" label="是否置顶">
              <el-select v-if="viewEdit()" v-model="appForm.topFlag" placeholder="请选择">
                <el-option v-for="item in $status.isCommon" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
              <p v-else>
                {{ $utils.statusFormat(appForm.topFlag,'isCommon') }}
              </p>
            </el-form-item>-->
            <!--<el-form-item prop="iconType" label="图标类型">
              <el-select v-if="viewEdit()" v-model="appForm.iconType" placeholder="请选择图标类型">
                <el-option v-for="item in $status.iconsType" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
              <p v-else>
                {{ $utils.statusFormat(appForm.iconType,'iconsType') }}
              </p>
            </el-form-item>
            <el-form-item v-if="appForm.iconType === '0'" prop="icon" label="图标">
              <fero-upload v-if="viewEdit()" ref="fileUpload" :file-list="appForm.fileList" name="object" :limit="1" accept="image/*" :on-exceed="onExceed" :action="uploadUrl" :headers="headers" :on-success="onSuccess" :on-remove="onRemove" :before-upload="beforeUploadFile" />
              <p v-else>
                <file-list :files="appForm.fileList" />
              </p>
            </el-form-item>
            <el-form-item v-if="appForm.iconType === '1'" prop="icon" label="图标">
              <el-input v-if="viewEdit()" v-model="appForm.icon" placeholder="请输入icons" />
              <p v-else>
                {{ $utils.isEffectiveCommon(appForm.icon) }}
              </p>
            </el-form-item>-->
            <el-form-item prop="url" label="访问地址">
              <el-input v-if="viewEdit()" v-model="appForm.url" placeholder="请输入访问地址" />
              <p v-else>
                {{ $utils.isEffectiveCommon(appForm.url) }}
              </p>
            </el-form-item>
            <el-form-item prop="sort" label="排序">
              <el-input v-if="viewEdit()" v-model="appForm.sort" placeholder="请输入排序" />
              <p v-else>
                {{ $utils.isEffectiveCommon(appForm.sort) }}
              </p>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
  </el-main>
</template>

<script>
import { basePageListMixin } from '@fero/commons-vue'
import { BaseObjectDataModel } from '@/assets/lib/data-model'
import { cloneDeep } from 'lodash'
import { cookie } from '@/assets/lib/commons'
class AppProp extends BaseObjectDataModel {
  // 应⽤code
  code = ''
  // 应⽤名称
  name = ''
  // ICONS类型
  iconType = '1'
  // ICONS
  icon = ''
  // 应用类型
  appType = ''
  // URL
  url = ''
  // 是否置顶
  topFlag = 0
  // 是否启用
  enable = 1
  fileList = []
}
export default {
  mixins: [basePageListMixin],
  data () {
    return {
      /** @public */
      baseSearchForm: {
        // 应⽤APP Code
        code: '',
        // 应⽤名称
        name: ''
      },
      // 创建应用
      appForm: new AppProp(),
      rules: {
        code: this.$rulesToolkit.createRules({ name: '应用编码', required: true, pattern: this.$constants.patternEnglishNumber, range: { max: 64 } }),
        name: this.$rulesToolkit.createRules({ name: '应用名称', required: true, range: { max: 64 } }),
        appType: this.$rulesToolkit.createRules({ name: '应用类型', required: true, trigger: 'change' }),
        iconType: this.$rulesToolkit.createRules({ name: '图标类型', required: true, trigger: 'change' }),
        url: this.$rulesToolkit.createRules({ name: '访问地址', required: true, range: { max: 128 } }),
        icon: this.$rulesToolkit.createRules({ name: '图标', required: true, trim: true }),
        sort: this.$rulesToolkit.createRules({ name: '排序', required: true, pattern: this.$constants.patternInteger.pattern })
      },
      uploadUrl: this.$utils.getUploadUrl('app/icon'),
      headers: {
        'X-token': `${cookie.get('token')}`
      },
      viewStatus: false,
      dialog: {
        title: '新建应用',
        visible: false
      }
    }
  },
  methods: {
    /** @public 拉取应用列表 */
    getList () {
      this.loading.list = true
      this.$api.it.application.apps.pageList(this.searchForm).then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    // 删除应用
    remove (appKey) {
      this.$confirm('确认要删除该应用吗？', '删除').then(() => {
        this.$api.it.application.apps.del(appKey).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    // 创建应用
    addApp () {
      this.dialog.title = '新建应用'
      this.dialog.visible = true
    },
    // 编辑应用
    editApp (data) {
      this.appForm.updateAllKeys(this.handleFileList(data))
      this.dialog.title = '编辑应用'
      this.dialog.visible = true
    },
    // 处理fileList
    handleFileList (data) {
      const formData = cloneDeep(data)
      // ICONS类型 iconType=0 图片 iconType=1 code
      if (formData.iconType === '0') {
        formData.fileList = this.$utils.singleFileToViewList({ filePath: formData.icon })
      }
      // console.log(formData)
      return formData
    },
    // 查看应用详情
    appDetail (data) {
      this.appForm.updateAllKeys(this.handleFileList(data))
      this.viewStatus = true
      this.dialog.visible = true
    },
    // 判断应用是编辑还是详情
    viewEdit () {
      return !this.viewStatus
    },
    // 提交应用
    submitApp () {
      this.$refs.appForm.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.appForm)

        let api = this.$api.it.application.apps.add
        if (formData.pid) {
          api = this.$api.it.application.apps.edit
        }
        api(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
          this.dialog.visible = false
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    // 上传成功
    onSuccess (response) {
      this.appForm.fileList = this.$utils.singleFileToViewList(response.data)
      this.appForm.icon = response.data.filePath
    },
    // 文件列表移除文件
    onRemove () {
      this.appForm.fileList = []
      this.appForm.icon = ''
    },
    // 上传文件之前的钩子
    beforeUploadFile (file) {
      if (file.type.indexOf('image') === -1) {
        this.$message.error('只能上传图片')
        return false
      }
    },
    // 超出最大上传数量1限制自动覆盖前一个文件
    onExceed (files) {
      // 不是图片不能覆盖前一个文件
      if (files[0].type.indexOf('image') === -1) {
        this.$message.error('只能上传图片')
        return false
      }
      this.$refs.fileUpload.$refs.upload.clearFiles()
      this.$refs.fileUpload.$refs.upload.handleStart(files[0])
      // 数据成功替换了 在提交
      this.$nextTick(() => {
        this.$refs.fileUpload.$refs.upload.submit()
      })
    },
    // 关闭弹层
    closeDialog () {
      this.appForm = new AppProp()
      this.$refs.appForm.resetFields()
      this.viewStatus = false
    }
  }
}
</script>

<style>

</style>
