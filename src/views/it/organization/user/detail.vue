<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <div v-loading="loading.detail" class="detail-area">
        <div class="area-title">
          <p class="title">
            用户信息
          </p>
        </div>
        <div class="detail-inner">
          <el-form label-width="120px" label-suffix="：">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="用户名称">
                  <p>{{ $utils.isEffectiveCommon(userForm.name) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机">
                  <p>{{ $utils.isEffectiveCommon(userForm.mobile) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别">
                  <p>{{ $utils.statusFormat(userForm.gender, 'gender') }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <p>{{ $utils.isEffectiveCommon(userForm.email) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态">
                  <p>{{ $utils.statusFormat(userForm.status, 'disableEnable') }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否开通账号">
                  <p>{{ $utils.trueFalseFormant(userForm.loginFlag) }}</p>
                </el-form-item>
              </el-col>
              <template v-if="userForm.loginFlag === 1">
                <el-col :span="12">
                  <el-form-item label="账号名称">
                    <p>{{ $utils.isEffectiveCommon(userForm.account) }}</p>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="有效期">
                    <p>{{ $utils.dateTimeFormat(userForm.validStartTime) }} - {{ $utils.dateTimeFormat(userForm.validEndTime) }}</p>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item prop="mobileFlag" label="开通手机登录">
                    <p>{{ $utils.trueFalseFormant(userForm.mobileFlag) }}</p>
                  </el-form-item>
                </el-col>
              </template>
            </el-row>
          </el-form>
        </div>
        <div class="table-title">
          <table-inner :table-data="userForm.userDeptList" :table-footer="false" title="部门信息">
            <el-table-column prop="deptName" label="所属部门" :formatter="$utils.isEffective" />
            <el-table-column prop="isLeader" label="是否为主管" :formatter="$utils.tableTrueFalseFormant()" />
            <el-table-column prop="isDefault" label="默认部门" :formatter="$utils.tableTrueFalseFormant()" />
          </table-inner>
        </div>
        <!--<div class="table-title">
          <table-inner :table-data="userForm.userPostList" :table-footer="false" title="岗位信息">
            <el-table-column prop="deptName" label="所属部门" :formatter="$utils.isEffective" />
            <el-table-column prop="postName" label="岗位" :formatter="$utils.isEffective" />
          </table-inner>
        </div>-->
        <div class="area-title">
          <p class="title">
            授权角色
          </p>
        </div>
        <div class="detail-inner">
          <el-form label-width="120px" label-suffix="：">
            <el-form-item label="按部门授权" class="role-item">
              <el-tooltip v-for="item in userForm.deptRoleList" :key="item.roleId" effect="dark" :content="`来源：${item.source}`" placement="top">
                <el-tag>
                  {{ item.roleName }}
                </el-tag>
              </el-tooltip>
            </el-form-item>
            <!--<el-form-item label="按岗位授权" class="role-item">
              <el-tooltip v-for="item in userForm.postRoleList" :key="item.roleId" effect="dark" :content="`来源：${item.source}`" placement="top">
                <el-tag>
                  {{ item.roleName }}
                </el-tag>
              </el-tooltip>
            </el-form-item>-->
            <el-form-item label="按用户授权" class="role-item">
              <el-tag v-for="item in userForm.userRoleList" :key="item.roleId">
                {{ item.roleName }}
              </el-tag>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div v-loading="loading.app" class="tabs-inner">
      <el-tabs v-model="activeName" type="card" @tabChange="getAuthDataList()">
        <el-tab-pane v-for="item in appList" :key="item.pid" :label="item.name" :name="item.pid" lazy>
          <div v-loading="loading.detail">
            <div class="area-title">
              <p class="title">
                功能授权
              </p>
            </div>
            <div class="detail-inner">
              <el-tree
                node-key="pid"
                :data="menuTreeList"
                :props="treeProps"
                default-expand-all
                show-checkbox
                :default-checked-keys="menuTreeCode"
              />
            </div>
            <!--<div class="area-title">
              <p class="title">
                数据授权
              </p>
            </div>
            <div class="detail-inner">
              <el-tree
                node-key="pid"
                :data="dataTreeList"
                :props="treeProps"
                default-expand-all
                show-checkbox
                :default-checked-keys="dataTreeCode"
              />
            </div>-->
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
import { UserProp } from '@/assets/lib/data-model'
import { get } from 'lodash'

export default {
  data () {
    return {
      pid: this.$route.query.id,
      userForm: new UserProp(),
      activeName: '',
      appList: [],
      menuTreeList: [],
      dataTreeList: [],
      dataTreeCode: [],
      menuTreeCode: [],
      treeProps: {
        label: 'name',
        class: this.setTreeItemClass,
        disabled: 'pid'
      },
      loading: {
        detail: false,
        app: false
      }
    }
  },
  created () {
    this.getDetail()
    this.getAppList()
  },
  methods: {
    getDetail () {
      this.loading.detail = true
      this.$api.it.organization.user.detail(this.pid).then(result => {
        this.userForm = new UserProp(result.data.data)
        const dataTreeCode = []
        if (this.userForm.userDataScopeList) {
          this.userForm.userDataScopeList.forEach(item => {
            dataTreeCode.push(item.datascopeId)
          })
        }
        this.dataTreeCode = dataTreeCode
        const menuTreeCode = []
        if (this.userForm.userItemList) {
          this.userForm.userItemList.forEach(item => {
            menuTreeCode.push(item.pid)
          })
        }
        this.menuTreeCode = menuTreeCode
      }).finally(() => {
        this.loading.detail = false
      })
    },
    // 获取应用
    getAppList () {
      this.loading.app = true
      this.$api.it.application.apps.allList().then(result => {
        this.appList = result.data.data || []
        this.activeName = get(this.appList, '[0].pid', '')
        this.getMenuList()
        this.getDataList()
      }).finally(() => {
        this.loading.app = false
      })
    },
    getAuthDataList () {
      this.loading.detail = true
      Promise.all([
        this.getMenuList(),
        this.getDataList()
      ]).finally(() => {
        this.loading.detail = false
      })
    },
    getMenuList () {
      return new Promise((resolve, reject) => {
        this.$api.it.application.menu.menuPageTree({ appId: this.activeName }).then(result => {
          const resultData = result.data.data || []
          resultData.forEach(item => {
            if (item.children) {
              item.children.forEach(sub => {
                if (sub.appItemList) {
                  sub.appItemList.forEach(auth => {
                    auth.type = 'AUTH'
                    auth.appName = sub.appName
                    auth.parentId = sub.pid
                  })
                  sub.children = sub.appItemList
                }
              })
            }
          })
          this.menuTreeList = resultData
          resolve(resultData)
        }).catch(e => {
          reject(e)
        })
      })
    },
    getDataList () {
      return new Promise((resolve, reject) => {
        this.$api.it.application.dataResource.treeList({ appId: this.activeName }).then(result => {
          const treeData = result.data.data || []
          treeData.forEach(item => {
            if (item.children) {
              item.children.forEach(sub => {
                if (sub.dataScopeList) {
                  sub.dataScopeList.forEach(auth => {
                    auth.type = 'DATA'
                    auth.parentId = sub.pid
                    auth.name = auth.scopeName
                  })
                  sub.children = sub.dataScopeList
                }
              })
            }
          })
          this.dataTreeList = result.data.data || []
          resolve(result.data.data)
        }).catch(e => {
          reject(e)
        })
      })
    },
    setTreeItemClass (data, node) {
      if (data.parentCode === '-1') {
        return 'next-inline-node'
      }
    }
  }
}
</script>
<style lang="scss">
.detail-inner .el-form-item.role-item {
  .el-form-item__label {
    line-height: 24px;
    height: 24px;
  }
  .el-tag {
    margin-right: 10px;
  }
}
</style>
