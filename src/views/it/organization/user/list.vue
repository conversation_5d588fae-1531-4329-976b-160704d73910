<template>
  <el-main>
    <page-title />
    <div class="partition-area user-area">
      <user-page-list change-router-status created-init-status />
    </div>
  </el-main>
</template>

<script>

import UserPageList from '@/components/it/user-page-list.vue'

export default {
  components: { UserPageList },
  mixins: [],
  data () {
    return {
    }
  },
  created () {
  },
  methods: {}
}
</script>

<style>
.user-area {
  background-color: transparent;
}
</style>
