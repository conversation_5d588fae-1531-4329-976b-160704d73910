<template>
  <el-main>
    <page-title show-back-btn />
    <div v-loading="loading.app" class="tabs-inner">
      <el-tabs v-model="activeName" type="card" @tabChange="getDataList()">
        <el-tab-pane v-for="item in appList" :key="item.pid" :label="item.name" :name="item.pid" lazy>
          <div v-loading="loading.data" class="m15">
            <el-tree
              :ref="`data-tree-${item.pid}`"
              node-key="pid"
              :data="dataTreeList"
              :props="treeProps"
              :expand-on-click-node="false"
              :default-checked-keys="checkedKeys"
              default-expand-all
              show-checkbox
            />
            <div class="tr">
              <el-button type="primary" @click="confirmDataAuth">
                确认
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>

import { get } from 'lodash'

export default {
  data () {
    return {
      id: this.$route.query.pid,
      activeName: '',
      appList: [],
      dataTreeList: [],
      checkedKeys: [],
      treeProps: {
        label: 'name'
      },
      loading: {
        app: false,
        data: false
      }
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    getDetail () {
      this.loading.app = true
      this.$api.it.organization.role.detail(this.id).then(result => {
        const checkedKeys = []
        if (result.data.data.roleDataScopeList) {
          result.data.data.roleDataScopeList.forEach(item => {
            checkedKeys.push(item.datascopeId)
          })
        }
        this.checkedKeys = checkedKeys
        this.getAppList()
      }).catch(e => {
        this.loading.app = false
      })
    },
    // 获取应用
    getAppList () {
      this.$api.it.organization.role.appList().then(result => {
        this.appList = result.data.data || []
        this.activeName = get(this.appList, '[0].pid', '')
        this.getDataList()
      }).finally(() => {
        this.loading.app = false
      })
    },
    getDataList () {
      this.loading.data = true
      this.$api.it.application.dataResource.treeList({ appId: this.activeName }).then(result => {
        const treeData = result.data.data || []
        treeData.forEach(item => {
          if (item.children) {
            item.children.forEach(sub => {
              if (sub.dataScopeList) {
                sub.dataScopeList.forEach(auth => {
                  auth.type = 'DATA'
                  auth.parentId = sub.pid
                  auth.name = auth.scopeName
                })
                sub.children = sub.dataScopeList
              }
            })
          }
        })
        this.dataTreeList = result.data.data || []
      }).finally(() => {
        this.loading.data = false
      })
    },
    confirmDataAuth () {
      const checkNodes = this.$refs[`data-tree-${this.activeName}`][0].getCheckedNodes()
      console.log(checkNodes)
      if (checkNodes.length === 0) {
        this.$message.error('请选择要添加的权限')
      }
      const datascopeIds = []
      checkNodes.forEach(item => {
        if (item.type === 'DATA') {
          datascopeIds.push(item.pid)
        }
      })
      const formData = {
        appId: this.activeName,
        roleId: this.id,
        datascopeIds
      }
      this.$api.it.organization.role.dataAuth(formData).then(result => {
        this.$utils.resultBaseMessage(result)
      })
    }
  }
}
</script>
<style lang="scss">
.next-inline-node {
  .el-tree-node__children {
    .el-tree-node {
      display: inline-block;
    }
  }
}
</style>
