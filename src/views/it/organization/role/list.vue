<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="应用名称">
            <el-select v-model="searchForm.appId" placeholder="请选择应用名称" @change="changeApp">
              <el-option v-for="val in appList" :key="val.pid" :label="val.name" :value="val.pid" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="角色名称">
            <el-input v-model="searchForm.name" placeholder="请输入角色名称" />
          </el-form-item>
        </el-col>
      </search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="角色列表" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe row-key="pid" @change-page-num="changePageNum" @change-page-size="changePageSize">
        <template #btn-inner>
          <auth-manage code="">
            <el-button type="primary" @click="dialog.edit.visible = true">
              新建
            </el-button>
          </auth-manage>
        </template>
        <el-table-column label="所属应用" prop="appName" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column label="角色名称" prop="name" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column label="角色编码" prop="code" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column label="角色描述" prop="description" :formatter="$utils.isEffective" show-overflow-tooltip />
        <el-table-column :fixed="'right'" label="操作">
          <template #default="scope">
            <table-link :to="{ name: 'organizationRoleDetail', query: { pid: scope.row.pid } }" type="primary">
              详情
            </table-link>
            <table-link :to="{ name: 'organizationRoleMenu', query: { pid: scope.row.pid } }" type="primary">
              功能权限
            </table-link>
            <!--<table-link :to="{ name: 'organizationRoleData', query: { pid: scope.row.pid } }" type="primary">-->
            <!--  数据权限-->
            <!--</table-link>-->
            <el-dropdown @command="code => handleCommand(code, scope.row)">
              <el-link type="primary">
                更多
              </el-link>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="edit">
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item command="del" class="danger">
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </table-inner>
    </div>
    <dialog-inner v-model="dialog.edit.visible" :title="dialog.edit.title" :loading="loading.submit" @submit="submitRole" @close="closeDialog">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="roleForm" v-auto-placeholder :model="roleForm" :rules="rules" label-suffix="：" label-width="120px">
            <el-form-item label="所属应用" prop="appId">
              <el-select v-model="roleForm.appId" placeholder="请选择所属应用">
                <el-option v-for="item in appList" :key="item.pid" :label="item.name" :value="item.pid" />
              </el-select>
            </el-form-item>
            <el-form-item prop="name" label="角色名称">
              <el-input v-model="roleForm.name" />
            </el-form-item>
            <el-form-item prop="code" label="角色编码">
              <el-input v-model="roleForm.code" />
            </el-form-item>
            <el-form-item prop="description" label="角色描述">
              <el-input v-model="roleForm.description" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
  </el-main>
</template>
<script>
import { basePageListMixin } from '@fero/commons-vue'
import { getAppListMixin } from '@/assets/lib/mixins'
import { cloneDeep } from 'lodash'

export default {
  mixins: [basePageListMixin, getAppListMixin],
  data () {
    return {
      appType: 'PLATFORM',
      roleForm: {},
      rules: {
        name: this.$rulesToolkit.createRules({ name: '角色名称', required: true, range: { max: 64 } }),
        code: this.$rulesToolkit.createRules({ name: '角色编码', required: true, range: { max: 64 } }),
        appId: this.$rulesToolkit.createRules({ name: '所属应用', required: true, trigger: 'change' }),
        description: this.$rulesToolkit.createRules({ name: '角色描述', range: { max: 200 } })
      },
      dialog: {
        edit: {
          title: '新建角色',
          visible: false
        }
      },
      loading: {
        submit: false
      }
    }
  },
  methods: {
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.it.organization.role.pageList(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    submitRole () {
      this.$refs.roleForm.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.roleForm)
        let api = this.$api.it.organization.role.add
        if (formData.pid) {
          api = this.$api.it.organization.role.edit
        }
        api(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
          this.dialog.edit.visible = false
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    handleCommand (code, row) {
      this[code](row)
    },
    edit (row) {
      this.roleForm = cloneDeep(row)
      this.dialog.edit.title = '编辑角色'
      this.dialog.edit.visible = true
    },
    del (row) {
      this.$confirm('确认删除此角色？', '确认删除').then(() => {
        this.$api.it.organization.role.del(row.pid).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    closeDialog () {
      this.dialog.edit.title = '新建角色'
      this.roleForm = {}
      this.$refs.roleForm.resetFields()
    }
  }
}
</script>
<style lang="scss"></style>
