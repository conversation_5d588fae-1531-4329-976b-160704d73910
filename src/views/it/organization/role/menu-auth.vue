<template>
  <el-main>
    <page-title show-back-btn />
    <!--    <div v-loading="loading.app" class="tabs-inner">-->
    <!--      <el-tabs v-model="activeName" type="card" @tabChange="getMenuList()">-->
    <!--        <el-tab-pane v-for="item in appList" :key="item.pid" :label="item.name" :name="item.pid" lazy>-->
    <!--        </el-tab-pane>-->
    <!--      </el-tabs>-->
    <!--    </div>-->
    <div class="partition-area">
      <div class="area-title">
        <p class="title">
          功能授权
        </p>
      </div>
      <div v-loading="loading.menu" class="detail-inner">
        <el-tree
          ref="menuTree"
          node-key="pid"
          :data="menuTreeList"
          :props="treeProps"
          :expand-on-click-node="false"
          :default-checked-keys="checkedKeys"
          default-expand-all
          show-checkbox
        />
        <div class="tr">
          <el-button type="primary" @click="confirmMEnuAuth">
            确认
          </el-button>
        </div>
      </div>
    </div>
  </el-main>
</template>
<script>

import { isEmpty } from 'lodash'

export default {
  data () {
    return {
      id: this.$route.query.pid,
      appId: '',
      // appList: [],
      menuTreeList: [],
      checkedKeys: [],
      treeProps: {
        label: 'name',
        class: this.setTreeItemClass
      },
      loading: {
        app: false,
        menu: false
      }
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    getDetail () {
      this.loading.menu = true
      this.$api.it.organization.role.detail(this.id).then(result => {
        const data = result.data.data || {}
        const checkedKeys = []
        if (!isEmpty(data.roleItemList)) {
          data.roleItemList.forEach(item => {
            checkedKeys.push(item.itemId)
          })
        }
        this.checkedKeys = checkedKeys
        this.appId = data.appId
        if (this.appId) this.getMenuList()
      }).finally(() => {
        this.loading.menu = false
      })
    },
    // 获取应用
    // getAppList () {
    //   this.$api.it.organization.role.appList().then(result => {
    //     this.appList = result.data.data || []
    //     this.activeName = get(this.appList, '[0].pid', '')
    //     this.getMenuList()
    //   }).finally(() => {
    //     this.loading.app = false
    //   })
    // },
    getMenuList () {
      this.loading.menu = true
      this.$api.it.organization.role.menuPageTree({ appId: this.appId }).then(result => {
        const resultData = result.data.data || []
        const handleItem = (menu) => {
          menu.forEach(item => {
            if (item.appItemList) {
              item.appItemList.forEach(auth => {
                auth.type = 'AUTH'
                auth.appName = item.appName
                auth.parentId = item.pid
                auth.parentPath = `${item.parentPath}|${item.pid}`
              })
              item.children = item.appItemList
            } else {
              if (item.children) {
                handleItem(item.children)
              }
            }
          })
        }
        handleItem(resultData)
        this.menuTreeList = resultData
      }).finally(() => {
        this.loading.menu = false
      })
    },
    confirmMEnuAuth () {
      const checkNodes = this.$refs.menuTree.getCheckedNodes(false, true)
      if (checkNodes.length === 0) {
        this.$message.error('请选择要添加的权限')
      }
      const itemIds = []
      const functionIds = []
      checkNodes.forEach(item => {
        if (item.type === 'AUTH') {
          itemIds.push(item.pid)
        } else {
          functionIds.push(item.pid)
        }
      })
      const formData = {
        appId: this.appId,
        roleId: this.id,
        itemIds,
        functionIds
      }
      this.$api.it.organization.role.menuAuth(formData).then(result => {
        this.$utils.resultBaseMessage(result)
      })
    },
    setTreeItemClass (data, node) {
      if (data.type === 'MENU') {
        return 'next-inline-node'
      }
    }
  }
}
</script>
<style lang="scss">
</style>
