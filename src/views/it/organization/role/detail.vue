<template>
  <el-main>
    <page-title />
    <div class="partition-area">
      <div v-loading="loading.detail" class="detail-area">
        <div class="area-title">
          <p class="title">
            角色信息
          </p>
        </div>
        <div class="detail-inner">
          <el-form label-width="120px" label-suffix="：">
            <el-row :gutter="80">
              <el-col :span="12">
                <el-form-item label="所属应用">
                  <p>{{ $utils.isEffectiveCommon(roleDetail.appName) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="角色名称">
                  <p>{{ $utils.isEffectiveCommon(roleDetail.name) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="角色编码">
                  <p>{{ $utils.isEffectiveCommon(roleDetail.code) }}</p>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="角色描述">
                  <p>{{ $utils.isEffectiveCommon(roleDetail.description) }}</p>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="table-title">
          <table-inner :table-data="roleDetail.roleUserList" :table-footer="false" title="授权用户信息">
            <el-table-column prop="name" label="用户名称" :formatter="$utils.isEffective" show-overflow-tooltip />
            <el-table-column prop="mobile" label="手机" :formatter="$utils.isEffective" show-overflow-tooltip />
          </table-inner>
        </div>
        <div class="area-title">
          <p class="title">
            功能授权
          </p>
        </div>
        <div class="detail-inner">
          <el-tree
            node-key="pid"
            :data="menuTreeList"
            :props="treeProps"
            default-expand-all
            show-checkbox
            :default-checked-keys="menuTreeCode"
          />
        </div>
      </div>
    </div>
    <!--    <div v-loading="loading.app" class="tabs-inner">-->
    <!--      <el-tabs v-model="activeName" type="card" @tabChange="getAuthDataList()">-->
    <!--        <el-tab-pane v-for="item in appList" :key="item.pid" :label="item.name" :name="item.pid" lazy />-->
    <!--      </el-tabs>-->
    <!--    </div>-->
  </el-main>
</template>
<script>
// import { get } from 'lodash'

export default {
  data () {
    return {
      // activeName: '',
      // appList: [],
      roleDetail: {
        roleUserList: [],
        roleDataScopeList: [],
        roleItemList: []
      },
      menuTreeList: [],
      // dataTreeList: [],
      menuTreeCode: [],
      treeProps: {
        label: 'name',
        class: this.setTreeItemClass,
        disabled: 'pid'
      },
      loading: {
        detail: false,
        app: false
      }
    }
  },
  created () {
    this.getDetail()
    // this.getAppList()
  },
  methods: {
    getDetail () {
      this.loading.detail = true
      this.$api.it.organization.role.detail(this.$route.query.pid).then(result => {
        console.log(result.data.data)
        this.roleDetail = result.data.data
        const menuTreeCode = []
        if (this.roleDetail.roleItemList) {
          this.roleDetail.roleItemList.forEach(item => {
            menuTreeCode.push(item.itemId)
          })
        }
        this.menuTreeCode = menuTreeCode
        if (this.roleDetail.appId) this.getMenuList()
      }).finally(() => {
        this.loading.detail = false
      })
    },
    // 获取应用
    // getAppList () {
    //   this.loading.app = true
    //   this.$api.it.application.apps.allList().then(result => {
    //     this.appList = result.data.data || []
    //     this.activeName = get(this.appList, '[0].pid', '')
    //     this.getMenuList()
    //   }).finally(() => {
    //     this.loading.app = false
    //   })
    // },
    // getAuthDataList () {
    //   this.loading.detail = true
    //   Promise.all([
    //     this.getMenuList(),
    //   ]).finally(() => {
    //     this.loading.detail = false
    //   })
    // },
    getMenuList () {
      return new Promise((resolve, reject) => {
        this.$api.it.application.menu.menuPageTree({ appId: this.roleDetail.appId }).then(result => {
          const resultData = result.data.data || []
          resultData.forEach(item => {
            if (item.children) {
              item.children.forEach(sub => {
                if (sub.appItemList) {
                  sub.appItemList.forEach(auth => {
                    auth.type = 'AUTH'
                    auth.appName = sub.appName
                    auth.parentId = sub.pid
                  })
                  sub.children = sub.appItemList
                }
              })
            }
          })
          this.menuTreeList = resultData
          resolve(resultData)
        }).catch(e => {
          reject(e)
        })
      })
    },
    setTreeItemClass (data, node) {
      if (data.parentCode === '-1') {
        return 'next-inline-node'
      }
    }
  }
}
</script>
<style lang="scss"></style>
