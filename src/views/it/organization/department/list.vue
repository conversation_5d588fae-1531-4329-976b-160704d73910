<template>
  <el-main>
    <page-title />
    <div class="partition-area department-area">
      <subfield-area :left-span="6" :right-span="18">
        <template #left>
          <el-tree
            ref="orgTree"
            class="org-tree"
            :data="orgTreeList"
            :props="treeProps"
            node-key="pid"
            :expand-on-click-node="false"
            default-expand-all
            @current-change="currentChange"
          />
        </template>
        <template #right>
          <user-page-list ref="userPageList" show-selection :show-dept-name="false" :active-dept-data="activeDeptData" show-set-leader>
            <template #btn-inner>
              <el-button type="primary" size="small" @click="$router.push({ name: 'organizationDepartmentRole' })">
                部门角色授权
              </el-button>
              <el-button type="primary" size="small" @click="dialog.dept.visible = true">
                新增部门
              </el-button>
              <el-button :disabled="!activeDeptData" type="primary" size="small" @click="editDept()">
                编辑部门
              </el-button>
              <el-button :disabled="!activeDeptData || activeDeptData.level === 0" type="danger" size="small" @click="delDept()">
                删除部门
              </el-button>
            </template>
          </user-page-list>
        </template>
      </subfield-area>
    </div>
    <dialog-inner v-model="dialog.dept.visible" :title="dialog.dept.title" :loading="loading.submit" @submit="confirmDept" @close="closeDialog">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="deptForm" v-auto-placeholder :model="deptForm" :rules="rules" label-width="120px" label-suffix="：">
            <!-- 顶级组织只能编辑名称 -->
            <el-form-item v-if="deptForm.parentId !== '-1'" prop="parentId" label="上级组织">
              <el-tree-select v-model="deptForm.parentId" :data="orgTreeList" :props="treeProps" check-strictly placeholder="请选择上级组织" />
            </el-form-item>
            <el-form-item prop="name" label="部门名称">
              <el-input v-model="deptForm.name" />
            </el-form-item>
            <el-form-item v-if="deptForm.parentId !== '-1'" prop="sort" label="排序">
              <el-input-number v-model="deptForm.sort" v-bind="inputNumberIntAttr" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </dialog-inner>
    <dialog-inner v-model="dialog.transfer.visible" :loading="loading.submit" title="调整部门" @submit="confirmTransfer">
      <div class="form-area">
        <div class="form-inner">
          <el-row :gutter="20">
            <el-col :span="12">
              <div class="area-title lv2">
                <p class="title">
                  选择部门
                </p>
              </div>
              <el-tree
                ref="departmentTree"
                :data="orgTreeList"
                :props="treeProps"
                :expand-on-click-node="false"
                :check-strictly="true"
                node-key="pid"
                class="org-tree"
                show-checkbox
                @check="treeCheck"
              />
            </el-col>
            <el-col :span="12">
              <div class="area-title lv2">
                <p class="title">
                  已选部门
                </p>
              </div>
              <el-table :data="chosenDeptList">
                <el-table-column prop="name" label="所属部门" />
                <el-table-column fixed="right" label="操作">
                  <template #default="scope">
                    <el-link class="ml5" type="danger" @click="removeDept(scope.row)">
                      移除
                    </el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </div>
      </div>
    </dialog-inner>
  </el-main>
</template>
<script>
import UserPageList from '@/components/it/user-page-list.vue'
import { inputNumberIntAttr } from '@/assets/lib/misc'
import { cloneDeep, findIndex } from 'lodash'

export default {
  components: { UserPageList },
  data () {
    return {
      inputNumberIntAttr,
      /** @public */
      changeRouter: false,
      /** @public */
      createdInit: false,
      orgTreeList: [],
      // 当前选中的部门信息
      activeDeptData: null,
      deptForm: {},
      chosenDeptList: [],
      chosenUserList: [],
      rules: {
        parentId: this.$rulesToolkit.createRules({ name: '上级组织', required: true, trigger: 'change' }),
        name: this.$rulesToolkit.createRules({ name: '部门名称', required: true, range: { max: 64 } }),
        sort: this.$rulesToolkit.createRules({ name: '排序', required: true })
      },
      treeProps: {
        label: 'name',
        value: 'pid'
      },
      dialog: {
        dept: {
          title: '新增部门',
          visible: false
        },
        transfer: {
          visible: false
        }
      },
      loading: {
        submit: false
      }
    }
  },
  created () {
    this.getDeptList()
  },
  methods: {
    editDept () {
      if (!this.activeDeptData) {
        this.$message.error('请选择部门')
        return false
      }
      this.deptForm = cloneDeep(this.activeDeptData)
      this.dialog.dept.title = '编辑部门'
      this.dialog.dept.visible = true
    },
    delDept () {
      if (!this.activeDeptData) {
        this.$message.error('请选择部门')
        return false
      }
      this.$confirm('确认删除此部门？', '确认删除').then(() => {
        this.$api.it.organization.department.del(this.activeDeptData.pid).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getDeptList()
        })
      })
    },
    confirmDept () {
      this.$refs.deptForm.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.deptForm)
        let api = this.$api.it.organization.department.add
        if (formData.pid) {
          api = this.$api.it.organization.department.edit
        }
        api(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getDeptList()
          this.dialog.dept.visible = false
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    treeCheck (data, statusInfo) {
      this.chosenDeptList = statusInfo.checkedNodes
    },
    removeDept (row) {
      this.$refs.departmentTree.setChecked(row.pid, false, false)
      const delIndex = findIndex(this.chosenDeptList, { pid: row.pid })
      this.chosenDeptList.splice(delIndex, 1)
    },
    transferUserDept () {
      const chosenList = this.$refs.userPageList.chosenList
      if (chosenList.length === 0) {
        this.$message.error('请选择用户')
        return false
      }
      this.chosenUserList = chosenList
      this.dialog.transfer.visible = true
    },
    confirmTransfer () {
      if (this.chosenDeptList.length === 0) {
        this.$message.error('请选择要调整的部门')
        return false
      }
      const formData = {
        userIds: [],
        deptIds: []
      }
      this.chosenDeptList.forEach(item => {
        formData.deptIds.push(item.pid)
      })
      this.chosenUserList.forEach(item => {
        formData.userIds.push(item.pid)
      })
      this.loading.submit = true
      this.$api.it.organization.department.addUserDept(formData).then(result => {
        this.$utils.resultBaseMessage(result)
        this.$refs.userPageList.init(this.activeDeptData.pid)
        this.dialog.transfer.visible = false
      }).finally(() => {
        this.loading.submit = false
      })
    },
    getDeptList () {
      this.$api.it.organization.department.treeList().then(result => {
        this.orgTreeList = result.data.data || []
        if (this.orgTreeList.length > 0) {
          this.$nextTick(() => {
            this.$refs.orgTree.setCurrentKey(this.orgTreeList[0].pid)
          })
        }
      })
    },
    currentChange (data, node) {
      this.activeDeptData = data
      this.deptForm.parentId = this.activeDeptData.pid
      this.$refs.userPageList && this.$refs.userPageList.init(data.pid)
    },
    closeDialog () {
      this.deptForm = {
        parentId: this.activeDeptData.pid
      }
      this.$refs.deptForm.resetFields()
    }
  }
}
</script>
<style lang="scss">
.org-tree {
  padding: 10px;
  .el-tree-node__label {
    flex: 1;
  }
}
.tree-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .tree-operation {
    display: inline-block;
    .el-link {
      margin-left: 5px;
    }
  }
}
.department-area {
  background-color: transparent;
  .area-inner {
    background-color: #fff;
  }
  .left-area {
    .area-inner {
      margin-right: 15px;
      box-sizing: border-box;
    }
  }
}
</style>
