<template>
  <el-main>
    <page-title show-back-btn />
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="部门授权列表" :table-data="deptList" :table-footer="false" stripe row-key="pid" default-expand-all>
        <el-table-column prop="name" label="组织名称" />
        <el-table-column label="主管拥有的角色" :formatter="roleNameFormatter('LEADER')" />
        <el-table-column label="成员拥有的角色" :formatter="roleNameFormatter('MEMBER')" />
        <el-table-column :fixed="'right'" label="操作">
          <template #default="scope">
            <el-link type="primary" @click="authRole(scope.row)">
              角色授权
            </el-link>
          </template>
        </el-table-column>
      </table-inner>
      <dialog-inner v-model="dialog.role.visible" :loading="loading.submit" title="角色授权" destroy-on-close @submit="confirmRole" @close="closeDialog">
        <table-inner ref="roleTable" :table-data="roleList" :table-footer="false" title="以下为该用户所在当前机构的所有可选角色，一个用户支持选择多个角色">
          <template #btn-inner>
            {{ dialog.role.activeDeptName }}
          </template>
          <el-table-column prop="name" label="角色名称" :formatter="$utils.isEffective" show-overflow-tooltip />
          <el-table-column prop="appName" label="所属应用" :formatter="$utils.isEffective" show-overflow-tooltip />
          <el-table-column label="主管拥有的角色">
            <template #default="scope">
              <el-checkbox v-model="scope.row.leader" />
            </template>
          </el-table-column>
          <el-table-column label="成员拥有的角色">
            <template #default="scope">
              <el-checkbox v-model="scope.row.member" />
            </template>
          </el-table-column>
        </table-inner>
      </dialog-inner>
    </div>
  </el-main>
</template>
<script>
import { cloneDeep, find, filter } from 'lodash'

export default {
  data () {
    return {
      deptList: [],
      baseRoleList: [],
      roleList: [],
      dialog: {
        role: {
          activeDeptName: '',
          activeDeptId: '',
          visible: false
        }
      },
      loading: {
        list: false,
        submit: false
      }
    }
  },
  created () {
    this.getList()
    this.getRoleList()
  },
  methods: {
    getList () {
      this.$api.it.organization.department.hasRoleTreeList().then(result => {
        this.deptList = result.data.data || []
      })
    },
    getRoleList () {
      this.$api.it.organization.role.list().then(result => {
        this.baseRoleList = result.data.data
      })
    },
    authRole (row) {
      console.log(row)
      this.dialog.role.activeDeptId = row.pid
      const activeRoleList = row.roleList || []
      const deptNameList = []
      const deptIdList = row.parentPath.split('|')
      let nowData = this.deptList
      deptIdList.forEach((deptId, index) => {
        if (index > 0) {
          const deptData = find(nowData, { pid: deptId })
          if (deptData) {
            deptNameList.push(deptData.name)
            nowData = deptData.children
          }
        }
      })
      deptNameList.push(row.name)
      const roleList = cloneDeep(this.baseRoleList)
      activeRoleList.forEach(item => {
        const activeRole = find(roleList, { pid: item.roleId })
        if (item.gradeType === 'MEMBER') {
          activeRole.member = true
        }
        if (item.gradeType === 'LEADER') {
          activeRole.leader = true
        }
      })
      this.roleList = roleList
      this.dialog.role.activeDeptName = deptNameList.join('/')
      this.dialog.role.visible = true
    },
    confirmRole () {
      console.log(this.roleList)
      const formData = {
        deptId: this.dialog.role.activeDeptId,
        roleList: []
      }
      this.roleList.forEach(item => {
        if (item.leader) {
          formData.roleList.push({
            roleId: item.pid,
            gradeType: 'LEADER'
          })
        }
        if (item.member) {
          formData.roleList.push({
            roleId: item.pid,
            gradeType: 'MEMBER'
          })
        }
      })
      this.loading.submit = true
      this.$api.it.organization.department.setRole(formData).then(result => {
        this.$utils.resultBaseMessage(result)
        this.getList()
        this.dialog.role.visible = false
      }).finally(() => {
        this.loading.submit = false
      })
    },
    roleNameFormatter (gradeType) {
      return (row, col, cell) => {
        const roleList = row.roleList || []
        const roleTypeList = filter(roleList, { gradeType })
        const roleNameList = []
        roleTypeList.forEach(item => {
          roleNameList.push(item.roleName)
        })
        return roleNameList.length > 0 ? roleNameList.join('，') : '-'
      }
    },
    closeDialog () {
      this.dialog.role.activeDeptName = ''
      this.dialog.role.activeDeptId = ''
    }
  }
}
</script>
<style lang="scss"></style>
