<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import { basePageListMixin } from '@fero/commons-vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import { monthPickAttr } from '@/assets/lib/misc'

export default {
    components: { FeFormItem, FePageMain },
    mixins: [basePageListMixin],
    data() {
        return {
            loading: {
                update: false
            },
            multipleSelection: [],
            columnOptionList: [
                { label: '发票号码', prop: 'invoiceNumber', minWidth: 150, formatter: this.$utils.isEffective },
                { label: '发票代码', prop: 'invoiceCode', minWidth: 150, formatter: this.$utils.isEffective },
                { label: '不含税金额（元）', prop: 'amountNoTax', minWidth: 150, formatter: this.$utils.tableMoneyFormat },
                { label: '含税金额（元）', prop: 'amountTax', minWidth: 150, formatter: this.$utils.tableMoneyFormat },
                { label: '发票类型', prop: 'invoiceType', minWidth: 150, formatter: this.$utils.tableStatusFormat('ASSETS_INVOICE_STATUS', { valueTypeToString: true }) },
                { label: '开票日期', prop: 'invoiceDate', minWidth: 120, formatter: this.$utils.tableDateFormat, slotName: 'belongAgreementTime' },
                { label: '购买方', prop: 'buyerName', minWidth: 150, formatter: this.$utils.isEffective },
                { label: '销售方', prop: 'sellerName', minWidth: 150, formatter: this.$utils.isEffective },
                { label: '校验码后6位', prop: 'checkCode', minWidth: 150, formatter: this.$utils.isEffective },
                { label: '使用状态', prop: 'isUsed', minWidth: 100, formatter: this.$utils.tableTrueFalseFormant({ true: '已使用', false: '未使用' }) },
                { label: '合同附件', prop: 'productTypeName', minWidth: 150, formatter: this.$utils.isEffective },
                { label: '更新时间', prop: 'updateTime', minWidth: 120, formatter: this.$utils.tableDateFormat }
            ]
        }
    },
    methods: {
        /** @public */
        getList() {
            this.loading.list = true
            this.$api.bill.invoiceAssets.pageList(this.searchForm).then(result => {
                if (result.data.data) {
                    this.tableList = result.data.data
                }
            }).finally(() => {
                this.loading.list = false
            })
        },
        batchDel() {
            if (this.multipleSelection.length === 0) {
                this.$message.warning('请至少选择一条数据')
                return
            }
            const params = this.multipleSelection.map(item => item.id)
            // 这里添加批量删除逻辑
            this.$confirm('确认要删除当前条数据？', '删除').then(() => {
                this.$api.bill.invoiceAssets.batchDel(params).then(res => {
                    this.$message.success('删除成功')
                    this.getList()
                })
            })

        },
        del(row) {
            this.$confirm('确认要删除当前条数据？', '删除').then(() => {
                this.$api.bill.invoiceAssets.del(row.id).then(res => {
                    this.$utils.resultBaseMessageV2(result)
                    this.getList()
                })
            })
        },
        handleSelectionChange(row){
            this.multipleSelection = row
        }
    }
}
</script>

<template>
    <fe-page-main>
        <div class="partition-area">
            <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
                <el-col :span="6">
                    <fe-form-item label="发票代码">
                        <el-input v-model="searchForm.invoiceCode" placeholder="请输入发票代码" />
                    </fe-form-item>
                </el-col>
                <el-col :span="6">
                    <fe-form-item label="发票号码">
                        <el-input v-model="searchForm.invoiceNumber" placeholder="请输入发票号码" />
                    </fe-form-item>
                </el-col>
                <el-col :span="6">
                    <fe-form-item label="发票类型">
                        <el-select v-model="searchForm.invoiceType" placeholder="请选择发票类型">
                            <el-option v-for="item in $utils.getEnableDictStatus(
                                'ASSETS_INVOICE_STATUS'
                            )" :key="item.pid" :label="item.label" :value="item.value" />
                        </el-select>
                    </fe-form-item>
                </el-col>
                <el-col :span="6">
                    <fe-form-item label="使用状态">
                        <el-select v-model="searchForm.isUsed" placeholder="请选择使用状态">
                            <el-option label="全部" value="" />
                            <el-option label="已使用" :value="1" />
                            <el-option label="未使用" :value="0" />
                        </el-select>
                    </fe-form-item>
                </el-col>
                <el-col :span="6">
                    <fe-form-item label="开票时间">
                        <el-date-picker v-model="searchForm.invoiceDate" type="date" placeholder="请选择开票时间"
                            format="YYYY-MM-DD" value-format="x" />
                    </fe-form-item>
                </el-col>
                <el-col :span="6">
                    <fe-form-item label="上传时间">
                        <el-date-picker v-model="searchForm.createTime" type="date" placeholder="请选择上传时间"
                            format="YYYY-MM-DD" value-format="x" />
                    </fe-form-item>
                </el-col>
            </fe-search-inner>
        </div>
        <div class="partition-table">
            <table-inner v-loading="loading.list" title="信息明细" @selection-change="handleSelectionChange"
                :table-data="tableList.records" show-set-columns :column-option="columnOptionList"
                :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe
                @change-page-num="changePageNum" @change-page-size="changePageSize">
                <template #btn-inner>
                    <el-button type="primary" @click="batchDel">
                        批量删除
                    </el-button>
                    <el-button type="primary" @click="$router.push({ name: 'invoiceAssetsAdd' })">
                        新增
                    </el-button>
                </template>
                <template #table-columns-before>
                    <el-table-column type="selection" width="55" fixed="left" />
                </template>
                <template #table-columns-after>
                    <el-table-column fixed="right" label="操作" width="120">
                        <template #default="scope">
                            <table-link :to="{ name: 'invoiceAssetsDetail', query: { pid: scope.row.id } }"
                                type="primary">
                                详情
                            </table-link>
                            <el-link type="danger" @click="del(scope.row)">
                                删除
                            </el-link>
                            <table-link :to="{ name: 'invoiceAssetsEdit', query: { pid: scope.row.id } }"
                                type="primary">
                                修改
                            </table-link>
                        </template>
                    </el-table-column>
                </template>
            </table-inner>
        </div>
    </fe-page-main>
</template>

<style lang="scss"></style>
