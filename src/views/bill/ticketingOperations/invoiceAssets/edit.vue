<template>
    <fe-page-main show-back-btn>
        <template #btn-inner>
            <tab-btn :loading="loading.submit" :showSaveBtn="false" :is-approval="false" @confirm-submit="submitThis" />
        </template>
        <div class="partition-area">
            <div class="form-area">
                <table-inner v-loading="loading.list1" :tableFooter="false" :cell-class-name="setStatusColor"
                    :column-option="columnoptionList_invoice" :table-data="baseInforForm.invoiceInfoList">
                    <template #title>
                        <span>发票信息</span>
                        <!-- <i class="iconfont icon-TmpSVG"></i> -->
                        <span style="font-size: 12px;margin-left: 20px;">共计：</span>
                        <span style="font-size: 12px;">{{ invoiceCount }}个</span>
                        <span style="font-size: 12px;margin-left: 20px;">发票金额合计：</span>
                        <span style="font-size: 12px;">{{ contractCount }}元</span>
                    </template>
                    <template #btn-inner>
                        <el-button type="primary" @click="pickFile()">
                            上传附件
                        </el-button>
                        <el-tooltip placement="right" effect="light">
                            <template #content>
                                上传文件类型为jpg、jpeg、bmp、png、pdf
                                文件名称不超过【32】位，大小不超过【7】MB
                            </template>
                            <el-icon class="ml-2">
                                <QuestionFilled />
                            </el-icon>
                        </el-tooltip>
                    </template>
                    <template #table-columns-before>
                        <el-table-column type="index" label="序号" />
                        <el-table-column label="发票类型" label-class-name="required-th">
                            <template #default="scope">
                                <el-select v-model="scope.row.invoiceType" placeholder="请选择发票类型">
                                    <el-option v-for="item in $utils.getEnableDictStatus(
                                        'ASSETS_INVOICE_STATUS'
                                    )" :key="item.pid" :label="item.label" :value="item.value" />
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column label="发票号码" label-class-name="required-th">
                            <template #default="scope">
                                <el-input v-model="scope.row.invoiceNumber" placeholder="请输入发票号码" />
                            </template>
                        </el-table-column>
                        <el-table-column label="发票代码" label-class-name="required-th">
                            <template #default="scope">
                                <el-input v-model="scope.row.invoiceCode" placeholder="请输入发票代码" />
                            </template>
                        </el-table-column>
                        <el-table-column label="开票日期" label-class-name="required-th">
                            <template #default="scope">
                                <el-date-picker v-model="scope.row.invoiceDate" type="date" placeholder="请选择开票日期"
                                    format="YYYY-MM-DD" value-format="x" />
                            </template>
                        </el-table-column>
                        <el-table-column label="含税金额（元）" label-class-name="required-th">
                            <template #default="scope">
                                <amount-input v-model="scope.row.amountTax" placeholder="请输入含税金额" />
                            </template>
                        </el-table-column>
                        <el-table-column label="不含税金额（元）" label-class-name="required-th">
                            <template #default="scope">
                                <amount-input v-model="scope.row.amountNoTax" placeholder="请输入不含税金额" />
                            </template>
                        </el-table-column>
                        <el-table-column label="购买方">
                            <template #default="scope">
                                <el-input v-model="scope.row.buyerName" placeholder="请输入购买方" />
                            </template>
                        </el-table-column>
                        <el-table-column label="销售方">
                            <template #default="scope">
                                <el-input v-model="scope.row.sellerName" placeholder="请输入销售方" />
                            </template>
                        </el-table-column>
                        <el-table-column label="校验码后6位">
                            <template #default="scope">
                                <el-input v-model="scope.row.checkCode" placeholder="请输入校验码后6位" />
                            </template>
                        </el-table-column>
                        <el-table-column label="上传企业名称" label-class-name="required-th">
                            <template #default="scope">
                                <detail-input :model-value="$utils.isEffectiveCommon(scope.row.registerName)" />
                            </template>
                        </el-table-column>
                    </template>
                    <template #table-columns-after>
                        <el-table-column fixed="right" label="操作">
                            <template #default="scope">
                                <!-- <el-link type="primary" @click="preview(scope.row)">
                                    详情
                                </el-link> -->
                                <el-link type="danger" @click="del(scope.row)">
                                    删除
                                </el-link>
                            </template>
                        </el-table-column>
                    </template>
                </table-inner>
            </div>
        </div>
    </fe-page-main>
    <pick-file-dialog ref="pickFileRef" :file-type="fileType" />
</template>

<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import PickFileDialog from '@/components/pick-file-dialog.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import { cloneDeep, defaultsDeep, find } from 'lodash'
import AmountInput from '@/components/amount-input.vue'
import dayjs from 'dayjs'
import AttachmentSimpleList from '@/components/attachment-simple-list.vue'
import { useBaseStore } from '@/assets/lib/commons'

export default {
    components: {
        AttachmentSimpleList,
        FePageMain,
        FeFormItem,
        AmountInput,
        PickFileDialog
    },
    data() {
        return {
            baseStore: null,
            loading: { submit: false },
            pid: this.$route.query.pid,
            fileList: [],
            fileType: 'default',
            baseInforForm: {
                invoiceInfoList: []
            },
            rules: {
                companyName: this.$rulesToolkit.createRules({
                    name: '上传方企业名称',
                    required: true
                }),
                contractCode: this.$rulesToolkit.createRules({
                    name: '合同编号',
                    required: true
                }),
                contractType: this.$rulesToolkit.createRules({
                    name: '合同类型',
                    required: true,
                    trigger: 'blur'
                }),
                tradeMode: this.$rulesToolkit.createRules({
                    name: '合同贸易方式',
                    required: true,
                    trigger: 'blur'
                }),
                contractAmount: this.$rulesToolkit.createRules({
                    name: '合同金额',
                    required: true,
                }),
                contractDate: this.$rulesToolkit.createRules({
                    name: '合同签署日期',
                    required: true,
                    trigger: 'blur'
                })
            },
            contractCount: 0,
        }
    },
    computed: {
        invoiceCount() {
            return this.baseInforForm.invoiceInfoList ? this.baseInforForm.invoiceInfoList.length : 0
        }
    },
    created() {
        if (this.pid) {
            this.getDetail()
        }
        const baseState = useBaseStore()
        const employeeInfo = baseState.employeeInfo
        this.baseInforForm.companyName = employeeInfo.mainName
    },
    methods: {
        getDetail() {
            this.$api.bill.invoiceAssets.detail(this.pid).then(res => {
                if (res.data.code === 200) {
                    this.baseInforForm.invoiceInfoList.push(res.data.data)
                    this.calculateTotalAmount()
                }
            })
        },
        pickFile() {
            this.$refs.pickFileRef.pick().then((fileList) => {
                if (fileList) {
                    const param = fileList.map(item => item.fileId)
                    console.log(param)
                    this.$api.bill.invoiceAssets.invoiceOcrList(param).then((res) => {
                        if (res.data.code === 200) {
                            const data = cloneDeep(res.data.data)
                            this.baseInforForm.invoiceInfoList.push(...data)
                            this.baseInforForm.invoiceInfoList.forEach(item => {
                                if (!item.registerName) {
                                    item.registerName = this.baseInforForm.companyName
                                }
                            })
                            console.log(this.baseInforForm.invoiceInfoList)
                            this.calculateTotalAmount()
                        }
                    })
                }
            })
        },
        del(index) {
            this.$confirm('确定要删除这条票据信息吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.baseInforForm.invoiceInfoList.splice(index, 1)
                this.$message({
                    type: 'success',
                    message: '删除成功!'
                })
                // 重新计算票据总金额
                this.calculateTotalAmount()
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                })
            })
        },
        calculateTotalAmount() {
            this.contractCount = this.baseInforForm.invoiceInfoList.reduce((total, item) => {
                return total + (Number(item.amountTax) || 0)
            }, 0)
        },
        async submitThis() {
            try {
                // 验证必填字段
                // const requiredFields = ['invoiceType', 'invoiceNumber', 'invoiceCode', 'invoiceDate', 'amountTax', 'amountNoTax'];
                // const invalidItems = this.baseInforForm.invoiceInfoList.filter(item => 
                //     requiredFields.some(field => !item[field])
                // );
                
                // if (invalidItems.length > 0) {
                //     this.$message.error('请填写所有必填字段');
                //     return;
                // }

                this.loading.submit = true;
                
                const params = cloneDeep(this.baseInforForm.invoiceInfoList);
                
                // 统一处理API调用
                const apiCall = this.pid 
                    ? this.$api.bill.invoiceAssets.update(params[0].id, params[0])
                    : this.$api.bill.invoiceAssets.saveList(params);
                
                const result = await apiCall;
                
                if (result.data.code === 200) {
                    this.$message.success(result.data.msg);
                    this.$router.back();
                }
            } catch (error) {
                console.error('提交失败:', error);
                this.$message.error('提交失败，请稍后重试');
            } finally {
                this.loading.submit = false;
            }
        }
    }
}
</script>

<style scoped>
.privacy-text {
    color: gray;
}

.import-text {
    color: red;
}
</style>
