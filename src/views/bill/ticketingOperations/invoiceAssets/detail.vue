<template>
    <fe-page-main show-back-btn>
        <div class="partition-area">
            <div class="form-area">
                <table-inner :tableFooter="false" :cell-class-name="setStatusColor"
                    :column-option="columnoptionList_invoice" :table-data="baseInforForm.invoiceInfoList">
                    <template #title>
                        <span>发票信息</span>
                        <span style="font-size: 12px;margin-left: 20px;">共计：</span>
                        <span style="font-size: 12px;">{{invoiceCount}}个</span>
                        <span style="font-size: 12px;margin-left: 20px;">发票金额合计：</span>
                        <span style="font-size: 12px;">{{contractCount}}元</span>
                    </template>
                    <template #table-columns-before>
                        <el-table-column type="index" label="序号" />
                        <el-table-column label="发票类型" prop="invoiceType" :formatter="$utils.tableStatusFormat('ASSETS_INVOICE_STATUS')"/>
                        <el-table-column label="发票号码" prop="invoiceNumber" :formatter="$utils.isEffective" />
                        <el-table-column label="发票代码" prop="invoiceCode" :formatter="$utils.isEffective" />
                        <el-table-column label="开票日期" prop="invoiceDate" :formatter="$utils.tableDateTimeFormat" />
                        <el-table-column label="含税金额（元）" prop="amountTax" :formatter="$utils.tableMoneyFormat" />
                        <el-table-column label="不含税金额（元）" prop="amountNoTax" :formatter="$utils.tableMoneyFormat" />
                        <el-table-column label="购买方" prop="buyerName" :formatter="$utils.isEffective" />
                        <el-table-column label="销售方" prop="sellerName" :formatter="$utils.isEffective" />
                        <el-table-column label="校验码后6位" prop="checkCode" :formatter="$utils.isEffective" />
                        <el-table-column label="上传企业名称" prop="registerName" :formatter="$utils.isEffective" />
                    </template>
                    <template #table-columns-after>
                        <el-table-column v-if="showAddContract && typeBool" fixed="right" label="查看附件">
                            <template #default="scope">
                                <el-link type="primary" @click="preview(scope.row)">
                                    详情
                                </el-link>
                            </template>
                        </el-table-column>
                    </template>
                </table-inner>
            </div>
        </div>
    </fe-page-main>
    <!-- <pick-file-dialog ref="pickFileRef" :file-type="fileType" /> -->
</template>

<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import PickFileDialog from '@/components/pick-file-dialog.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import { cloneDeep, defaultsDeep, find } from 'lodash'
import AmountInput from '@/components/amount-input.vue'
import dayjs from 'dayjs'
import AttachmentSimpleList from '@/components/attachment-simple-list.vue'
import { useBaseStore } from '@/assets/lib/commons'

export default {
    components: {
        AttachmentSimpleList,
        FePageMain,
        FeFormItem,
        AmountInput,
        PickFileDialog
    },
    data() {
        return {
            baseStore: null,
            loading: { submit: false },
            pid: this.$route.query.pid,
            fileList: [],
            fileType: 'default',
            baseInforForm: {
                invoiceInfoList:[]
            },
            contractCount: 0,
        }
    },
    created() {
        if (this.pid) {
            this.getDetail()
        }
    },
    computed: {
        invoiceCount() {
            return this.baseInforForm.invoiceInfoList ? this.baseInforForm.invoiceInfoList.length : 0
        }
    },
    methods: {
        getDetail() {
            this.$api.bill.invoiceAssets.detail(this.pid).then(res => {
                if (res.data.code === 200) {
                    this.baseInforForm.invoiceInfoList.push(res.data.data)
                    this.calculateTotalAmount()
                }
            })
        },
        calculateTotalAmount() {
            this.contractCount = this.baseInforForm.invoiceInfoList.reduce((total, item) => {
                return total + (Number(item.amountTax) || 0)
            }, 0)
        }
    }
}
</script>

<style scoped>
.privacy-text {
    color: gray;
}

.import-text {
    color: red;
}
</style>
