<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import { basePageListMixin } from '@fero/commons-vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import { monthPickAttr } from '@/assets/lib/misc'
import AttachmentSimpleList from '@/components/attachment-simple-list.vue'
import PdfPreviewDialog from '@/components/pdf-preview-dialog.vue'
import AttachmentList from '@/components/attachment-list.vue'
import DataDefendBtn from '@/components/data-defend-btn.vue'

export default {
  components: { FeFormItem, FePageMain, AttachmentSimpleList, PdfPreviewDialog, DataDefendBtn,AttachmentList },
  mixins: [basePageListMixin],
  data() {
    return {
      loading: {
        update: false
      },
      baseInfo: {
        fileList: [],
      },
      visible: false,
      attaVisible:false,
      multipleSelection: [],
      columnOptionList: [
        { label: '合同编号', prop: 'contractCode', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '合同类型', prop: 'contractType', minWidth: 150, formatter: this.$utils.tableStatusFormat('BILL_ASSETS_CONTRACT_TYPE', { valueTypeToString: true }) },
        { label: '合同金额（元）', prop: 'contractAmount', minWidth: 150, formatter: this.$utils.tableMoneyFormat },
        { label: '合同贸易方式', prop: 'tradeMode', minWidth: 150, formatter: this.$utils.tableStatusFormat('ASSETS_CONTRACT_TRADE_TYPE') },
        { label: '合同签署日期', prop: 'contractDate', minWidth: 120, formatter: this.$utils.tableDateFormat },
        { label: '使用状态', prop: 'isUsed', minWidth: 100, formatter: this.$utils.tableTrueFalseFormant({ true: '已使用', false: '未使用' }) },
      ]
    }
  },
  methods: {
    /** @public */
    getList() {
      this.loading.list = true
      this.$api.bill.assetManagement.pageList(this.searchForm).then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    openDia() {
      this.visible = true
    },
    batchDel() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      const params = this.multipleSelection.map(item => item.id)
      // 这里添加批量删除逻辑
      this.$api.bill.assetManagement.batchDel(params).then(res => {
        this.$message.success('删除成功')
        this.getList()
      })
    },
    del(row) {
      this.$api.bill.assetManagement.del(row.id).then(res => {
        this.$message.success('删除成功')
        this.getList()
      })
    },
    openAtta(row) {
      this.baseInfo = row
      this.attaVisible = true
    },
    handleSelectionChange(rows) {
      this.multipleSelection = rows
    },
    closeDialog() {
      this.visible = false
    },
    closeAtta() {
      this.attaVisible = false
    }

  }
}
</script>

<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <fe-form-item label="合同编号">
            <el-input v-model="searchForm.contractCode" placeholder="请输入合同编号" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="合同类型">
            <el-select v-model="searchForm.contractType" placeholder="请选择合同类型">
              <el-option v-for="item in $utils.getEnableDictStatus(
                'BILL_ASSETS_CONTRACT_TYPE'
              )" :key="item.pid" :label="item.label" :value="item.value" />
            </el-select>
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="使用状态">
            <el-select v-model="searchForm.isUsed" placeholder="请选择使用状态">
              <el-option label="全部" value="" />
              <el-option label="已使用" :value="1" />
              <el-option label="未使用" :value="0" />
            </el-select>
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="合同贸易方式">
            <el-select v-model="searchForm.tradeMode" placeholder="请选择合同贸易方式">
              <el-option v-for="item in $utils.getEnableDictStatus(
                'ASSETS_CONTRACT_TRADE_TYPE'
              )" :key="item.pid" :label="item.label" :value="item.value" />
            </el-select>
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="合同签署日期">
            <el-date-picker v-model="searchForm.contractDate" type="date" placeholder="请选择合同签署日期" format="YYYY-MM-DD"
              value-format="x" />
          </fe-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="信息明细" @selection-change="handleSelectionChange"
        :table-data="tableList.records" show-set-columns :column-option="columnOptionList" :page-num="tableList.current"
        :page-size="tableList.size" :total="tableList.total" stripe @change-page-num="changePageNum"
        @change-page-size="changePageSize">
        <template #btn-inner>
          <el-button :disabled="multipleSelection.length === 0" type="primary" @click="batchDel">
            批量删除
          </el-button>
          <data-defend-btn hideExportBtn import-btn-text="批量导入" style="margin-right:7px;"
            :import-template-api="$api.bill.assetManagement.download"
            import-api-url="/customer/background/contract/import" @importSuccess="getList">
            <template #dialog-form-content>
              <el-col :span="24">
                <span style="color: #F56C6C; margin-left: 10px; font-size: 12px;">
                  温馨提示：<br>
                  1、请先"下载模板"，填写合同信息后，点击"批量导入"，再"上传附件"<br>
                  2、支持XLS、XLSX格式，文件名称不超过32位，大小不超过7MB<br>
                  3、单次最多支持批量导入100条合同数据。<br>
                </span>
              </el-col>
            </template>
          </data-defend-btn>
          <el-button type="primary" @click="$router.push({ name: 'contractAssetsAdd' })">
            新增单笔
          </el-button>
          <el-link @click="openDia" type="primary" style="margin-right: 20px;">合同附件上传说明</el-link>
        </template>
        <template #table-columns-before>
          <el-table-column type="selection" width="55" fixed="left" />
        </template>
        <template #table-columns-after>
          <el-table-column label="合同附件" width="120">
            <template #default="scope">
              <el-link @click="openAtta(scope.row)" type="primary">
                查看
              </el-link>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" :formatter="$utils.tableDateFormat" width="120" />
          <el-table-column fixed="right" label="操作" width="120">
            <template #default="scope">
              <table-link :to="{ name: 'contractAssetsEdit', query: { pid: scope.row.id } }" type="primary">
                编辑
              </table-link>
              <el-link type="danger" @click="del(scope.row)">
                删除
              </el-link>
            </template>
          </el-table-column>
        </template>
      </table-inner>
    </div>
  </fe-page-main>
  <pdf-preview-dialog ref="pdfViewerRef" title="文件预览" />
  <dialog-inner v-model="visible" title="合同附件上传说明" :show-close="false" :close-on-press-escape="false"
    :close-on-click-modal="false" top="25vh" :showCancel="false" submit-title="我知道了" @submit="closeDialog"
    @close="closeDialog">
    <div style="padding:2% 5% ;">
      <div>
        <h2 style="color: #2D84ff">第一点</h2>
        <p style="font-size: 12px;">
          请确保合同信息与您上传的合同附件内容匹配，关于合同编号，请优先填写您上传附件合同编号或版本号，若两者都无，可使用系统自动生成编号。
        </p>
      </div>
      <div>
        <h2 style="color: #2D84ff">第二点</h2>
        <span style=" font-size: 12px;">
          基于同一合同或者订单多次出票时，请确保合同或者订单编号（版本号）一致。
        </span>
      </div>
      <div>
        <h2 style="color: #2D84ff">第三点</h2>
        <span style=" font-size: 12px;">
          请确保合同印章清晰、图像清晰完整，关键要素完整（如交易双方、合同金额、签订时间、付款方式等），多页须加盖骑缝章。
        </span>
      </div>
    </div>

  </dialog-inner>
  <dialog-inner v-model="attaVisible" title="合同附件" :show-close="true" :close-on-press-escape="false"
    :close-on-click-modal="false" top="25vh" :showFooter="false" 
    @close="closeAtta">
    <attachment-list :model="baseInfo" :business-id="baseInfo.id" business-type="SUPPLY_TICKET_ASSETS_CONTRACT" readonly />
  </dialog-inner>
</template>

<style lang="scss"></style>
