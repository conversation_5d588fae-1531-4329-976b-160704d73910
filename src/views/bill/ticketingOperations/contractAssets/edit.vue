<template>
  <fe-page-main show-back-btn>
    <template #btn-inner>
      <tab-btn :loading="loading.submit" :showSaveBtn="false" :is-approval="false" @confirm-submit="submitThis" />
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form ref="baseInfoFormRef" :model="baseInforForm" :rules="rules">
          <div class="area-title">
            <p class="title">基本信息</p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <fe-form-item label="上传方企业名称" prop="registerCertNo">
                  <el-select v-model="baseInforForm.registerCertNo" placeholder="请选择上传方企业名称" filterable
                    @change="changeCompany">
                    <el-option v-for="item in noAccesslist" :key="item.pid" :label="item.companyName"
                      :value="item.socialCreditCode" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="合同编号" prop="contractCode">
                  <el-input v-model="baseInforForm.contractCode" placeholder="请输入合同编号">
                    <template #append>
                      <el-button @click="creatContractCode">一键生成</el-button>
                    </template>
                  </el-input>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="合同类型" prop="contractType">
                  <el-select v-model="baseInforForm.contractType" placeholder="请选择合同类型">
                    <el-option v-for="item in $utils.getEnableDictStatus(
                      'BILL_ASSETS_CONTRACT_TYPE'
                    )" :key="item.pid" :label="item.label" :value="item.value" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="合同贸易方式" prop="tradeMode">
                  <el-select v-model="baseInforForm.tradeMode" placeholder="请选择合同贸易方式">
                    <el-option v-for="item in $utils.getEnableDictStatus(
                      'ASSETS_CONTRACT_TRADE_TYPE'
                    )" :key="item.pid" :label="item.label" :value="item.value" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="合同金额（元）" prop="contractAmount">
                  <amount-input v-model="baseInforForm.contractAmount" placeholder="请输入合同金额" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="合同签署日期" prop="contractDate">
                  <el-date-picker v-model="baseInforForm.contractDate" type="date" placeholder="请选择合同签署日期"
                    format="YYYY-MM-DD" value-format="x" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="合同附件"
                  help-content="上传文件类型为jpg、jpeg、bmp、png、pdf，文件名称不超过【32】位，大小不超过【7】MB，上传个数不能超过【2】个">
                  <attachment-simple-list v-model="baseInforForm" file-path="fileList"
                    file-type="SUPPLY_TICKET_ASSETS_CONTRACT" :show-create-time="false" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
  </fe-page-main>
</template>

<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import { cloneDeep, defaultsDeep, find } from 'lodash'
import AmountInput from '@/components/amount-input.vue'
import dayjs from 'dayjs'
import AttachmentSimpleList from '@/components/attachment-simple-list.vue'
import { useBaseStore } from '@/assets/lib/commons'

export default {
  components: {
    AttachmentSimpleList,
    FePageMain,
    FeFormItem,
    AmountInput
  },
  data() {
    return {
      baseStore: null,
      loading: { submit: false },
      pid: this.$route.query.pid,
      baseInforForm: {
        fileList: []
      },
      noAccesslist:[],
      rules: {
        registerCertNo: this.$rulesToolkit.createRules({
          name: '上传方企业名称',
          required: true
        }),
        contractCode: this.$rulesToolkit.createRules({
          name: '合同编号',
          required: true
        }),
        contractType: this.$rulesToolkit.createRules({
          name: '合同类型',
          required: true,
          trigger: 'blur'
        }),
        tradeMode: this.$rulesToolkit.createRules({
          name: '合同贸易方式',
          required: true,
          trigger: 'blur'
        }),
        contractAmount: this.$rulesToolkit.createRules({
          name: '合同金额',
          required: true,
        }),
        contractDate: this.$rulesToolkit.createRules({
          name: '合同签署日期',
          required: true,
          trigger: 'blur'
        })
      }
    }
  },
  created() {
    this.getCompList()
    if (this.pid) {
      this.getDateil()
      return
    }
    // const baseState = useBaseStore()
    // const employeeInfo = baseState.employeeInfo
    // this.baseInforForm.companyName = employeeInfo.mainName
    // this.getCompList()
  },
  methods: {
    async getCompList () {
      try {
        const res = await this.$api.business.noAccesslist({ type: 'CUSTOMER' })
        if (res.data.code === 200) {
          this.noAccesslist = res.data.data
        }
      } catch (error) {}
    },
    changeCompany(val){
      this.baseInforForm.registerCompanyId = this.noAccesslist.find(item => item.socialCreditCode === val).companyId
      this.baseInforForm.registerName = this.noAccesslist.find(item => item.socialCreditCode === val).companyName
    },
    getDateil() {
      this.$api.bill.assetManagement.detail(this.pid).then(res => {
        if (res.data.code === 200) {
          this.baseInforForm = cloneDeep(res.data.data)
          this.baseInforForm.companyName = this.baseInforForm.createName
          // this.fileList.fileId = this.baseInforForm.fileList || []
          this.getFile()
        }
      })
    },
    getFile() {
      this.$api.cloudDisk.manage.businessFileList({ businessId: this.pid, businessType: 'SUPPLY_TICKET_ASSETS_CONTRACT', fileType: 'default' }).then(res => {
        if (res.data.data) {
          this.baseInforForm.fileList = res.data.data
        } else {
          this.$message.warning('暂无附件')
        }
      })
    },
    creatContractCode() {
      const now = dayjs()
      this.baseInforForm.contractCode = `ht${now.format('YYYYMMDDHHmmss')}`
    },
    async submitThis() {
      try {
        const valid = await this.$refs.baseInfoFormRef.validate()
        if (!valid) return false
        // 添加附件数量验证
        if (this.baseInforForm.fileList?.length > 2) {
          this.$message.warning('最大上传合同附件数为2个')
          return false
        }
        console.log(this.baseInforForm)
        this.loading.submit = true
        const param = cloneDeep(this.baseInforForm)

        const apiMethod = this.pid
          ? this.$api.bill.assetManagement.update
          : this.$api.bill.assetManagement.save
        const apiParams = this.pid ? [this.pid, param] : [param]

        const result = await apiMethod(...apiParams)
        if (result.data.code === 200) {
          this.$message.success(result.data.msg)
          this.$router.back()
        }
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败，请稍后重试')
      } finally {
        this.loading.submit = false
      }
    }
  }
}
</script>

<style scoped>
.privacy-text {
  color: gray;
}

.import-text {
  color: red;
}
</style>
