<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <fe-form-item label="业务办理渠道名称">
            <el-input v-model="searchForm.mbrNm" placeholder="请输入业务办理渠道名称" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="业务办理渠道代码">
            <el-input v-model="searchForm.mbrTp" placeholder="请输入业务办理渠道代码" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="业务办理渠道状态">
            <el-select v-model="searchForm.mbrSts" placeholder="请选择业务办理渠道状态">
              <el-option v-for="item in $utils.getEnableDictStatus('BR_STATUS')" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </fe-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="信息明细" :table-data="tableList.records" 
            :page-num="tableList.current" :cell-class-name="setStatusColor" :page-size="tableList.size" 
            :total="tableList.total" :column-option="columnoptionList" show-set-columns 
            @change-page-num="changePageNum" @change-page-size="changePageSize">
      </table-inner>
    </div>
  </fe-page-main>
</template>
<script>
import { basePageListMixin } from '@fero/commons-vue'
import { baseWorkflowMixin } from '@/assets/lib/mixins'

export default {
  name: 'CustomerAccess',
  components: { },
  mixins: [basePageListMixin, baseWorkflowMixin],
  data () {
    return {
      tableList: {},
      columnoptionList: [
        { label: '业务办理渠道代码', prop: 'mbrId', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '业务办理渠道名称', prop: 'mbrNm', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '业务办理渠道状态', prop: 'mbrSts', formatter: this.$utils.tableStatusFormat('BR_STATUS'), minWidth: 180 },
        { label: '业务办理渠道类别', prop: 'mbrTp', formatter: this.$utils.tableStatusFormat('MEMBER_TYPE'), minWidth: 180 },
        { label: '创建时间', prop: 'createTime', formatter: this.$utils.tableDateTimeFormat, minWidth: 180 },
        { label: '更新日期', prop: 'updateTime', formatter: this.$utils.tableDateFormat, minWidth: 180 },
      ],
      loading: {
        list: false
      }
    }
  },
  created () {
  },
  methods: {
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.bill.billBasic.channel.pageList(this.searchForm).then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    }
  }
}
</script>

<style lang="scss">

</style>
