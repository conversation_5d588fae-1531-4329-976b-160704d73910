<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch" @change-more="changeMore" >
        <el-col :span="6">
          <fe-form-item label="商品分类编码">
            <el-input v-model="searchForm.categoryCode" placeholder="请输入商品分类编码" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="商品分类名称">
            <el-input v-model="searchForm.categoryName" placeholder="请输入商品分类名称" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="状态">
            <el-select v-model="searchForm.enabled" placeholder="请选择状态">
              <el-option v-for="item in $utils.getEnableDictStatus('PC_ENABLED')" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </fe-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table" style="display: flex;">
        <div style="flex: 3;">
            <div class="area-title" style="margin-top: 2%;background: #fff;">
                <p class="title">
                    商品分类
                </p>
            </div>
        <div class="form-inner" style="padding-left: 20px">
            <el-input placeholder="输入商品分类编码/商品名称" v-model="categoryCodeOrName" clearable></el-input>
            <el-tree
                class="filter-tree"
                :data="treeList"
                :props="defaultProps"
                @current-change="currentChange"
                default-expand-all
                :filter-node-method="filterNode"
                ref="tree"
            ></el-tree>
        </div>
    </div>
      <table-inner v-loading="loading.list" row-key="pid" 
        :table-data="tableList.records" 
        :page-num="tableList.current" 
        :page-size="tableList.size" 
        :total="tableList.total" 
        stripe 
        :column-option="columnoptionList" 
        show-set-columns 
        @change-page-num="changePageNum" 
        @selection-change="handleSelectionChange" 
        @change-page-size="changePageSize">
        <template #btn-inner>
          <el-button type="primary" @click="showSortDialog('add')">
            新增分类
          </el-button>
          <el-button type="primary" :disabled="isDis" @click="tableClick('on')">
            启用
          </el-button>
          <el-button type="danger" :disabled="isDis" @click="tableClick('off')">
            禁用
          </el-button>
          <el-button type="danger" :disabled="isDis" @click="tableClick('del')">
            删除
          </el-button>
        </template>
        <template #table-columns-after>
          <el-table-column type="selection" fixed="left" />
          <el-table-column fixed="right" label="操作" width="240px">
            <template #default="scope">
              <el-link type="danger"  @click="showSortDialog('edit',scope.row)">
                编辑
              </el-link>
            </template>
          </el-table-column>
        </template>
      </table-inner>
    </div>
    <sort-dialog v-if="sortDialogVisable" ref="sortDialoggRef" :rowData="rowData" :dialogTitle="dialogTitle" v-model="sortDialogVisable" @submit="categorySubmit" @close="chooseSpuClose" />
  </fe-page-main>
</template>
<script>
import { basePageListMixin } from '@fero/commons-vue'
import { baseWorkflowMixin } from '@/assets/lib/mixins'
import sortDialog from './sortDialog.vue'
import { cloneDeep } from 'lodash'

export default {
  name: 'businessProjectInitiation',
  mixins: [basePageListMixin, baseWorkflowMixin],
  components:{ sortDialog },
  data () {
    return {
        categoryCodeOrName:'',
        tableList: {},
        treeList:[],
        fullPath: '',
        rowData:{},
        isDis:true,
        sortDialogVisable:false,
        columnoptionList: [
            { label: '分类编码', prop: 'categoryCode', formatter: this.$utils.isEffective, minWidth: 180 },
            { label: '分类名称', prop: 'categoryName', formatter: this.$utils.isEffective, minWidth: 180 },
            { label: '上级分类编码', prop: 'parentCode', formatter: this.$utils.isEffective, minWidth: 180 },
            { label: '上级分类名称', prop: 'parentName', formatter: this.$utils.isEffective, minWidth: 180 },
            { label: '商品数量', prop: 'goodsCount', formatter: this.$utils.isEffective, minWidth: 180 },
            { label: '状态', prop: 'enabled', formatter: this.$utils.tableStatusFormat('PC_ENABLED'), minWidth: 180 },
        ],
        defaultProps: {
            children: 'children',
            label: 'name',
        },
        dialogTitle:'',
        selectionList:[]
    }
  },
  watch: {
    categoryCodeOrName(val) {
      this.$refs.tree.filter(val);
    },
  },
  created () {
    this.getTree()
  },
  methods: {
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.goods.category.pageList(this.searchForm).then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    getTree(){
      this.loading.list = true
      this.$api.goods.category.getTree(this.categoryCodeOrName).then(result => {
        if (result.data.data) {
          this.treeList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    currentChange(data,node){
      this.searchForm.fullPath = data.id
      this.search()
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    handleSelectionChange (val) {
      this.selectionList = val
      console.log(this.selectionList)
      this.isDis = this.selectionList.length <= 0
    },
    showSortDialog(type,row){
        this.dialogTitle =  type === 'add' ? '添加分类' : '编辑分类'
        this.rowData = {
          type : type,
          ...row
        }
        this.sortDialogVisable = true
    },
    /**
     * @description: 提交分类
     */
    async categorySubmit (val) {
      try {
        const res = await this.$api.goods.category.add(val)
        if (res.data.code === 200) {
          this.$message.success('操作成功')
          this.sortDialogVisable = false
          this.getTree()
        }
      } catch (error) {
        console.log(error)
      }
    },
    /**
     * 表单操作按钮
    */
    
    async tableClick(tab) {
        this.loading.list = true;
        const isEnable = tab === 'on';
        const isDisable = tab === 'off';
        const isDelete = tab === 'del';

        const hasDisabledItem = this.selectionList.some(item => item.enabled === (isEnable ? 1 : 0));

        if (hasDisabledItem && !isDelete) {
          this.$message.warning(`当前选中条目中有数据不可${isEnable ? '启用' : '禁用'}`);
          this.loading.list = false;
          return;
        }

        const ids = this.selectionList.map(item => item.pid);

        const confirmMessage = {
          on: '确认要启用商品分类？',
          off: '确认要禁用商品分类？',
          del: '确认要删除商品分类？'
        }[tab];

        const confirmTitle = {
          on: '启用',
          off: '禁用',
          del: '删除'
        }[tab];

        try {
          await this.$confirm(confirmMessage, confirmTitle);

          const apiMethod = {
            on: this.$api.goods.category.enable,
            off: this.$api.goods.category.disable,
            del: this.$api.goods.category.delete
          }[tab];

          const result = await apiMethod(ids);
          this.$utils.resultBaseMessageV2(result);
          this.search();
        } catch (error) {
          // 处理取消操作或其他错误
          console.error(error);
        } finally {
          this.loading.list = false;
        }
      }
  }
}
</script>
<style lang="scss">
.filter-tree{
    padding-top: 20px;
}
</style>
