<!-- table-inner -->
<template>
  <dialog-inner v-model="visible" :title="dialogTitle" :loading="loading.submit" @submit="submit" @close="close">
    <div class="form-area">
      <el-form :model="sortForm" ref="sortFormRef" :rules="rules">
        <div class="form-inner">
          <el-row :gutter="24" class="form-inner">
            <el-col :span="12">
              <fe-form-item label="上级商品分类" prop="parentId">
                <el-cascader v-model="sortForm.parentId" :options="options" :props="{ checkStrictly: true , label: 'name', value: 'id' }" placeholder="请输入上级商品分类" filterable  clearable></el-cascader>
              </fe-form-item>
            </el-col>
            <el-col :span="12">
              <fe-form-item label="商品分类名称" prop="categoryName">
                <el-input v-model="sortForm.categoryName" placeholder="请输入商品分类名称" />
              </fe-form-item>
            </el-col>
            <el-col v-if="type === 'edit'" :span="12">
              <fe-form-item label="商品分类编码" >
                <detail-input  :modelValue="$utils.isEffectiveCommon(sortForm.companyName)" />
              </fe-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
  </dialog-inner>
</template>
<script>
import { cloneDeep, isEmpty } from 'lodash'
export default {
  name: 'sortDialog',
  components: {},
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    // 标题
    dialogTitle: {
      type: String,
      default: '添加分类'
    },
    type: {
      type: String,
      default: 'add'
    },
    rowData: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  emits: ['submit', 'close', 'update:value'],
  data () {
    return {
      dialog: {
        chooseFileCloudDialogVisible: false
      },
      sortForm: {
        parentId:'',
        categoryName:'',
      },
      options:[],
      loading: {
        list: false
      },
      rules:{
        categoryName: this.$rulesToolkit.createRules({ name: '商品分类名称',required: true }),
        // parentId:this.$rulesToolkit.createRules({ name: '上级商品分类', required: true, trigger: 'change' }),
      }
    }
  },
  computed: {
    visible: {
      get () {
        return this.modelValue
      },
      set (value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  created () {
    this.getTree()
    console.log(this.rowData)
    if(this.type === 'edit'){
      console.log(this.rowData)
      // this.sortForm = {

      // }
    }
  },
  watch: {
    visible (newVal) {
      if (newVal) {
        this.initializeDialog()
      }
    }
  },
  methods: {
   
    /**
     * @description: 获取页面全部数据（左侧分类，右侧商品列表）
    */
    getTree(){
      this.loading.list = true
      this.$api.goods.category.getTree(this.categoryCodeOrName).then(result => {
        if (result.data.data) {
          this.options = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    /**
     * @description: 获取商品列表
    */
    submit () {
      this.$refs.sortFormRef.validate().then(() => {
        const { parentId,categoryName } = this.sortForm
        const data = {
          parentId:parentId[parentId.length-1],
          fullPath:parentId,
          categoryName:categoryName
        }
        this.$emit('submit', data)
      })
    },
    close () {
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
.el-dialog__body .el-table--fit {
  padding: 0;
}
</style>
