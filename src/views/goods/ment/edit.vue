<template>
  <fe-page-main show-back-btn>
    <template #btn-inner>
      <el-button type="primary" @click="submitThis">
        保存
      </el-button>
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form
          ref="baseInforFormRef"
          :model="baseInforForm"
          :rules="rules"
          :disabled="mode === 'meeting'"
        >
          <div class="area-title">
            <p class="title">
              价格盯市设置
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="24">
                <el-form-item>
                  <el-switch v-model="baseInforForm.vss" active-text="云商" @change="changeVss" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <fe-form-item label="产品大类" prop="projectName">
                  <el-select
                    v-model="baseInforForm.productType"
                    :disabled="!baseInforForm.vss"
                    placeholder="请选择"
                    @change="changeProductType"
                  >
                    <el-option
                      v-for="(item, index) in productTypeList"
                      :key="index"
                      :label="item.productName"
                      :value="item.productType"
                    />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="6">
                <fe-form-item label="钢种系列" prop="projectName">
                  <el-select
                    v-model="baseInforForm.representSteel"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="(item, index) in representSteelList"
                      :key="index"
                      :label="item.representSteel"
                      :value="item.representSteel"
                    />
                  </el-select>
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
  </fe-page-main>
</template>

<script>
import { cloneDeep } from 'lodash'
import { cookie } from '@/assets/lib/commons'

export default {
  data () {
    return {
      unionId: this.$route.query.unionId,
      productTypeList: [],
      representSteelList: [],
      baseInforForm: {
        vss: true
      },
      loading: {
        detail: false,
        submit: false
      }
    }
  },
  created () {
    this.$route.meta.parentName = 'goodsMent'
    this.getDetail()
    this.getProductTypeList()
  },
  methods: {
    getDetail () {
      this.loading.detail = true
      this.$api.goods.ment.getDetail({ unionId: this.unionId })
        .then(result => {
          if (result.data.data) {
            this.baseInforForm = result.data.data
            this.baseInforForm.vss = true
            this.isEdit = true
            this.getRepresentSteelList(this.baseInforForm.productType)
          }
        })
        .finally(() => {
          this.loading.detail = false
        })
    },
    changeVss (vss) {
      if (!vss) {
        this.baseInforForm.productName = ''
        this.baseInforForm.productType = ''
        this.baseInforForm.representSteel = ''
      }
    },
    getProductTypeList () {
      this.$api.goods.ment.getProductTypeList()
        .then(result => {
          if (result.data.data) {
            this.productTypeList = result.data.data
          }
        })
    },
    getRepresentSteelList (productType) {
      this.$api.goods.ment.getRepresentSteelList({ productType })
        .then(result => {
          if (result.data.data) {
            this.representSteelList = result.data.data
          }
        })
    },
    changeProductType (val) {
      const data = this.productTypeList.find(item => item.productType === val)
      this.baseInforForm.productName = data.productName
      this.getRepresentSteelList(val)
    },
    apiCallbackFun () {
      return new Promise((resolve, reject) => {
        this.loading.submit = true
        const params = cloneDeep(this.baseInforForm)
        const param = {
          ...params,
          unionId: this.unionId
        }
        const apiCall = this.isEdit ? this.$api.goods.ment.update : this.$api.goods.ment.priceSave
        apiCall(param)
          .then(result => {
            this.$utils.resultBaseMessageV2(result)
            resolve(true)
          })
          .finally(() => {
            this.loading.submit = false
          })
      })
    },
    submitThis () {
      this.apiCallbackFun()
        .then(() => {
          this.$router.back()
        })
    }
  }
}
</script>

<style lang="scss"></style>
