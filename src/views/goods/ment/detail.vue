<template>
  <fe-page-main show-back-btn>
    <div class="partition-area">
        <div class="form-area">
        <el-form ref="baseInforFormRef" :model="baseInforForm" :rules="rules" :disabled="mode === 'meeting'">
            <div class="area-title">
                <p class="title">
                基本信息
                </p>
            </div>
            <div class="form-inner">
                <el-row :gutter="80">
                    <el-col :span="12">
                        <fe-form-item label="分类名称" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="12">
                        <fe-form-item label="规格型号" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="12">
                        <fe-form-item label="产品牌号" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="12">
                        <fe-form-item label="产品形态名称" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="12">
                        <fe-form-item label="产品规范代码" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="12">
                        <fe-form-item label="执行标准全名" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="12">
                        <fe-form-item label="生产厂家" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="12">
                        <fe-form-item label="计量单位" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="12">
                        <fe-form-item label="制造标准代码" prop="projectCode">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="12">
                        <fe-form-item label="产品规范代码状态" prop="projectCode">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="12">
                        <fe-form-item label="系统商品ID" prop="projectCode">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="24">
                        <fe-form-item label="商品主图" prop="declarationNote">

                        </fe-form-item>
                    </el-col>
                </el-row>
            </div>
            <div class="area-title">
                <p class="title">
                    图片附件
                </p>
            </div>
            <div class="form-inner">
                <el-row :gutter="80">
                    <el-col :span="12">
                        <fe-form-item label="法人姓名" prop="projectCode">
                            <el-input v-model="baseInforForm.projectName" placeholder="请输入法人姓名" />
                        </fe-form-item>
                    </el-col>
                </el-row>
            </div>
            <div class="area-title">
                <p class="title">
                    价格盯市设置
                </p>
            </div>
            <div class="form-inner">
                <el-row :gutter="80">
                    <el-col :span="24">
                        <fe-form-item>
                            <el-switch
                                v-model="baseInforForm.vss"
                                active-text="云商">
                            </el-switch>
                        </fe-form-item>
                    </el-col>
                    <el-col :span="3">
                        <fe-form-item label="产品大类" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="3">
                        <fe-form-item label="钢种系列" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="3">
                        <fe-form-item label="产品细类代码" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="3">
                        <fe-form-item label="产品形态" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="3">
                        <fe-form-item label="产品牌号" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="3">
                        <fe-form-item label="产品规范代码" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col><el-col :span="3">
                        <fe-form-item label="制造标准代码" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="24">
                        <fe-form-item>
                            <el-switch
                                v-model="baseInforForm.vss"
                                active-text="找钢网">
                            </el-switch>
                        </fe-form-item>
                    </el-col>
                    <el-col :span="3">
                        <fe-form-item label="产品大类" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="3">
                        <fe-form-item label="品名" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="3">
                        <fe-form-item label="市场区域" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                    <el-col :span="3">
                        <fe-form-item label="钢厂" prop="projectName">
                            <detail-input  :modelValue="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                        </fe-form-item>
                    </el-col>
                </el-row>
            </div>
        </el-form>
    </div>
    </div>
  </fe-page-main>
</template>
<script>

import { cloneDeep } from 'lodash'
// import AttachmentList from '@/components/attachment-list.vue'
// import RelationCompanyList from '@/views/business/project/initiation/components/relation-company-list.vue'

export default {
  components: { },
  data () {
    return {
      activeName: 'info',
      type: this.$route.query.type,
      pid: this.$route.query.pid,
      baseInforForm: {
        participantsList: [],
        enterpriseList: [],
        downEnterpriseList: [],
        supplierEnterprise: [],
        goodsList: []
      },
      loading: {
        detail: false
      }
    }
  },
  created () {
    this.$route.meta.parentName = this.$route.query.type
    if (this.pid) {
      this.getProjectInfo()
    }
  },
  methods: {
    // 处理数据
    processingInfoData (data) {
      const infoData = cloneDeep(data)
      infoData.downEnterprise = ''
      infoData.tradeExecution = ''
      infoData.participantsList.forEach(item => {
        switch (item.code) {
          case 'down':
            infoData.downEnterprise = item.companyId
            break
          case 'trade-execution': // 易执行企业
            infoData.tradeExecution = item.companyId
            break
        }
      })
      return infoData
    },
    /**
     * 获取详情
    */
    getProjectInfo () {
    //   if (!this.pid) return
    //   this.loading.detail = true
    //   this.$api.business.project.initiation.info(this.pid).then(result => {
    //     this.baseForm = this.processingInfoData(result.data.data)
    //     console.log(this.baseForm, 'baseFormbaseForm')
    //   }).finally(() => {
    //     this.loading.detail = false
    //   })
    },
    // 校验调用接口方法
    apiCallbackFun (type) {
      return new Promise((resolve, reject) => {
        const checkFormList = [
          // 组件校验方法
          this.$refs.baseInfoEditRef.checkForm(),
          this.$refs.relationCompanyListRef.checkForm()
        ]
        Promise.all(checkFormList).then(checkResult => {
          this.loading.submit = true
          const formData = this.$refs.baseInfoEditRef.processingData(this.baseForm)
          this.$api.business.project.initiation.save(formData).then(result => {
            if (result.data.data) {
              this.baseForm.pid = result.data.data
              resolve(true)
            }
          }).finally(() => {
            if (type !== 'submit') {
              this.loading.submit = false
            }
          })
        })
      })
    },
    // 保存
    confirmSubmit (type) {
      return new Promise((resolve, reject) => {
        this.apiCallbackFun(type).then(result => {
          this.$utils.resultBaseMessageV2(result)
          resolve(result)
        }).finally(() => {
          if (type !== 'submit') {
            this.loading.submit = false
          }
        })
      })
    },
    // 提交
    submitThis () {
      this.confirmSubmit('submit').then(() => {
        this.$api.business.project.initiation.submit(this.baseForm.pid).then(result => {
          this.$utils.resultBaseMessageV2(result)
          this.$router.back()
        }).finally(() => {
          this.loading.submit = false
        })
      })
    }
  }
}
</script>
<style lang="scss"></style>
