<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner
        :search-form="searchForm"
        @submit-search="search"
        @clear-search="clearSearch"
        @change-more="changeMore"
      >
        <el-col :span="6">
          <fe-form-item label="产品大类名称">
            <el-input
              v-model="searchForm.productTypeName"
              placeholder="请输入产品大类名称"
            />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="钢种系列名称">
            <el-input
              v-model="searchForm.gradeSeriesName"
              placeholder="请输入钢种系列名称"
            />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="产品牌号">
            <el-input
              v-model="searchForm.shopSign"
              placeholder="请输入产品牌号"
            />
          </fe-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <fe-form-item label="商品状态">
              <el-select v-model="searchForm.enabled" placeholder="请选择商品状态">
                <el-option v-for="item in $utils.getEnableDictStatus('PC_ENABLED')" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
          </fe-form-item>
        </el-col> -->
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner
        v-loading="loading.list"
        :table-data="tableList.records"
        :page-num="tableList.current"
        :page-size="tableList.size"
        :total="tableList.total"
        stripe
        :column-option="columnoptionList"
        show-set-columns
        @change-page-num="changePageNum"
        @change-page-size="changePageSize"
      >
        <template #btn-inner>
          <!--          <el-button-->
          <!--            type="primary"-->
          <!--            @click="$router.push({ name: 'goodsMentAdd' })"-->
          <!--          >-->
          <!--            新增-->
          <!--          </el-button>-->
          <!--          <el-button-->
          <!--            type="primary"-->
          <!--            @click="$router.push({ name: 'goodsMentDetail' })"-->
          <!--          >-->
          <!--            导入-->
          <!--          </el-button>-->
          <el-button :loading="loading.update" type="primary" @click="updateData">
            更新数据
          </el-button>
          <!--          <el-button-->
          <!--            type="primary"-->
          <!--            @click="$router.push({ name: 'customerAccessAdd' })"-->
          <!--          >-->
          <!--            批量启用-->
          <!--          </el-button>-->
          <!--          <el-button-->
          <!--            type="danger"-->
          <!--            @click="$router.push({ name: 'customerAccessAdd' })"-->
          <!--          >-->
          <!--            批量禁用-->
          <!--          </el-button>-->
        </template>
        <!--<template #table-columns-after>
          &lt;!&ndash; <el-table-column type="selection" fixed="left" /> &ndash;&gt;
          <el-table-column fixed="right" label="操作" width="80px">
            <template #default="scope">
              <table-link
                :to="{
                  name: 'goodsMentAdd',
                  query: {
                    unionId: scope.row.unionId
                  },
                }"
                type="primary"
              >
                盯市设置
              </table-link>
              &lt;!&ndash; <table-link
                :to="{
                  name: 'customerAccessDetail',
                  query: {
                    pid: scope.row.pid,
                    type: scope.row.collateralFloatType,
                  },
                }"
                type="primary"
              >
                参数查看
              </table-link> &ndash;&gt;
              &lt;!&ndash; <el-link type="danger" v-if="['1'].includes(scope.row.status)" @click="tableClick(scope.row,'off')">
                    启用
                </el-link>
                <el-link type="danger" v-if="['1'].includes(scope.row.status)" @click="tableClick(scope.row,'off')">
                    禁用
                </el-link> &ndash;&gt;
            </template>
          </el-table-column>
        </template>-->
      </table-inner>
    </div>
  </fe-page-main>
</template>
<script>
import { basePageListMixin } from '@fero/commons-vue'
import { baseWorkflowMixin } from '@/assets/lib/mixins'

export default {
  name: 'BusinessProjectInitiation',
  mixins: [basePageListMixin, baseWorkflowMixin],

  data () {
    return {
      loading: {
        update: false
      },
      tableList: {},
      columnoptionList: [
        {
          label: '产品大类编码',
          prop: 'saleClassNo',
          formatter: this.$utils.isEffective,
          minWidth: 180
        },
        {
          label: '产品大类名称',
          prop: 'productTypeName',
          formatter: this.$utils.isEffective,
          minWidth: 180
        },
        {
          label: '钢种系列编码',
          prop: 'gradeSeriesCode',
          formatter: this.$utils.isEffective,
          minWidth: 180
        },
        {
          label: '钢种系列名称',
          prop: 'gradeSeriesName',
          formatter: this.$utils.isEffective,
          checked: true,
          minWidth: 180
        },
        {
          label: '产品形态编码',
          prop: 'prodTypeNo',
          formatter: this.$utils.isEffective,
          checked: true,
          minWidth: 180
        },
        {
          label: '产品形态名称',
          prop: 'prodTypeName',
          formatter: this.$utils.isEffective,
          minWidth: 180
        },
        {
          label: '产品细类编码',
          prop: 'saleDetailClassNo',
          formatter: this.$utils.isEffective,
          minWidth: 180
        },
        {
          label: '产品细类名称',
          prop: 'productDetailName',
          formatter: this.$utils.isEffective,
          minWidth: 180
        },
        {
          label: '产品牌号',
          prop: 'shopSign',
          formatter: this.$utils.isEffective,
          minWidth: 180
        },
        {
          label: '执行标准全名',
          prop: 'stdFulDesc',
          formatter: this.$utils.isEffective,
          minWidth: 180
        }
      ]
    }
  },
  created () {},
  methods: {
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.goods.ment
        .pageList(this.searchForm)
        .then((result) => {
          if (result.data.data) {
            this.tableList = result.data.data
          }
        })
        .finally(() => {
          this.loading.list = false
        })
    },
    /**
     * 表单操作按钮
     */
    tableClick (row, tab) {
      switch (tab) {
        // 启用
        case 'on':
          this.$confirm('确认要启用当前条数据', '启用').then(() => {
            this.$api.business.floatCollateral.ment
              .delete({ pid: row.pid })
              .then((result) => {
                this.$utils.resultBaseMessageV2(result)
                this.search()
              })
              .finally(() => {})
          })
          break
        // 禁用
        case 'off':
          this.$confirm('确认要禁用当前条数据', '禁用').then(() => {
            this.$api.business.floatCollateral.ment
              .close({ pid: row.pid })
              .then((result) => {
                this.$utils.resultBaseMessageV2(result)
                this.search()
              })
              .finally(() => {})
          })
          break
      }
    },
    updateData () {
      this.loading.update = true
      this.$api.goods.ment.updateData().then(result => {
        this.getList()
      }).finally(() => {
        this.loading.update = false
      })
    }
  }
}
</script>
<style lang="scss">
.title {
  width: 50%;
}
</style>
