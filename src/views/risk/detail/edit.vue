<template>
  <fe-page-main show-back-btn>
    <div class="tabs-inner">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="预警信息" name="info">
          <div class="form-area">
            <el-form ref="baseInforFormRef" :model="baseInforForm">
              <div class="area-title">
                <p class="title">
                  预警信息
                </p>
              </div>
              <div class="form-inner">
                <el-row :gutter="80">
                  <el-col :span="12">
                    <fe-form-item label="预警规则名称">
                      <detail-input :model-value="$utils.isEffectiveCommon(dealForm.ruleName)" />
                    </fe-form-item>
                  </el-col>
                  <el-col :span="12">
                    <fe-form-item label="预警记录编号">
                      <detail-input :model-value="$utils.isEffectiveCommon(dealForm.alertCode)" />
                    </fe-form-item>
                  </el-col>
                  <el-col :span="12">
                    <fe-form-item label="预警等级">
                      <detail-input :model-value="$utils.statusFormat(dealForm.ruleLevel,'GX_RULE_LEVEL')" />
                    </fe-form-item>
                  </el-col>
                  <el-col :span="12">
                    <fe-form-item label="关联业务编号">
                      <detail-input :model-value="$utils.isEffectiveCommon(baseInforForm.alertRelationCode)" />
                    </fe-form-item>
                  </el-col>
                  <el-col :span="12">
                    <fe-form-item label="关联业务">
                      <detail-input :model-value="$utils.isEffectiveCommon(baseInforForm.alertRelationName)" />
                    </fe-form-item>
                  </el-col>
                  <el-col :span="12">
                    <fe-form-item label="预警企业名称">
                      <detail-input :model-value="$utils.isEffectiveCommon(baseInforForm.companyName)" />
                    </fe-form-item>
                  </el-col>
                  <el-col :span="24">
                    <fe-form-item label="预警规则描述">
                      <detail-input :model-value="$utils.isEffectiveCommon(dealForm.ruleDescription)" />
                    </fe-form-item>
                  </el-col>
                </el-row>
              </div>
              <div class="area-title">
                <p class="title">
                  触发记录
                </p>
              </div>
              <div class="form-inner">
                <table-inner :table-header="false" :table-data="baseInforForm.alertTriggerLogVOList" :table-footer="false">
                  <el-table-column prop="triggerTime" label="触发时间" min-width="120" :formatter="$utils.tableDateFormat" />
                  <el-table-column prop="triggerCondition" label="触发条件" min-width="120" :formatter="$utils.isEffective" />
                </table-inner>
              </div>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="处置信息" name="deal">
          <div class="form-area">
            <div class="form-inner">
              <el-col :span="12">
                <fe-form-item label="处置措施">
                  <detail-input :model-value="$utils.statusFormat(baseInforForm.disposalMeasures, 'GX_DISPOSAL_MEASURES')" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="处置人">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseInforForm.disposalPersonName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="处置状态">
                  <detail-input :model-value="$utils.statusFormat(baseInforForm.disposalStatus,'GX_ALERT_DISPOSAL_STATUS')" />
                </fe-form-item>
              </el-col>
              <el-col :span="24">
                <fe-form-item label="处置说明">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseInforForm.disposalRemark)" />
                </fe-form-item>
              </el-col>
            </div>
            <attachment-list v-model="dealForm" :business-id="pid" business-type="ALERT_NOTICE" readonly />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </fe-page-main>
</template>

<script>
import { defineComponent } from 'vue'
import { cloneDeep } from 'lodash'
import FeFormItem from '@/components/base/fe-form-item.vue'
import AttachmentList from '@/components/attachment-list.vue'

export default defineComponent({
  name: 'BaseinfoEdit',
  components: { AttachmentList, FeFormItem },
  mixins: [],
  props: {
    pid: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue'],
  data () {
    return {
      pid: this.$route.query.pid,
      activeName: 'info',
      dealForm: {},
      cusForm: {},
      proForm: {},
      baseInforForm: {
        ruleLevel: '',
        alertCode: '',
        ruleName: '',
        ruleDescription: '',
        alertTriggerLogVOList: []
      },
      loading: {
        submit: false
      }
    }
  },
  computed: {
    financingPeriodFormat () {
      return `${this.baseInforForm.financingPeriod}${this.$utils.statusFormat(this.baseInforForm.financingPeriodUnit, 'GX_FINANCING_PERIOD_UNIT')}`
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    async getDetail () {
      try {
        this.loading.submit = true
        const dealRes = await this.$api.risk.details.details({ id: this.pid })
        if (this.$utils.isResEmpty(dealRes.data.data)) {
          this.dealForm = cloneDeep(dealRes.data.data)
          this.baseInforForm.alertRelationCode = this.dealForm.alertRelationCode
          this.baseInforForm.alertRelationName = this.dealForm.alertRelationName
          this.baseInforForm.companyName = this.dealForm.companyName
          this.baseInforForm.ruleLevel = this.dealForm.ruleLevel
          this.baseInforForm.alertCode = this.dealForm.alertCode
          this.baseInforForm.ruleName = this.dealForm.ruleName
          this.baseInforForm.isApproval = this.dealForm.isApproval
          this.baseInforForm.ruleDescription = this.dealForm.ruleDescription
          this.baseInforForm.alertTriggerLogVOList = this.dealForm.alertTriggerLogVOList
          this.baseInforForm.disposalMeasures = this.dealForm.disposalMeasures
          this.baseInforForm.disposalPersonName = this.dealForm.disposalPersonName
          this.baseInforForm.disposalStatus = this.dealForm.disposalStatus
          this.baseInforForm.disposalRemark = this.dealForm.disposalRemark
        }
      } catch (error) {
      } finally {
        this.loading.submit = false
      }
    }
  }
})
</script>

<style>
.slect-width{
  display: inline-block;
  width: 30% !important;
  margin-right: 10px;
}
.inp-width{
  width: 60% !important;
  display: inline-block;
}

</style>
