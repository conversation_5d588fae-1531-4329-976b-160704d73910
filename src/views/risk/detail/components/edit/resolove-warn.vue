<template>
  <el-form ref="baseInforForm" :model="baseInforForm" :rules="rules">
    <div class="form-area">
      <div class="form-inner">
        <el-row :gutter="24">
          <el-col :span="12">
            <fe-form-item label="处置人">
              <el-input v-model="baseInforForm.handPerson" placeholder="请输入预警规则名称" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="处置措施">
              <el-select v-model="baseInforForm.handMeasure" placeholder="处置措施">
                <el-option v-for="val in $utils.getEnableDictStatus('WARN_DEAL')" :key="val.value" :label="val.label"
                  :value="val.value" />
              </el-select>
            </fe-form-item>
          </el-col>
          <el-col :span="24">
            <fe-form-item label="处置说明">
              <el-input v-model="baseInforForm.content" :rows="4" type="textarea" placeholder="请输入处置说明" show-word-limit :maxlength="1000" />
            </fe-form-item>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-form>
</template>
<script>
export default {
  components: {},
  props: {
    // 传进来的数据
    localBaseInforForm: {
      type: Object,
      default () {
        return {}
      }
    }

  },
  emits: ['update:baseInforForm', 'check'],
  data () {
    return {
      companyList: [],
      rules: {
        handMeasure: this.$rulesToolkit.createRules({ name: '处置措施', required: true, trigger: 'change' })
      }
    }
  },
  computed: {
    baseInforForm: {
      get () {
        return this.localBaseInforForm
      },
      set (value) {
        this.$emit('update:baseInforForm', value)
      }
    }
  },
  created () {
  },
  methods: {
    checkForm () {
      return new Promise((resolve, reject) => {
        this.$refs.baseInforForm.validate().then(() => {
          resolve(true)
          return true
        }).catch(() => {
          this.$emit('check')
          return false
        })
      })
    }
  }
}
</script>

<style scoped>
</style>
