<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" :more-btn="false" :show-question-icon="false" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <fe-form-item label="预警企业名称">
            <el-input v-model="searchForm.companyName" placeholder="请输入预警企业名称" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="预警规则名称">
            <el-input v-model="searchForm.ruleName" placeholder="请输入预警规则名称" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="预警状态">
            <el-select v-model="searchForm.status" placeholder="请选择预警状态">
              <el-option v-for="item in $utils.getEnableDictStatus('GX_ALERT_STATUS')" :key="item.pid" :label="item.label" :value="item.value" />
            </el-select>
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="处置状态">
            <el-select v-model="searchForm.disposalStatus" placeholder="请选择处置状态">
              <el-option v-for="item in $utils.getEnableDictStatus('GX_ALERT_DISPOSAL_STATUS')" :key="item.pid" :label="item.label" :value="item.value" />
            </el-select>
          </fe-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" :table-data="tableList.records" show-set-columns :column-option="columnoptionList" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe @change-page-num="changePageNum" @change-page-size="changePageSize">
        <template #table-columns-before>
          <!-- <el-table-column label="预警等级" min-width="100">
            <template #default="scope">
              <div :class="`coloc_rule${scope.row.ruleLevel}`">
                <span>{{$utils.statusFormat(scope.row.ruleLevel,'GX_RULE_LEVEL')}}</span>
              </div>
            </template>
          </el-table-column> -->
        </template>
        <template #table-columns-after>
          <el-table-column fixed="right" label="操作" width="240px">
            <template #default="scope">
              <table-link :to="{ name:'riskEarlyWarningDetailInfo',query:{ pid: scope.row.pid } }" type="primary">
                详情
              </table-link>
              <el-link v-if="['1'].includes(scope.row.status) && ['2'].includes(scope.row.disposalStatus)" type="primary" @click="dealClick(scope.row)">
                处置
              </el-link>
              <el-link v-if="['3','4'].includes(scope.row.status) && ['1'].includes(scope.row.disposalStatus)" type="danger" @click="againDisposeClick(scope.row)">
                重置处置
              </el-link>
              <el-link v-if="['5'].includes(scope.row.status) && ['1'].includes(scope.row.disposalStatus)" type="danger" @click="finishDisposeClick(scope.row)">
                完成处置
              </el-link>
              <!-- <el-link type="primary" @click="dealClick(scope.row)">
                处置
              </el-link>
              <el-link  type="danger" @click="againDisposeClick(scope.row)">
                重置处置
              </el-link>
              <el-link  type="danger" @click="finishDisposeClick(scope.row)">
                完成处置
              </el-link> -->
            </template>
          </el-table-column>
        </template>
      </table-inner>
      <dialog-inner v-model="dialog.update" width="50%" title="预警处置" @close="closeDeal" @submit="submitDeal">
        <el-form ref="appForm" :model="appForm" :rules="rules">
          <div class="form-area">
            <div class="form-inner">
              <el-col :span="12">
                <fe-form-item label="处置措施" prop="disposalMeasures">
                  <el-select
                    v-model="appForm.disposalMeasures"
                    clearable
                    filterable
                    placeholder="请选择处置措施"
                  >
                    <el-option v-for="item in $utils.getEnableDictStatus('GX_DISPOSAL_MEASURES')" :key="item.pid" :label="item.itemName" :value="item.itemValue" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="处置人" prop="disposalPersonName">
                  <el-input v-model="appForm.disposalPersonName" placeholder="请输入处置人" />
                </fe-form-item>
              </el-col>
              <el-col :span="24">
                <fe-form-item label="处置说明" prop="disposalRemark">
                  <el-input v-model="appForm.disposalRemark" :maxlength="2000" :rows="4" type="textarea" placeholder="请输入处置说明" show-word-limit />
                </fe-form-item>
              </el-col>
            </div>
            <attachment-list v-model="appForm" :business-id="pid" business-type="ALERT_NOTICE" />
          </div>
        </el-form>
      </dialog-inner>
    </div>
  </fe-page-main>
</template>
<script>
import { basePageListMixin } from '@fero/commons-vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import AttachmentList from '@/components/attachment-list.vue'
export default {
  name: 'BusinessProjectSetup',
  components: { FeFormItem, AttachmentList },
  mixins: [basePageListMixin],
  data () {
    return {
      tableList: {},
      pid: '',
      dialog: {
        update: false
      },
      appForm: {
        disposalMeasures: '',
        disposalPersonName: '',
        disposalRemark: '',
        fileList: []
      },
      columnoptionList: [
        { label: '预警等级', prop: 'ruleLevel', formatter: this.$utils.tableStatusFormat('GX_RULE_LEVEL'), minWidth: 140 },
        { label: '预警记录编号', prop: 'alertCode', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '预警规则名称', prop: 'ruleName', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '关联业务编号', prop: 'alertRelationCode', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '预警企业名称', prop: 'companyName', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '预警时间', prop: 'updateTime', formatter: this.$utils.tableDateFormat, minWidth: 180 },
        { label: '预警状态', prop: 'status', formatter: this.$utils.tableStatusFormat('GX_ALERT_STATUS'), minWidth: 180 },
        { label: '处置状态', prop: 'disposalStatus', formatter: this.$utils.tableStatusFormat('GX_ALERT_DISPOSAL_STATUS'), minWidth: 180 }
      ],
      rules: {
        disposalMeasures: [{ required: true, message: '请选择处置措施', trigger: 'change' }]
      }
    }
  },
  created () {},
  methods: {
    /** @public */
    async getList () {
      try {
        this.loading.list = true
        const res = await this.$api.risk.details.pageList(this.searchForm)
        this.tableList = res.data.data || {}
        this.loading.list = false
      } catch (error) {
        console.log(error)
      } finally {
        this.loading.list = false
      }
    },
    dealClick (row) {
      this.pid = row.pid
      this.dialog.update = true
    },
    submitDeal () {
      try {
        this.$refs.appForm.validate().then(async () => {
          const param = {
            pid: this.pid,
            ...this.appForm
          }
          const res = await this.$api.risk.details.dispose(param)
          if (res.data.code === 200) {
            this.$message.success('处置成功')
            this.getList()
            this.$refs.appForm.resetFields()
            this.dialog.update = false
          } else {
            this.$message.error(res.data.msg)
          }
        }).catch(() => {
          return false
        })
      } catch (error) { }
    },
    closeDeal () {
      this.appForm = {
        disposalMeasures: '',
        disposalPersonName: '',
        disposalRemark: '',
        fileList: []
      }
      this.$refs.appForm.resetFields()
      this.dialog.update = false
    },
    againDisposeClick (row) {
      this.$confirm('确认要重置？', '确认').then(async () => {
        try {
          const res = await this.$api.risk.details.againDispose({ pid: row.pid })
          if (res.data.code === 200) {
            this.$message.success('操作成功')
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        } catch (error) {}
      })
    },
    finishDisposeClick (row) {
      this.$confirm('确认要完成处置？', '确认').then(async () => {
        try {
          const res = await this.$api.risk.details.finishDispose({ id: row.pid })
          if (res.data.code === 200) {
            this.$message.success('操作成功')
            this.getList()
          } else {
            this.$message.error(res.data.msg)
          }
        } catch (error) {}
      })
    }
  }
}
</script>
<style lang="scss"></style>
