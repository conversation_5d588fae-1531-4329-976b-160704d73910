<template>
  <div class="partition-table">
    <el-row :gutter="20">
      <el-col :span="8" v-for="item in data" :key="item.id">
        <el-card style="border: 2px solid #54B2EA;">
          <div style="font-size: 18px; font-weight: bold;">{{ item.warnName }}</div>
          <!-- <label>{{ $utils.statusFormat(item.warnType,'WARN_FROM_TYPE') }}</label> -->
          <div style="display: flex; justify-content:  space-between; align-items: center;">
            <div >
              <span :class="getStatusClass(item.warnStatus)"></span>
              <span class="warn-status">{{ $utils.statusFormat(item.warnStatus,'WARN_INFO_STATE')}}</span>
              <el-tag class="warn-tag" effect="dark" :type="item.type">{{ item.warnLevelName }}</el-tag>
            </div>
            <div style="margin-left: auto;">
              <el-switch v-model="item.warnStatus" active-value="1" inactive-value="0"  @click="updateStatus(item)"/>
            </div>
          </div>
          <div v-html="item.ruleDescription.replace('XXX', `<span style='color: red; font-size: 16px'>${item.warnNumber}</span>` )"></div>
          <div v-html="item.triggeringCondition.replace('XXX', `<span style='color: red; font-size: 16px'>${item.warnNumber}</span>` )"></div>
          <div style="display: flex; width: 100%;">
            <el-button style="flex: 1;" @click="editWarnInfo(item)">规则调整</el-button>
            <el-button style="flex: 1;" @click="warnModifyRecord(item)">操作记录</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <edit-dialog ref="editDialog" @refresh="getList" />
    <record-dialog ref="recordDialog" @refresh="getList" />
  </div>
</template>
<script>

import EditDialog from './components/edit/edit-dialog.vue'
import RecordDialog from './components/edit/record-dialog.vue'
import { basePageListMixin } from '@fero/commons-vue'
import { searchMoreMixin, setColorMixin } from '@/assets/lib/mixins'

export default {
  mixins: [basePageListMixin, searchMoreMixin, setColorMixin],
  components: {
    EditDialog,
    RecordDialog
  },
  data () {
    return {
      baseSearchForm: {
      },
      tableList: {},
      columnoptionList: [],
      loading: {
        list: false
      },
      data: [],
      record: {}
    }
  },
  methods: {
    getStatusClass (status) {
      if (status === '1') {
        return 'status-low'
      } else if (status === '0') {
        return 'status-high'
      } else {
        return 'status-low'
      }
    },
    getList () {
      this.loading.list = true
      this.$api.risk.earlyWarning.rules.pageList(this.searchForm).then(result => {
        this.data = result.data.data
        this.data.map(item => {
          if (item.warnLevel === 'high') {
            item.type = 'danger'
            item.warnLevelName = '高'
          } else if (item.warnLevel === 'middle') {
            item.type = 'warning'
            item.warnLevelName = '中'
          } else if (item.warnLevel === 'low') {
            item.type = 'primary'
            item.warnLevelName = '低'
          }
          return item
        })
      }).finally(() => {
        this.loading.list = false
      })
    },
    editWarnInfo (row) {
      this.$refs.editDialog.init(row)
    },
    warnModifyRecord (row) {
      const param = row
      this.$api.risk.earlyWarning.rules.recordList(param.pid).then(result => {
        this.record = result.data.data
      }).finally(() => {
        this.$refs.recordDialog.init(this.record)
      })
    },
    updateStatus (item) {
      const param = item
      this.$api.risk.earlyWarning.rules.updateState(param).then(result => {
        this.data = result.data.data
      }).finally(() => {
        this.getList()
        this.loading.list = false
      })
    },
    /**
     * 表单操作按钮
    */
    tableClick (row, tab) {
      switch (tab) {
        // 删除
        case 'del':
          this.$confirm(this.$utils.tableTips(tab).tip, this.$utils.tableTips(tab).title).then(() => {
            this.$api.risk.blackWhite.black.del(row.pid).then(result => {
              this.$utils.resultBaseMessageV2(result)
              this.getList()
            }).finally(() => {
            })
          })
          break
      }
    }
  }
}
</script>
<style lang="scss">
.status-high {
  width: 6px;
  height: 6px;
  background-color: red;
  border-radius: 3px;
  display: inline-block;
}
.status-medium {
  width: 6px;
  height: 6px;
  background-color: orange;
  border-radius: 3px;
  display: inline-block;
}
.status-low {
  width: 6px;
  height: 6px;
  background-color: green;
  border-radius: 3px;
  display: inline-block;
}
.el-col {
  padding-bottom: 10px;
}
.warn-tag {
  border-radius: 10px;
}

.warn-status {
  margin: 10px;
  display: inline-block;
}
</style>
