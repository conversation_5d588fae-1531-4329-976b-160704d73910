<template>
    <el-dialog v-model="dialog.warnInfo" class="form-dialog drag-dialog" :close-on-click-modal="false" title="预警信息" @close="close">
      <el-table v-loading="loading.list" title="信息明细" :data="tableList.records" stripe >
          <el-table-column prop="warningBrforeLevel" label="调整前" :formatter="$utils.tableStatusFormat('WARN_LEVEL')" show-overflow-tooltip min-width="80" />
          <el-table-column prop="warningAfterLevel" label="调整后" :formatter="$utils.tableStatusFormat('WARN_LEVEL')" show-overflow-tooltip min-width="80" />
          <el-table-column prop="adjustTime" label="调整时间" :formatter="$utils.tableDateTimeFormat" min-width="120" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialog.warnInfo = false">
            关闭
          </el-button>
        </span>
      </template>
    </el-dialog>
  </template>

<script>
import { cloneDeep } from 'lodash'

export default {
  name: 'WarnInfoDialog',
  props: {},
  data () {
    return {
      tableList: {
        records: []
      },
      warnInfoFormRules: {
        warnStatus: this.$rulesToolkit.createRules({ name: '预警等级', required: true, trigger: 'change' }),
        warnNumber: this.$rulesToolkit.createRules({ name: '触发条件值', required: true })
      },
      dialog: {
        warnInfo: false
      },
      loading: {
        submit: false
      }
    }
  },
  methods: {
    init (data) {
      if (data) {
        this.tableList.records = cloneDeep(data || [])
      } else {
        this.tableList.records = []
      }
      console.log(data)
      this.dialog.warnInfo = true
    },
    close () {
      this.warnInfoForm = {}
      //   this.$refs.warnInfoFormRef.resetFields()
      this.dialog.warnInfo = false
    }
  }
}
</script>

  <style scoped>
  </style>
