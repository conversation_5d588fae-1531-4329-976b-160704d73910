<template>
    <el-dialog v-model="dialog.warnInfo" class="form-dialog drag-dialog" :close-on-click-modal="false" title="预警信息" @close="close">
      <div class="form-area">
        <div class="form-inner">
          <el-form ref="warnInfoFormRef" :model="warnInfoForm" :rules="warnInfoFormRules" label-width="90px" label-suffix="：">
            <el-form-item prop="describe" label="触发条件">
              <el-input disabled="true" v-model="warnInfoForm.triggeringCondition" placeholder="请输入" />
            </el-form-item>
            <el-form-item prop="warnNumber" label="触发条件值">
              <el-input-number v-model="warnInfoForm.warnNumber" placeholder="请输入" v-bind="$constants.baseIntNumberProp" @wheel.prevent="false" />
            </el-form-item>
            <el-form-item prop="warnStatus" label="预警等级">
              <el-select v-model="warnInfoForm.warnLevel" placeholder="请选择">
                <el-option v-for="item in $utils.getEnableDictStatus('WARN_LEVEL')" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item prop="warnStatus" label="是否发送短信">
              <el-select v-model="warnInfoForm.isSendSms" placeholder="请选择">
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item prop="warnStatus" label="短信接收人信息">
              <el-select v-model="warnInfoForm.warnLevel" placeholder="请选择">
                <el-option v-for="item in $utils.getEnableDictStatus('WARN_LEVEL')" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item prop="warnStatus" label="推送短信频次">
              <el-select v-model="warnInfoForm.pushFrequency" placeholder="请选择">
                <el-option v-for="item in $utils.getEnableDictStatus('WARN_LEVEL')" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialog.warnInfo = false">
            取消
          </el-button>
          <debounce-button :loading="loading.submit" type="primary" @click="confirminfoLoading">
            保存
          </debounce-button>
        </span>
      </template>
    </el-dialog>
</template>

<script>
import { cloneDeep } from 'lodash'

export default {
  name: 'WarnInfoDialog',
  props: {},
  data () {
    return {
      warnInfoForm: {},
      warnInfoFormRules: {
        warnStatus: this.$rulesToolkit.createRules({ name: '预警等级', required: true, trigger: 'change' }),
        warnNumber: this.$rulesToolkit.createRules({ name: '触发条件值', required: true }),
        isSendSms: this.$rulesToolkit.createRules({ name: '是否发送短信', required: true, trigger: 'change' }),
        warnLevel: this.$rulesToolkit.createRules({ name: '短信接收人信息', required: true, trigger: 'change' }),
        pushFrequency: this.$rulesToolkit.createRules({ name: '推送短信频次', required: true, trigger: 'change' }),
      },
      dialog: {
        warnInfo: false
      },
      loading: {
        submit: false
      }
    }
  },
  methods: {
    init (data) {
      this.warnInfoForm = cloneDeep(data || {})
      this.dialog.warnInfo = true
    },
    confirminfoLoading () {
      this.$refs.warnInfoFormRef.validate().then(() => {
       this.$api.risk.rules.update(this.warnInfoForm).then(result => {
          this.$emit('refresh')
        }).finally(() => {
          this.dialog.warnInfo = false
        })
      })
    },
    close () {
      this.warnInfoForm = {}
      //   this.$refs.warnInfoFormRef.resetFields()
      this.dialog.warnInfo = false
    }
  }
}
</script>

  <style scoped>
  </style>
