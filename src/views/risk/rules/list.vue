<template>
  <fe-page-main>
    <div class="partition-area">
      <el-row>
        <el-col v-for="item in planList" :key="item.pid" :span="8">
          <el-card class="box-card">
            <template #header>
              <span style="font-size:18px"><b>{{ item.ruleName }}</b></span>
            </template>
            <div class="rule-div">
              <span>{{ item.enable === 0 ? '生效中':'未生效' }}</span>
              <div :class="item.ruleColor">
                {{ $utils.statusFormat(item.ruleLevel,'GX_RULE_LEVEL') }}
              </div>
              <el-switch
                v-model="item.enable"
                class="rule-switch"
                :active-value="0"
                :inactive-value="1"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="changeSwitch(item)"
              />
            </div>
            <div class="rule-desc-div">
              <el-row>
                <span><b>规则描述</b>：{{ item.ruleDescription }}</span>
              </el-row>
              <el-row>
                <span><b>触发条件</b>：{{ item.triggerCondition }}</span>
              </el-row>
            </div>
            <el-divider />
            <div class="rule-btn-div">
              <el-link type="primary" @click="planClick(item)">
                规则调整
              </el-link>
              <el-divider direction="vertical" />
              <el-link type="primary" @click="setPlanClick(item)">
                操作记录
              </el-link>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <dialog-inner v-model="dialog.update" width="50%" title="规则调整" @close="closePlan" @submit="submitPlan">
      <el-form ref="appform" v-auto-placeholder :model="appform" :rules="rules">
        <div class="form-area">
          <div class="area-title">
            <p class="title">
              {{ info.ruleName }}
            </p>
          </div>
          <div class="form-inner">
            <fe-form-item label="触发条件">
              <span>{{ info.triggerCondition }}</span>
            </fe-form-item>
            <fe-form-item label="触发条件值" prop="triggerConditionValue">
              <el-input v-model="appform.triggerConditionValue" placeholder="请输入触发条件值" />
            </fe-form-item>
            <fe-form-item label="预警等级" prop="ruleLevel">
              <el-select
                v-model="appform.ruleLevel"
                class="slect-width"
                clearable
                filterable
                placeholder="请选择"
              >
                <el-option v-for="item in $utils.getEnableDictStatus('GX_RULE_LEVEL')" :key="item.pid" :label="item.label" :value="item.value" />
              </el-select>
            </fe-form-item>
            <fe-form-item v-if="info.pid === 5" prop="isSendSms" label="是否发送短信">
              <el-select v-model="appform.isSendSms" placeholder="请选择">
                <el-option label="是" :value="1" />
                <el-option label="否" :value="0" />
              </el-select>
            </fe-form-item>
            <fe-form-item v-if="info.pid === 5" prop="smsRecipientPhone" label="短信接收人手机号" help-content="可添加多个手机号，手机号之间用英文逗号分隔。">
<!--              <el-select v-model="appform.warnLevel" placeholder="请选择">-->
<!--                <el-option v-for="item in $utils.getEnableDictStatus('WARN_LEVEL')" :key="item.value" :label="item.label" :value="item.value" />-->
<!--              </el-select>-->
              <el-input v-model="appform.smsRecipientPhone" placeholder="请输入短信接收人手机号" />
            </fe-form-item>
            <fe-form-item v-if="info.pid === 5" prop="pushFrequency" label="推送短信频次">
              <el-select v-model="appform.pushFrequency" placeholder="请选择">
                <el-option v-for="item in $utils.getEnableDictStatus('SMS_FREQ')" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </fe-form-item>
          </div>
        </div>
      </el-form>
    </dialog-inner>
    <dialog-inner v-model="dialog.tableUpdate" width="50%" title="操作记录" :show-footer="false" @close="close">
      <el-form ref="actionLogDialog">
        <div class="form-area">
          <div class="area-title">
            <p class="title">
              {{ info.ruleName }}
            </p>
          </div>
          <div class="form-inner">
            <table-inner :table-header="false" :table-data="repaymentList" :table-footer="false">
              <el-table-column prop="beforeTriggerConditionVale" label="调整前触发条件值" min-width="120" :formatter="$utils.isEffective" />
              <el-table-column prop="beforeRuleLevel" label="调整前预警等级" min-width="120" :formatter="$utils.tableStatusFormat('GX_RULE_LEVEL')" />
              <el-table-column prop="afterTriggerConditionVale" label="调整后触发条件值" min-width="120" :formatter="$utils.isEffective" />
              <el-table-column prop="afterRuleLevel" label="调整后预警等级" min-width="120" :formatter="$utils.tableStatusFormat('GX_RULE_LEVEL')" />
              <el-table-column prop="updateTime" label="调整时间" min-width="120" :formatter="$utils.tableDateFormat" />
            </table-inner>
          </div>
        </div>
      </el-form>
    </dialog-inner>
  </fe-page-main>
</template>
<script>
import { basePageListMixin } from '@fero/commons-vue'
import { cloneDeep } from 'lodash'
import FeFormItem from '@/components/base/fe-form-item.vue'
export default {
  name: 'BusinessProjectSetup',
  components: [FeFormItem],
  mixins: [basePageListMixin],
  data () {
    return {
      info: {},
      planList: [],
      repaymentList: [],
      appform: {
        ruleLevel: '',
        triggerConditionValue: '',
        isSendSms: '',
        warnLevel: '',
        pushFrequency: ''
      },
      dialog: {
        update: false,
        tableUpdate: false
      },
      rules: {
        ruleLevel: this.$rulesToolkit.createRules({ name: '预警等级', required: true, trigger: 'change' }),
        triggerConditionValue: this.$rulesToolkit.createRules({ name: '触发条件值', required: true }),
        isSendSms: this.$rulesToolkit.createRules({ name: '是否发送短信', required: true, trigger: 'change' }),
        smsRecipientPhone: this.$rulesToolkit.createRules({ name: '短信接收人信息', required: true, trigger: 'change' }),
        pushFrequency: this.$rulesToolkit.createRules({ name: '推送短信频次', required: true, trigger: 'change' })
      }
    }
  },
  created () {
  },
  methods: {
    /** @public */
    async getList () {
      try {
        const res = await this.$api.risk.rules.pageList()
        if (res.data.code === 200) {
          this.planList = res.data.data || []
          this.planList.forEach(v => {
            v.ruleColor = `ruleLevel-div coloc_rule${v.ruleLevel}`
          })
          console.log(this.planList)
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading.list = false
      }
    },
    submitPlan () {
      this.$refs.appform.validate().then(async () => {
        const formData = cloneDeep(this.appform)
        formData.pid = this.info.pid
        const res = await this.$api.risk.rules.update(formData)
        if (res.data.code === 200) {
          this.$message.success('调整成功')
          this.dialog.update = false
          this.getList()
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    async setPlanClick (item) {
      this.info = item
      this.dialog.tableUpdate = true
      try {
        const res = await this.$api.risk.rules.logList({ id: item.pid })
        if (res.data.code === 200) {
          this.repaymentList = res.data.data || []
        } else {
          this.$message.error(res.data.msg)
        }
      } catch (error) {
      }
    },
    async changeSwitch (item) {
      try {
        // console.log(item)
        await this.$api.risk.rules.enable({ id: item.pid, enable: item.enable ? 1 : 0 })
      } catch (error) {
      }
    },
    planClick (item) {
      this.info = item
      this.dialog.update = true
    },
    closePlan () {
      this.$refs.appform.resetFields()
      this.dialog.update = false
    }
  }
}
</script>
<style lang="scss">
.el-card__header {
  display: flex;
  background-color: #F3F4F8;
  height: 80px;
  align-items: center;
}

.box-card {
  min-width: 80%;
  min-height: 75%;
  margin: 8%;
}

.rule-div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ruleLevel-div {
  display: inline-block;
  padding: 0px 6px;
  height: 18px;
  margin-left: 9px;
  border-radius: 8px;

  color: white;
}

.rule-switch {
  margin-left: auto;
}

.rule-desc-div {
  padding: 10px 0px;
  height: 70px;
}

.rule-btn-div {
  display: flex;
  justify-content: space-evenly;
}

.coloc_rule1 {
  background-color: red;
}

.coloc_rule2 {
  background-color: rgb(245, 154, 35);
}

.coloc_rule3 {
  background-color: rgb(85, 130, 243);
}
</style>
