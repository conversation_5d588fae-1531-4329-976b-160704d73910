<template>
  <fe-page-main show-back-btn>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <el-form-item label="价格日期">
            <el-date-picker
              v-model="searchPriceDate"
              type="daterange"
              start-placeholder="价格开始日期"
              end-placeholder="价格结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="getList"
              @clear="setDefaultSearchPriceDate"
            />
          </el-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-area">
      <div id="chart_area" class="w-full h-[400px]" />
    </div>
    <div class="partition-area">
      <div class="my-3 mx-5">
        <el-segmented v-model="type" :options="options" @change="changeType" />
      </div>
      <table-inner v-loading="loading.list" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" :column-option="columnOptionList" show-set-columns @change-page-num="changePageNum" @change-page-size="changePageSize">
        <template #btn-inner>
          <el-button v-if="type === '0'" :loading="loading.update" type="primary" @click="updateData">
            更新数据
          </el-button>
          <el-button v-if="type === '1'" type="primary" @click="addPrice">
            新增价格
          </el-button>
        </template>
      </table-inner>
    </div>
    <edit-price ref="editPrice" />
  </fe-page-main>
</template>

<script>
import { basePageListMixin } from '@fero/commons-vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import EditPrice from '@/views/risk/priceSupervision/components/edit-price.vue'
import { defaultsDeep } from 'lodash'

export default {
  components: { EditPrice },
  mixins: [basePageListMixin],
  data () {
    return {
      /** @public */
      changeRouter: false,
      /** @public */
      createdInit: false,
      baseSearchForm: {},
      searchForm: {
        startDate: '',
        endDate: ''
      },
      type: '0',
      options: [
        { label: '监测价格', value: '0' },
        { label: '录入价格', value: '1' }
      ],
      columnOptionList: [
        { label: '产品大类编码', prop: 'productType', formatter: this.isEffective, minWidth: 180 },
        { label: '产品大类名称', prop: 'productName', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '牌号', prop: 'shopSign', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '钢厂', prop: 'steelFactory', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '价格', prop: 'price', formatter: this.$utils.tableMoneyFormat, minWidth: 180 },
        { label: '价格日期', prop: 'fdate', formatter: this.$utils.tableDateFormat, minWidth: 180 }
      ]
    }
  },
  computed: {
    searchPriceDate: {
      get () {
        return [this.searchForm.startDate, this.searchForm.endDate]
      },
      set (newValue) {
        if (newValue) {
          this.searchForm.startDate = newValue[0]
          this.searchForm.endDate = newValue[1]
        } else {
          this.searchForm.startDate = ''
          this.searchForm.endDate = ''
        }
      }
    }
  },
  created () {
    this.baseSearchForm = this.$route.query
    this.searchForm = {
      startDate: dayjs().subtract(1, 'month').startOf('day').format('YYYY-MM-DD'),
      endDate: dayjs().endOf('day').format('YYYY-MM-DD')
    }
    this.recoverSearch()
  },
  mounted () {
    // this.chart = echarts.init(document.getElementById('chart_area'))
    this.initChart()
  },
  methods: {
    search () {
      this.changeType()
    },
    addPrice () {
      this.$refs.editPrice.init(this.baseSearchForm).then(() => {
        this.getList()
      })
    },
    updateData () {
      this.loading.update = true
      this.$api.risk.price.updateData().then(result => {
        this.$utils.resultBaseMessage(result)
        this.changeType()
      }).finally(() => {
        this.loading.update = false
      })
    },
    setDefaultSearchPriceDate () {
      this.searchForm.startDate = dayjs().subtract(1, 'month').startOf('day').format('YYYY-MM-DD')
      this.searchForm.endDate = dayjs().endOf('day').format('YYYY-MM-DD')
    },
    initChart () {
      console.log(this.searchForm)
      const params = {
        ...this.searchForm,
        ...this.baseSearchForm,
        type: this.type
      }
      this.$api.risk.price.getPriceLine(params).then(result => {
        const data = defaultsDeep(result.data.data, { goodsPriceMap: {} })
        const lineChart = echarts.init(document.getElementById('chart_area'))
        lineChart.clear()
        const colorArray = [
          '#FAA000', '#00A0E9', '#FF6347', '#32CD32', '#8A2BE2',
          '#FFD700', '#4682B4', '#FF69B4', '#00CED1', '#8B4513'
        ]

        const series = Object.keys(data.goodsPriceMap).map((brand, index) => ({
          name: brand,
          type: 'line',
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            color: colorArray[index % colorArray.length] // 按顺序使用颜色数组中的颜色
          },
          data: data.goodsPriceMap[brand].map(parseFloat)
        }))
        lineChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#FAA000'
              }
            }
          },
          legend: {
            data: Object.keys(data.goodsPriceMap)
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: data.goodsDateList
            }
          ],
          yAxis: [
            {
              type: 'value',
              scale: true,
              min: function (value) {
                return value.min - 100
              },
              max: function (value) {
                return value.max + 100
              }
            }
          ],
          series
        })
      })
    },
    changeType () {
      this.initChart()
      this.getList()
    },
    /** @public */
    getList () {
      this.loading.list = true
      const params = {
        ...this.searchForm,
        ...this.baseSearchForm,
        type: this.type
      }
      this.$api.risk.price.getPriceList(params).then(res => {
        this.tableList = res.data.data || {}
      }).finally(() => {
        this.loading.list = false
      })
    },
    dataStructure (ls) {
      let it = null
      let data = []
      data = ls.map((v, i) => {
        if (v) {
          it = v
        } else {
          v = it
        }
        return v
      })
      return data
    }
    // 补全插值
    //     interpolate(data) {
    //     var prev = data[0];
    //     return data.map(function(point, idx, arr) {
    //         if (point[1] === null && idx > 0) {
    //             // 使用前一个点的值进行插值
    //             var diffX = point[0] - prev[0];
    //             var diffY = point[1] - prev[1];
    //             return [point[0], prev[1] + diffY / diffX * (point[0] - prev[0])];
    //         } else {
    //             // 记录当前点作为下一个点的前一个点
    //             prev = point;
    //             return point;
    //         }
    //     });
    // }
  }
}
</script>

<style>

</style>
