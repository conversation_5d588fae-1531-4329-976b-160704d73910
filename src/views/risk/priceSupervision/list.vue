<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <fe-form-item label="产品大类名称">
            <el-input v-model="searchForm.productName" placeholder="" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="牌号">
            <el-input v-model="searchForm.shopSign" placeholder="" />
          </fe-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="信息明细" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" :column-option="columnOptionList" show-set-columns @change-page-num="changePageNum" @change-page-size="changePageSize">
        <template #btn-inner>
        </template>
        <template #table-columns-after>
          <el-table-column fixed="right" label="操作" width="240px">
            <template #default="scope">
              <el-link type="primary" @click="addDialog(scope.row)">
                新增价格
              </el-link>
              <table-link :to="{ name: 'priceSupervisionDetail', query: { pid: scope.row.pid, productType: scope.row.productType, productName: scope.row.productName, shopSign: scope.row.shopSign } }" type="primary">
                价格趋势
              </table-link>
            </template>
          </el-table-column>
        </template>
      </table-inner>
    </div>
    <edit-price ref="editPrice" />
  </fe-page-main>
</template>
<script>
import { basePageListMixin } from '@fero/commons-vue'
import EditPrice from '@/views/risk/priceSupervision/components/edit-price.vue'

export default {
  name: 'BusinessProjectInitiation',
  components: { EditPrice },
  mixins: [basePageListMixin],
  data () {
    return {
      tableList: {},
      columnOptionList: [
        { label: '产品大类编码', prop: 'productType', formatter: this.isEffective, minWidth: 180 },
        { label: '产品大类名称', prop: 'productName', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '牌号', prop: 'shopSign', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '价格（永钢）', prop: 'marketPrice', formatter: this.$utils.tableMoneyFormat, minWidth: 180 },
        { label: '价格（录入）', prop: 'manualPrice', formatter: this.$utils.tableMoneyFormat, minWidth: 180 }
      ],
      loading: {
        list: false,
        submit: false
      }
    }
  },
  created () {
  },
  methods: {
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.risk.price.pageList(this.searchForm).then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
          if (this.tableList.records && this.tableList.records.length > 0) {
            this.tableList.records.forEach(item => {
              this.formatterName(item)
            })
          }
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    formatterName (row) {
      // 设置默认值
      row.systematicName = '-'

      // 生成 systematicName
      row.systematicName = row.productTypeName ? row.productTypeName : ''
      row.systematicName += row.productDetailName ? `_${row.productDetailName}` : ''
      row.systematicName += row.gradeSeriesName ? `_${row.gradeSeriesName}` : ''

      // 如果 systematicName 为空，设置为默认值
      if (!row.systematicName) {
        row.systematicName = '-'
      }
    },
    addDialog (row) {
      this.$refs.editPrice.init(row).then(() => {
        this.getList()
      })
    }
  }
}
</script>

<style lang="scss">

</style>
