<script setup>
import { ref, unref } from 'vue'
import { api } from '@/assets/lib/api'
import { utils } from '@/assets/lib/utils'
import { inputNumberAttr } from '@/assets/lib/misc'
import AmountInput from "@/components/amount-input.vue";

let editResolve = null
let editReject = null
const visible = ref(false)
const loading = ref(false)
const appForm = ref({})
const init = (info) => {
  return new Promise((resolve, reject) => {
    editResolve = resolve
    editReject = reject
    appForm.value = {
      pid: info.pid,
      productType: info.productType,
      productName: info.productName,
      shopSign: info.shopSign
    }
    visible.value = true
  })
}
const appFormRef = ref()
const confirm = async () => {
  appFormRef.value.validate().then(() => {
    loading.value = true
    api.risk.price.addPrice(unref(appForm)).then(result => {
      utils.resultBaseMessage(result)
      visible.value = false
      editResolve(result)
      editResolve = null
      editReject = null
    }).finally(() => {
      loading.value = false
    })
  })
}
const close = () => {
  appFormRef.value.resetFields()
  if (editReject) {
    editReject()
    editResolve = null
    editReject = null
  }
}
const rules = {
  price: [{ required: true, message: '请输入价格' }],
  fdate: [{ required: true, message: '请选择日期', trigger: 'change' }]
}
defineExpose({
  init
})
</script>

<template>
  <dialog-inner v-model="visible" :loading="loading" destroy-on-close append-to-body width="30%" title="新增价格" @submit="confirm" @close="close">
    <div class="form-area">
      <el-form ref="appFormRef" :model="appForm" :rules="rules">
        <div class="form-inner">
          <fe-form-item label="产品大类编码" prop="productType">
            <detail-input :model-value="$utils.isEffectiveCommon(appForm.productType)" />
          </fe-form-item>
          <fe-form-item label="产品大类名称" prop="productName">
            <detail-input :model-value="$utils.isEffectiveCommon(appForm.productName)" />
          </fe-form-item>
          <fe-form-item label="牌号" prop="shopSign">
            <detail-input :model-value="$utils.isEffectiveCommon(appForm.shopSign)" />
          </fe-form-item>
          <fe-form-item label="价格" prop="price">
            <amount-input v-model="appForm.price" placeholder="请输入价格" />
<!--            <el-input-number v-model="appForm.price" v-bind="inputNumberAttr" placeholder="请输入价格" />-->
          </fe-form-item>
          <fe-form-item label="日期" prop="fdate">
            <el-date-picker v-model="appForm.fdate" type="date" placeholder="请选择日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
          </fe-form-item>
        </div>
      </el-form>
    </div>
  </dialog-inner>
</template>

<style lang="scss">

</style>
