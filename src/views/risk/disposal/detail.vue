<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import AttachmentList from '@/components/attachment-list.vue'
import DetailInput from '@/components/base/detail-input.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'

export default {
  components: { FeFormItem, DetailInput, AttachmentList, FePageMain },
  data () {
    return {
      pid: this.$route.query.pid,
      baseForm: {},
      columnOptionList: [
        { label: '产品大类', prop: 'productTypeName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '钢种系列', prop: 'gradeSeriesName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '产品细分类', prop: 'productName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '牌号', prop: 'shopSign', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '货物重量(吨)', prop: 'weight', minWidth: 150, formatter: this.$utils.tableWeightFormat },
        { label: '货物单价(元)', prop: 'orderUnitPrice', minWidth: 150, formatter: this.$utils.tableMoneyFormat },
        { label: '货物金额(元)', prop: 'amount', minWidth: 150, formatter: this.$utils.tableMoneyFormat },
        { label: '销售合同编号', prop: 'orderCode2', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '销售货物行号', prop: 'orderLineNo2', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '销售合同年月', prop: 'belongAgreementTime', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '销售合同旬', prop: 'belongAgreementTendays', minWidth: 150, formatter: this.$utils.tableStatusFormat('decade') },
        { label: '质押日期', prop: 'pledgeDate', minWidth: 150, formatter: this.$utils.tableDateFormat }
      ],
      loading: {
        detail: false
      }
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    getDetail () {
      this.loading.detail = true
      this.$api.risk.disposal.detail({ pid: this.pid }).then(result => {
        this.baseForm = result.data.data || {}
      }).finally(() => {
        this.loading.detail = false
      })
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <div class="partition-area">
      <div class="form-area">
        <el-form :model="baseForm">
          <div class="area-title">
            <p class="title">
              处置记录
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="货物处置编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseForm.disposalCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="处置方式">
                  <detail-input :model-value="$utils.statusFormat(baseForm.disposalMethod, 'DISPOSAL_METHOD')" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="处置日期">
                  <detail-input :model-value="$utils.dateFormat(baseForm.disposalDate)" />
                </fe-form-item>
              </el-col>
              <el-col :span="24">
                <fe-form-item label="处置说明">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseForm.disposalDesc)" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="table-title">
            <table-inner title="处置明细" :table-data="baseForm.goodsDisposalRelList" :column-option="columnOptionList" :table-footer="false" />
          </div>
          <attachment-list v-model="baseForm" :business-id="pid" business-type="GOODS_DISPOSAL" readonly />
        </el-form>
      </div>
    </div>
  </fe-page-main>
</template>

<style lang="scss">

</style>
