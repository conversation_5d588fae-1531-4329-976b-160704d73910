<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import { basePageListMixin } from '@fero/commons-vue'
import FeSearchInner from '@/components/base/fe-search-inner.vue'
import { utils } from '@/assets/lib/utils'

export default {
  components: { FeSearchInner, FePageMain },
  mixins: [basePageListMixin],
  data () {
    return {
      columnOptionList: [
        { label: '货物处置编号', prop: 'disposalCode', minWidth: 150, formatter: this.$utils.isEffective },
        // { label: '客户名称', prop: 'buyerName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '处置方式', prop: 'disposalMethod', minWidth: 150, formatter: this.$utils.tableStatusFormat('DISPOSAL_METHOD') },
        // { label: '业务单据编号', prop: 'ladingCode', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '处置日期', prop: 'disposalDate', minWidth: 150, formatter: this.$utils.tableDateFormat },
        { label: '状态', prop: 'status', minWidth: 150, formatter: this.$utils.tableStatusFormat('GOODS_DISPOSAL_STATUS') },
        // { label: '操作人', prop: 'updateBy', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '创建时间', prop: 'createTime', minWidth: 150, formatter: this.$utils.tableDateFormat }
      ]
    }
  },
  computed: {
    searchDisposalTime: utils.computedDate('disposalTimeStart', 'disposalTimeEnd')
  },
  methods: {
    /** @public */
    async getList () {
      try {
        this.loading.list = true
        const res = await this.$api.risk.disposal.pageList(this.searchForm)
        this.tableList = res.data.data || {}
      } finally {
        this.loading.list = false
      }
    },
    del (row) {
      this.$confirm('确认删除该处置？', '确认删除').then(() => {
        this.$api.risk.disposal.del(row.pid).then(result => {
          this.$utils.resultBaseMessageV2(result)
          this.getList()
        })
      })
    }
  }
}
</script>

<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <fe-form-item label="货物处置编号">
            <el-input v-model="searchForm.disposalCode" placeholder="请输入货物处置编号" />
          </fe-form-item>
        </el-col>
        <el-col :span="12">
          <fe-form-item label="处置日期">
            <el-date-picker
              v-model="searchDisposalTime"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="shortcuts"
              format="YYYY-MM-DD"
              value-format="x"
              placeholder="请选择处置日期"
            />
          </fe-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" title="信息明细" :table-data="tableList.records" show-set-columns :column-option="columnOptionList" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe @change-page-num="changePageNum" @change-page-size="changePageSize">
        <template #btn-inner>
          <el-button type="primary" @click="$router.push({ name: 'riskDisposalAdd' })">
            新增
          </el-button>
        </template>
        <template #table-columns-after>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <table-link :to="{ name: 'riskDisposalDetail', query: { pid: scope.row.pid } }">
                详情
              </table-link>
              <table-link v-if="scope.row.status === '1'" type="primary" :to="{ name: 'riskDisposalEdit', query: { pid: scope.row.pid } }">
                编辑
              </table-link>
              <el-link v-if="scope.row.status === '1'" type="danger" @click="del(scope.row)">
                删除
              </el-link>
            </template>
          </el-table-column>
        </template>
      </table-inner>
    </div>
  </fe-page-main>
</template>

<style lang="scss">

</style>
