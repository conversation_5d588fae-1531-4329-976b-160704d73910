<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import DetailInput from '@/components/base/detail-input.vue'
import { datePickAttr } from '@/assets/lib/misc'
import AttachmentList from '@/components/attachment-list.vue'
import { cloneDeep, defaultsDeep, find } from 'lodash'
import PickGoodsList from '@/views/pledge/components/pick-goods-list.vue'

export default {
  components: { PickGoodsList, AttachmentList, DetailInput, FeFormItem, FePageMain },
  data () {
    return {
      pid: this.$route.query.pid,
      baseForm: {
        goodsDisposalRelList: []
      },
      ladingList: [],
      rules: {
        ladingCode: this.$rulesToolkit.createRules({ name: '发货申请编号', required: true, trigger: 'change' }),
        disposalMethod: this.$rulesToolkit.createRules({ name: '处置方式', required: true, trigger: 'change' }),
        disposalDate: this.$rulesToolkit.createRules({ name: '处置日期', required: true, trigger: 'change' })
      },
      loading: {
        submit: false,
        detail: false
      },
      columnOptionList: [
        { label: '产品大类', prop: 'productTypeName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '钢种系列', prop: 'gradeSeriesName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '产品细分类', prop: 'productName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '牌号', prop: 'shopSign', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '货物重量(吨)', prop: 'weight', minWidth: 150, formatter: this.$utils.tableWeightFormat },
        { label: '货物单价(元)', prop: 'orderUnitPrice', minWidth: 150, formatter: this.$utils.tableMoneyFormat },
        { label: '货物金额(元)', prop: 'amount', minWidth: 150, formatter: this.$utils.tableMoneyFormat },
        { label: '销售合同编号', prop: 'orderCode2', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '销售货物行号', prop: 'orderLineNo2', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '销售合同年月', prop: 'belongAgreementTime', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '销售合同旬', prop: 'belongAgreementTendays', minWidth: 150, formatter: this.$utils.tableStatusFormat('decade') },
        { label: '质押日期', prop: 'pledgeDate', minWidth: 150, formatter: this.$utils.tableDateFormat }
      ]
    }
  },
  computed: {
    datePickAttr () {
      return datePickAttr
    }
  },
  created () {
    this.getLadingList()
    if (this.pid) {
      this.getDetail()
    }
  },
  methods: {
    getLadingList () {
      this.$api.warehouse.pickUpGoods.getList().then(result => {
        this.ladingList = result.data.data
      })
    },
    ladingCodeChange (ladingCode) {
      const data = this.ladingList.find(item => item.ladingCode === ladingCode)
      this.baseForm.ladingId = data.pid
      this.baseForm.buyerName = data.buyerName
      this.baseForm.buyerCode = data.buyerCode
      this.getDisposalInfo()
    },
    getDisposalInfo () {
      this.$api.warehouse.pickUpGoods.getInfoList({ ladingCode: this.baseForm.ladingCode }).then(result => {
        const disposalInfo = result.data.data
        this.baseForm.goodsDisposalRelList = disposalInfo ?? []
      })
    },
    getDetail () {
      this.loading.detail = true
      this.$api.risk.disposal.detail({ pid: this.pid }).then(result => {
        this.baseForm = defaultsDeep(result.data.data, { goodsDisposalRelList: [] })
      }).finally(() => {
        this.loading.detail = false
      })
    },
    save () {
      this.$refs.baseForm.validate().then(() => {
        const formData = cloneDeep(this.baseForm)
        this.loading.submit = true
        let api = this.$api.risk.disposal.save
        if (formData.pid) {
          api = this.$api.risk.disposal.update
        }
        api(formData).then(result => {
          if (result.data.data) {
            this.baseForm.pid = result.data.data
            this.pid = result.data.data
          }
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    submit () {
      this.$refs.baseForm.validate().then(() => {
        const formData = cloneDeep(this.baseForm)
        this.loading.submit = true
        this.$api.risk.disposal.submit(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    remove (index) {
      this.baseForm.goodsDisposalRelList.splice(index, 1)
    },
    pickGoods () {
      this.$refs.pickGoodsList.init().then((res = []) => {
        res.forEach(item => {
          if (!find(this.baseForm.goodsDisposalRelList, { pid: item.pid })) {
            this.baseForm.goodsDisposalRelList.push(item)
          }
        })
      })
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <template #btn-inner>
      <el-button :loading="loading.submit" type="primary" @click="save">
        保存
      </el-button>
      <el-button :loading="loading.submit" type="primary" @click="submit">
        提交
      </el-button>
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form ref="baseForm" :model="baseForm" :rules="rules">
          <div class="area-title">
            <p class="title">
              处置记录
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="货物处置编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseForm.disposalCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="处置方式" prop="disposalMethod">
                  <el-select v-model="baseForm.disposalMethod" clearable placeholder="请选择处置方式">
                    <el-option v-for="item in $utils.getEnableDictStatus('DISPOSAL_METHOD')" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="处置日期" prop="disposalDate">
                  <el-date-picker v-model="baseForm.disposalDate" v-bind="datePickAttr" placeholder="请选择计处置日期" />
                </fe-form-item>
              </el-col>
              <el-col :span="24">
                <fe-form-item label="处置说明" prop="disposalDesc">
                  <el-input v-model="baseForm.disposalDesc" type="textarea" :rows="3" placeholder="请输入处置说明" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="table-title">
            <table-inner title="处置明细" :table-data="baseForm.goodsDisposalRelList" :column-option="columnOptionList" :table-footer="false">
              <template #btn-inner>
                <el-button type="primary" @click="pickGoods()">
                  选择质押货物
                </el-button>
              </template>
              <template #table-columns-after>
                <el-table-column label="操作" fixed="right" width="100">
                  <template #default="scope">
                    <el-link type="danger" @click="remove(scope.$index)">
                      删除
                    </el-link>
                  </template>
                </el-table-column>
              </template>
            </table-inner>
          </div>
          <attachment-list v-model="baseForm" :business-id="pid" business-type="GOODS_DISPOSAL" />
        </el-form>
      </div>
    </div>
    <pick-goods-list ref="pickGoodsList" />
  </fe-page-main>
</template>

<style lang="scss">

</style>
