<script setup>
import { ref } from 'vue'
import api from '@/assets/lib/api'

const props = defineProps({
  type: {
    type: String,
    default: 'plan'
  }
})

const visible = ref(false)
const infoForm = ref({})
const init = (pid) => {
  let detailApi = api.repayment.plan.info
  if (props.type === 'use') {
    detailApi = api.repayment.plan.getInfoByUseId
  }
  detailApi({ pid }).then((res) => {
    infoForm.value = res.data.data ?? {}
    visible.value = true
  })
}
const closeDialog = () => {
  infoForm.value = {}
}
defineExpose({
  init
})
</script>

<template>
  <dialog-inner v-model="visible" :show-submit="false" title="还款情况" cancel-title="关闭" @close="closeDialog">
    <div class="form-area">
      <div class="detail-inner">
        <el-row :gutter="20">
<!--          <el-col :span="12">-->
<!--            <fe-form-item label="融资金额（元）">-->
<!--              <detail-input :model-value="$utils.moneyFormat(infoForm.financingAmount)" />-->
<!--            </fe-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <fe-form-item label="待结清本金（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.unpaidPrincipal)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="已结清本金（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.repaidPrincipal)" />
            </fe-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <fe-form-item label="违约金合计（元）">-->
<!--              <detail-input :model-value="$utils.moneyFormat(infoForm.totalOverdueFees)" />-->
<!--            </fe-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <fe-form-item label="待收违约金（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.unpaidOverdueFees)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="已收违约金（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.repaidOverdueFees)" />
            </fe-form-item>
          </el-col>
<!--          <el-col :span="12">
            <fe-form-item label="利息合计（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.totalInterest)" />
            </fe-form-item>
          </el-col>-->
          <el-col :span="12">
            <fe-form-item label="待收融资利息（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.unpaidFinancingInterest)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="已收融资利息（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.repaidFinancingInterest)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="待收宽限利息（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.unpaidGraceInterest)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="已收宽限利息（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.repaidGraceInterest)" />
            </fe-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <fe-form-item label="服务费合计（元）">-->
<!--              <detail-input :model-value="$utils.moneyFormat(infoForm.totalServiceFee)" />-->
<!--            </fe-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <fe-form-item label="待收服务费（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.unpaidServiceFee)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="已收服务费（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.repaidServiceFee)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="已收总金额（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.totalRepaymentAmount)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="可发货金额（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.availableDeliveryAmount)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="已发货金额（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.deliveredAmount)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="已锁定金额（元）">
              <detail-input :model-value="$utils.moneyFormat(infoForm.lockedDeliveryAmount)" />
            </fe-form-item>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--<div class="m-4">-->
    <!--  <el-table :data="infoForm.receiptRecordVOList" border stripe max-height="200px">-->
    <!--    <el-table-column label="还款记录编号" prop="recordCode" :formatter="$utils.isEffective" min-width="100" show-overflow-tooltip />-->
    <!--    <el-table-column label="还款类型" prop="claimType" :formatter="$utils.tableStatusFormat('CLAIM_TYPE')" min-width="80" show-overflow-tooltip />-->
    <!--    <el-table-column label="实际还款日期" prop="actualRepaymentDate" :formatter="$utils.tableDateFormat" min-width="100" show-overflow-tooltip />-->
    <!--    <el-table-column label="还款金额（元）" prop="totalAmount" :formatter="$utils.tableMoneyFormat" min-width="80" show-overflow-tooltip />-->
    <!--  </el-table>-->
    <!--</div>-->
  </dialog-inner>
</template>

<style lang="scss">

</style>
