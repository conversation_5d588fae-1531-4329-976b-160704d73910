<script setup>
import { ref } from 'vue'
import { cloneDeep } from 'lodash'

const dialog = ref({
  planDetail: false
})
const productionPlanDetailList = ref([])
const init = (row) => {
  const item = cloneDeep(row)
  // const list = []
  /* if (item.lineList) {
    item.lineList.forEach(detail => {
      detail.prodClassName = item.prodClassName
      detail.gradeSeriesName = item.gradeSeriesName
      list.push(detail)
    })
  } */
  productionPlanDetailList.value = item.lineList
  dialog.value.planDetail = true
}
defineExpose({
  init
})
</script>

<template>
  <dialog-inner v-model="dialog.planDetail" title="生产计划明细" :show-submit="false" cancel-title="关闭" append-to-body>
    <div class="form-area">
      <div class="form-inner">
        <el-table :data="productionPlanDetailList">
          <!-- <el-table-column prop="prodClassName" label="大类名称" :formatter="$utils.isEffective" show-overflow-tooltip />
          <el-table-column prop="gradeSeriesName" label="钢种名称" :formatter="$utils.isEffective" show-overflow-tooltip />-->
          <el-table-column label="牌号" prop="brand" :formatter="$utils.isEffective" show-overflow-tooltip min-width="120" />
          <el-table-column label="直径（mm）" prop="diameter" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip min-width="120" />
          <el-table-column label="长度（mm）" prop="length" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip min-width="120" />
          <el-table-column label="预报量（吨）" prop="submitQuantity" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip min-width="120" />
          <el-table-column label="价格（元）" prop="price" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip min-width="120" />
        </el-table>
      </div>
    </div>
  </dialog-inner>
</template>

<style lang="scss">

</style>
