<script setup>
import { ref, watch } from 'vue'
import PlanDetailDialog from '@/views/loan/components/plan-detail-dialog.vue'

const props = defineProps({
  productionPlanList: {
    type: Array,
    default: () => []
  },
  isInit: {
    type: Boolean,
    default: true
  }
})
const emptyText = ref('请先选择提前打款信息')
const planDetailDialogRef = ref()
const viewPlan = (row) => {
  planDetailDialogRef.value.init(row)
}
watch(() => props.isInit, () => {
  if (props.isInit) {
    emptyText.value = '请先选择提前打款信息'
  } else {
    emptyText.value = '未查询到相关的生产计划信息'
  }
})
</script>

<template>
  <div class="area-title">
    <p class="title">
      生产计划信息
    </p>
  </div>
  <div class="detail-inner">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-table :data="props.productionPlanList" :empty-text="emptyText">
          <el-table-column prop="dataType" label="数据类型" :formatter="$utils.tableStatusFormat('dataType')" show-overflow-tooltip />
          <el-table-column prop="submitType" label="计划类型" :formatter="$utils.tableStatusFormat('submitType')" show-overflow-tooltip />
          <el-table-column prop="prodClassCode" label="产品大类编码" :formatter="$utils.isEffective" show-overflow-tooltip />
          <el-table-column prop="prodClassName" label="产品大类名称" :formatter="$utils.isEffective" show-overflow-tooltip />
          <el-table-column prop="gradeSeriesCode" label="钢种系列编码" :formatter="$utils.isEffective" show-overflow-tooltip />
          <el-table-column prop="gradeSeriesName" label="钢种系列名称" :formatter="$utils.isEffective" show-overflow-tooltip />
          <el-table-column prop="discountQuantit" label="折后协议量（吨）" :formatter="$utils.tableWeightFormat" show-overflow-tooltip />
          <el-table-column prop="submitPlanQuantit" label="已提报量（吨）" :formatter="$utils.tableWeightFormat" show-overflow-tooltip />
          <el-table-column label="操作" fixed="right" width="100">
            <template #default="scope">
              <el-link type="primary" @click="viewPlan(scope.row)">
                查看明细
              </el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
  </div>
  <plan-detail-dialog ref="planDetailDialogRef" />
</template>

<style lang="scss">

</style>
