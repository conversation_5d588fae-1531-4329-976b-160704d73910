<script setup>
import DetailInput from '@/components/base/detail-input.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import { ref } from 'vue'

const prePayInfo = ref({})
const dialogVisible = ref(false)
const init = (info) => {
  prePayInfo.value = info
  dialogVisible.value = true
}
defineExpose({
  init
})
</script>

<template>
  <dialog-inner v-model="dialogVisible" title="提前打款详情" :show-submit="false" cancel-title="关闭" append-to-body>
    <div class="form-area">
      <div class="detail-inner">
        <el-row :gutter="20">
          <el-col :span="12">
            <fe-form-item label="是否满足延期资金">
              <detail-input :model-value="$utils.trueFalseFormant(prePayInfo.isDelay)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="是否满足提前打款">
              <detail-input :model-value="$utils.trueFalseFormant(prePayInfo.isAdvancePay)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="客户确认状态">
              <detail-input :model-value="$utils.trueFalseFormant(prePayInfo.confirmStatus)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="提前打款要求比例">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.advancePayPercent)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="月协议量（吨）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.monthAgreementAmount)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="该旬理论协议量（吨）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.tendaysAgreementAmount)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="该旬理论折后量（吨）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.tendaysAmountAfterFlood)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="该旬理论所需资金（元）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.tendaysPrice)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="上一旬未完成量（吨）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.lastTendaysLackAmount)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="上一旬延期所需资金（元）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.lastTendaysLackPrice)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="该旬提前发货量(吨)">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.tendaysAdvanceDelivery)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="该旬超发资金（元）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.tendaysOverPrice)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="理论提前打款资金（元）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.advancePayAmount)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="客户锁定账户余额（元）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.balance)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="客户账户可用余额（元）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.balanceAmount)"/>
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="实际应打款金额（元）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.actualFund)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="满足延期货款所缺金额（元）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.lackAmount)" />
            </fe-form-item>
          </el-col>
          <el-col :span="12">
            <fe-form-item label="享受提前奖励应补款金额（元）">
              <detail-input :model-value="$utils.moneyFormat(prePayInfo.differFund)" />
            </fe-form-item>
          </el-col>
        </el-row>
      </div>
    </div>
  </dialog-inner>
</template>

<style lang="scss">

</style>
