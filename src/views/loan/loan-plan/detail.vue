<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import DetailInput from '@/components/base/detail-input.vue'
import { inputNumberAttr } from '@/assets/lib/misc'
import AttachmentList from '@/components/attachment-list.vue'

export default {
  computed: {
    inputNumberAttr () {
      return inputNumberAttr
    }
  },
  components: { AttachmentList, DetailInput, FeFormItem, FePageMain },
  data () {
    return {
      pid: '',
      infoForm: {}
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <div class="partition-area">
      <div class="form-area">
        <el-form :model="infoForm">
          <div class="area-title">
            <p class="title">基本信息</p>
          </div>
          <div class="detail-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="放款申请单编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="关联用信申请">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="批复用款金额（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="已放款金额（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="本次申请放款金额(元)">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="用款期限（天）">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12" />
              <el-col :span="12">
                <fe-form-item label="放款方企业">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="放款银行账号">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款方企业">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款银行账号">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="24">
                <fe-form-item label="放款备注">
                  {{ $utils.isEffectiveCommon(infoForm.remark) }}
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">放款确认信息</p>
          </div>
          <div class="detail-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="实际放款金额（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="放款完成时间">
                  <detail-input :model-value="$utils.dateFormat(infoForm.code1)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="支付流水编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.code1)" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <attachment-list v-model="infoForm" :business-id="pid" business-type="" readonly />
        </el-form>
      </div>
    </div>
  </fe-page-main>
</template>

<style>

</style>
