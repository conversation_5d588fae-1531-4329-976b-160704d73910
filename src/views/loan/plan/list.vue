<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeSearchInner from '@/components/base/fe-search-inner.vue'
import { basePageListMixin } from '@fero/commons-vue'
import { utils } from '@/assets/lib/utils'
import RepayDetailDialog from '@/views/loan/components/repay-detail-dialog.vue'

export default {
  components: { RepayDetailDialog, FeSearchInner, FePageMain },
  mixins: [basePageListMixin],
  data () {
    return {
      columnOptionList: [
        {
          label: '融资申请编号',
          prop: 'financingCode',
          minWidth: 150,
          formatter: this.$utils.isEffective,
          slotName: 'financingCode'
        },
        // {
        //   label: '还款计划编号',
        //   prop: 'repaymentPlanCode',
        //   minWidth: 150,
        //   formatter: this.$utils.isEffective
        // },
        {
          label: '融资企业',
          prop: 'custName',
          minWidth: 200,
          formatter: this.$utils.isEffective
        },
        {
          label: '资金方名称',
          prop: 'finanicalCompanyName',
          minWidth: 200,
          formatter: this.$utils.isEffective
        },
        {
          label: '融资金额（元）',
          prop: 'financingAmount',
          minWidth: 150,
          formatter: this.$utils.tableMoneyFormat
        },
        // {
        //   label: '可发货金额(元)',
        //   prop: 'availableDeliveryAmount',
        //   minWidth: 150,
        //   formatter: this.$utils.tableMoneyFormat
        // },
        // {
        //   label: '已发货金额(元)',
        //   prop: 'deliveredAmount',
        //   minWidth: 150,
        //   formatter: this.$utils.tableMoneyFormat
        // },
        // {
        //   label: '已锁定金额(元)',
        //   prop: 'lockedDeliveryAmount',
        //   minWidth: 150,
        //   formatter: this.$utils.tableMoneyFormat
        // },
        {
          label: '已收本金(元)',
          prop: 'repaidPrincipal',
          minWidth: 150,
          formatter: this.$utils.tableMoneyFormat
        },
        {
          label: '待收本金(元)',
          prop: 'unpaidPrincipal',
          minWidth: 150,
          formatter: this.$utils.tableMoneyFormat
        },
        // {
        //   label: '已还利息(元)',
        //   prop: 'repaidFinancingInterest',
        //   minWidth: 150,
        //   formatter: this.$utils.tableMoneyFormat
        // },
        // {
        //   label: '待收利息(元)',
        //   prop: 'unpaidFinancingInterest',
        //   minWidth: 150,
        //   formatter: this.$utils.tableMoneyFormat
        // },
        {
          label: '还款方式',
          prop: 'repaymentType',
          minWidth: 150,
          formatter: this.$utils.tableStatusFormat('PRODUCT_REPAYMENT_METHOD')
        },
        {
          label: '融资利率',
          prop: 'financingRatio',
          minWidth: 120,
          formatter: this.$utils.tableMoneyFormat
        },
        {
          label: '实际放款日期',
          prop: 'disbursementDate',
          minWidth: 150,
          formatter: this.$utils.tableDateFormat
        },
        {
          label: '还款到期日期',
          prop: 'repaymentDate',
          minWidth: 150,
          formatter: this.$utils.tableDateFormat
        },
        {
          label: '还款状态',
          prop: 'status',
          minWidth: 150,
          formatter: this.$utils.tableStatusFormat('REPAYMENT_STATUS')
        }
      ]
    }
  },
  computed: {
    searchRepaymentDueDate: utils.computedDate(
      'repaymentStartDate',
      'repaymentEndDate'
    ),
    searchLoanDate: utils.computedDate(
      'disbursementStartDate',
      'disbursementEndDate'
    ),
    dynamicRoute () {
      return (id) => {
        const routeData1 = this.$router.resolve({
          name: 'loanCreditDetail',
          query: { pid: id }
        })
        return routeData1.href
      }
    }
  },
  methods: {
    /** @public */
    async getList () {
      try {
        this.loading.list = true
        const res = await this.$api.repayment.plan.pageList(this.searchForm)
        this.tableList = res.data.data || {}
      } finally {
        this.loading.list = false
      }
    },
    setRowColor ({ row }) {
      if (row.status === 'OVERDUE') {
        return 'danger-row'
      }
    },
    preview (row) {
      this.$refs.repayDetailRef.init(row.pid)
    }
    // windowOpen (id) {
    //   const routeData1 = this.$router.resolve({
    //     name: 'loanCreditDetail',
    //     query: { pid: id }
    //   })
    //   window.open(routeData1.href, '_blank')
    // }
  }
}
</script>

<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner
        :search-form="searchForm"
        @submit-search="search"
        @clear-search="clearSearch"
      >
        <el-col :span="6">
          <fe-form-item label="融资企业">
            <el-input
              v-model="searchForm.custName"
              placeholder="请输入融资企业"
            />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="融资申请编号">
            <el-input
              v-model="searchForm.financingCode"
              placeholder="请输入融资申请编号"
            />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="还款状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择还款状态"
            >
              <el-option
                v-for="item in $utils.getEnableDictStatus('REPAYMENT_STATUS')"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </fe-form-item>
        </el-col>
        <!-- <el-col :span="6">
          <fe-form-item label="放款申请编号">
            <el-input
              v-model="searchForm.loanApplicationNumber"
              placeholder="请输入放款申请编号"
            />
          </fe-form-item>
        </el-col> -->
        <!--        <el-col :span="6">-->
        <!--          <el-form-item label="实际放款日期">-->
        <!--            <el-date-picker-->
        <!--                v-model="searchLoanDate"-->
        <!--                type="daterange"-->
        <!--                range-separator="至"-->
        <!--                start-placeholder="开始日期"-->
        <!--                end-placeholder="结束日期"-->
        <!--                format="YYYY-MM-DD"-->
        <!--                value-format="x"-->
        <!--            />-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <!--        <el-col :span="6">-->
        <!--          <el-form-item label="到期还本日">-->
        <!--            <el-date-picker-->
        <!--              v-model="searchRepaymentDueDate"-->
        <!--              type="daterange"-->
        <!--              range-separator="至"-->
        <!--              start-placeholder="开始日期"-->
        <!--              end-placeholder="结束日期"-->
        <!--              format="YYYY-MM-DD"-->
        <!--              value-format="x"-->
        <!--            />-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner
        v-loading="loading.list"
        :table-data="tableList.records"
        show-set-columns
        :column-option="columnOptionList"
        :page-num="tableList.current"
        :page-size="tableList.size"
        :total="tableList.total"
        :row-class-name="setRowColor"
        stripe
        title="信息明细"
        @change-page-num="changePageNum"
        @change-page-size="changePageSize"
      >
        <template #financingCode="scope">
          <el-link type="primary" :href="dynamicRoute(scope.row.financingId)" target="_blank">
            {{ scope.row.financingCode }}
          </el-link>
        </template>
        <template #table-columns-after>
          <el-table-column fixed="right" label="操作" width="120">
            <template #default="scope">
              <table-link :to="{ name: 'loanPlanDetail', query: { pid: scope.row.pid } }" type="primary">
                总览
              </table-link>
            </template>
          </el-table-column>
        </template>
      </table-inner>
    </div>
    <repay-detail-dialog ref="repayDetailRef" />
  </fe-page-main>
</template>

<style>
</style>
