<script>
import FePageMain from '@/components/base/fe-page-main.vue'

export default {
  components: { FePageMain },
  data () {
    return {
      infoForm: {},
      dialog: {
        info: false
      },
      feeDetail: []
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    getDetail () {
      this.$api.repayment.plan.info({ pid: this.$route.query.pid }).then(result => {
        this.infoForm = result.data.data ?? {}
      })
    },
    viewInfo (info) {
      this.dialog.info = true
      this.feeDetail = [{}]
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <div class="partition-area">
      <div class="form-area">
        <el-form>
          <div class="area-title">
            <p class="title">
              还款总览
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <!--          <el-col :span="12">-->
              <!--            <fe-form-item label="融资金额（元）">-->
              <!--              <detail-input :model-value="$utils.moneyFormat(infoForm.financingAmount)" />-->
              <!--            </fe-form-item>-->
              <!--          </el-col>-->
              <el-col :span="12">
                <fe-form-item label="待收本金（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.unpaidPrincipal)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="已收本金（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.repaidPrincipal)" />
                </fe-form-item>
              </el-col>
              <!--          <el-col :span="12">-->
              <!--            <fe-form-item label="违约金合计（元）">-->
              <!--              <detail-input :model-value="$utils.moneyFormat(infoForm.totalOverdueFees)" />-->
              <!--            </fe-form-item>-->
              <!--          </el-col>-->
              <!--          <el-col :span="12">
                          <fe-form-item label="利息合计（元）">
                            <detail-input :model-value="$utils.moneyFormat(infoForm.totalInterest)" />
                          </fe-form-item>
                        </el-col>-->
              <el-col :span="12">
                <fe-form-item label="待收融资利息（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.unpaidFinancingInterest)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="已收融资利息（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.repaidFinancingInterest)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="待收宽限利息（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.unpaidGraceInterest)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="已收宽限利息（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.repaidGraceInterest)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="待收违约金（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.unpaidOverdueFees)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="已收违约金（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.repaidOverdueFees)" />
                </fe-form-item>
              </el-col>
              <!--          <el-col :span="12">-->
              <!--            <fe-form-item label="服务费合计（元）">-->
              <!--              <detail-input :model-value="$utils.moneyFormat(infoForm.totalServiceFee)" />-->
              <!--            </fe-form-item>-->
              <!--          </el-col>-->
              <el-col :span="12">
                <fe-form-item label="待收服务费（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.unpaidServiceFee)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="已收服务费（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.repaidServiceFee)" />
                </fe-form-item>
              </el-col>
<!--              <el-col :span="12">-->
<!--                <fe-form-item label="已收总金额（元）">-->
<!--                  <detail-input :model-value="$utils.moneyFormat(infoForm.totalRepaymentAmount)" />-->
<!--                </fe-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="12">-->
<!--                <fe-form-item label="可发货金额（元）">-->
<!--                  <detail-input :model-value="$utils.moneyFormat(infoForm.availableDeliveryAmount)" />-->
<!--                </fe-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="12">-->
<!--                <fe-form-item label="已发货金额（元）">-->
<!--                  <detail-input :model-value="$utils.moneyFormat(infoForm.deliveredAmount)" />-->
<!--                </fe-form-item>-->
<!--              </el-col>-->
<!--              <el-col :span="12">-->
<!--                <fe-form-item label="已锁定金额（元）">-->
<!--                  <detail-input :model-value="$utils.moneyFormat(infoForm.lockedDeliveryAmount)" />-->
<!--                </fe-form-item>-->
<!--              </el-col>-->
            </el-row>
          </div>
          <!--<div class="table-title">-->
          <!--  <table-inner :table-data="infoForm.repaymentDetailList" :table-footer="false" title="还款计划">-->
          <!--    <el-table-column prop="principalAmount" label="本金金额(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />-->
          <!--    <el-table-column prop="financingInterest" label="融资利息(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />-->
          <!--    <el-table-column prop="graceInterest" label="宽限利息(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />-->
          <!--    <el-table-column prop="overdueFee" label="违约金(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />-->
          <!--    <el-table-column prop="actualLoanDate" label="放款日期" :formatter="$utils.tableDateFormat" show-overflow-tooltip />-->
          <!--    <el-table-column prop="actualRepaymentDate" label="还款日期" :formatter="$utils.tableDateFormat" show-overflow-tooltip />-->
          <!--    <el-table-column prop="interestDays" label="计息天数" :formatter="$utils.isEffective" show-overflow-tooltip />-->
          <!--  </table-inner>-->
          <!--</div>-->
          <div class="table-title">
            <table-inner :table-data="infoForm.repaymentApplyVOList" :table-footer="false" title="还款明细">
              <el-table-column prop="repaymentApplyCode" label="还款申请编号" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="practicalRepaymentDate" label="实际还款日期" :formatter="$utils.tableDateFormat" show-overflow-tooltip />
              <el-table-column prop="practicalReleaseAmount" label="实收还款总额(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />
              <el-table-column prop="principalAmount" label="实收本金金额(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />
              <el-table-column prop="financingInterestAmount" label="实收融资利息(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />
              <el-table-column prop="graceInterestAmount" label="实收宽限利息(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />
              <el-table-column prop="overdueFee" label="实收违约金(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />
            </table-inner>
          </div>
          <div class="table-title">
            <table-inner :table-data="infoForm.receiptRecordVOList" :table-footer="false" title="收款明细">
              <el-table-column label="收款记录编号" prop="recordCode" :formatter="$utils.isEffective" min-width="100" show-overflow-tooltip />
              <el-table-column label="收款类型" prop="claimType" :formatter="$utils.tableStatusFormat('CLAIM_TYPE')" min-width="80" show-overflow-tooltip />
              <el-table-column label="收款日期" prop="actualRepaymentDate" :formatter="$utils.tableDateFormat" min-width="100" show-overflow-tooltip />
              <el-table-column label="收款金额（元）" prop="totalAmount" :formatter="$utils.tableMoneyFormat" min-width="80" show-overflow-tooltip />
            </table-inner>
          </div>
        </el-form>
      </div>
    </div>
    <dialog-inner v-model="dialog.info" :show-submit="false" title="费用明细" width="30%" cancel-title="关闭">
      <el-table :data="feeDetail">
        <el-table-column prop="feeType" label="收费类型" :formatter="$utils.isEffective" />
        <el-table-column prop="feeAmount" label="收费方式" :formatter="$utils.isEffective" />
        <el-table-column prop="feeDescription" label="值/率" :formatter="$utils.isEffective" />
      </el-table>
    </dialog-inner>
  </fe-page-main>
</template>

<style>

</style>
