<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <fe-form-item label="质押协议编号">
            <el-input v-model="searchForm.pledgeCode" placeholder="请输入质押协议编号" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="销售协议编号">
            <el-input v-model="searchForm.agreementCode" placeholder="请输入销售协议编号" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="销售协议名称">
            <el-input v-model="searchForm.agreementName" placeholder="请输入销售协议名称" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option v-for="item in $utils.getEnableDictStatus('PLEDGE_STATUS')" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </fe-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner ref="tableList" v-loading="loading.list" title="信息明细" :table-data="tableList.records" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe :column-option="columnOption" show-set-columns class="single-select-table" @change-page-num="changePageNum" @change-page-size="changePageSize" @selection-change="handleSelectionChange">
        <template #contractSigningStatus="scope">
          <contract-table-link type="pledge" :scope="scope" />
        </template>
        <template #btn-inner>
          <el-button type="primary" @click="$router.push({ name: 'loanPledgeAgreementAdd' })">
            新增
          </el-button>
          <el-button :disabled="!removeSelection || ['invalid', 'expired', 'pendingEffect'].includes(removeSelection.status)" type="primary" @click="release">
            解除
          </el-button>
        </template>
        <template #table-columns-before>
          <el-table-column type="selection" />
        </template>
        <template #table-columns-after>
          <el-table-column fixed="right" label="操作" width="160">
            <template #default="scope">
              <!--<el-link v-if="['WAIT_SIGN'].includes(scope.row.contractSigningStatus)" type="primary" @click="sign(scope.row)">
                发起签署
              </el-link>-->
              <table-link :to="{ name: 'loanPledgeAgreementDetail', query: { pid: scope.row.pid } }">
                详情
              </table-link>
            </template>
          </el-table-column>
        </template>
      </table-inner>
      <create-contract-start ref="contractStart" use-default-completion />
    </div>
  </fe-page-main>
</template>

<script>

import FePageMain from '@/components/base/fe-page-main.vue'
import FeSearchInner from '@/components/base/fe-search-inner.vue'
import { basePageListMixin } from '@fero/commons-vue'
import CreateContractStart from '@/views/contract/components/create-contract-start.vue'
import ContractTableLink from '@/components/contract-table-link.vue'
export default {
  components: { ContractTableLink, CreateContractStart, FePageMain, FeSearchInner },
  mixins: [basePageListMixin],
  data () {
    return {
      searchForm: {},
      tableList: {
        records: []
      },
      removeSelection: null,
      loading: {
        list: false
      },
      columnOption: [
        { label: '质押协议编号', prop: 'pledgeCode', formatter: this.$utils.isEffective, minWidth: 120 },
        { label: '出质企业', prop: 'pledgorName', formatter: this.$utils.isEffective, minWidth: 120 },
        { label: '质权企业', prop: 'pledgeeName', formatter: this.$utils.isEffective, minWidth: 120 },
        { label: '销售协议编号', prop: 'agreementCode', formatter: this.$utils.isEffective, minWidth: 120 },
        { label: '销售协议名称', prop: 'agreementName', formatter: this.$utils.isEffective, minWidth: 120 },
        { label: '状态', prop: 'status', formatter: this.$utils.tableStatusFormat('PLEDGE_STATUS'), minWidth: 120 },
        { label: '合同状态', prop: 'contractSigningStatus', formatter: this.$utils.tableStatusFormat('PC_CONTRACT_SIGN'), minWidth: 120, slotName: 'contractSigningStatus' },
        { label: '创建时间', prop: 'createTime', formatter: this.$utils.tableDateTimeFormat, minWidth: 140 }
      ]
    }
  },
  created () {},
  methods: {
    handleSelectionChange (selection) {
      if (!selection || selection.length === 0) {
        return false
      }
      const lastSelected = selection[selection.length - 1]
      this.removeSelection = lastSelected
      if (selection.length > 1) {
        this.$refs.tableList.tableEvent('clearSelection')
        this.$refs.tableList.tableEvent('toggleRowSelection', lastSelected, true)
      }
    },
    getList () {
      this.loading.list = true
      this.$api.pledgeManagement.agreement.pageList(this.searchForm).then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    release () {
      if (!this.removeSelection.pid) {
        this.$message.error('请选择一个协议进行解除')
        return false
      }
      this.$confirm('确认解除该协议？', '确认解除').then(() => {
        this.$api.pledgeManagement.agreement.release(this.removeSelection.pid).then(result => {
          this.$utils.resultBaseMessageV2(result)
          this.getList()
        })
      })
    }
    /* sign (row) {
      this.$refs.contractStart.init({ businessId: row.pid, businessCode: row.pledgeCode, businessType: 'pledge' })
    } */
  }
}
</script>

<style scoped lang="scss">

</style>
