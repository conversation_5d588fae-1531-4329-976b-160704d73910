<template>
  <fe-page-main show-back-btn>
    <div class="partition-area">
      <div class="form-area">
        <el-form ref="baseFormRef" :model="baseForm">
          <div class="area-title">
            <p class="title">
              基本信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <fe-form-item label="质押协议编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseForm.pledgeCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="销售协议名称（编号）">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseForm.agreementName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="出质企业">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseForm.pledgorName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="质权企业">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseForm.pledgeeName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="生效日期">
                  <detail-input :model-value="$utils.dateFormat(baseForm.effectDate)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="到期日期">
                  <detail-input :model-value="$utils.dateFormat(baseForm.expirationDate)" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <sale-detail :pid="baseForm.agreementId" title="销售协议信息" />
          <div class="area-title">
            <p class="title">
              质押协议信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <fe-form-item label="是否签署协议">
                  <el-radio-group v-model="baseForm.contractSigningOption" disabled>
                    <el-radio v-for="item in $utils.getEnableDictStatus('PC_SIGNING_METHOD')" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <attachment-list v-model="baseForm" :business-id="pid" business-type="PLEDGE_AGREEMENT" readonly />
          <div class="area-title">
            <p class="title">
              备注信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="24">
                <el-form-item prop="remark">
                  {{ $utils.isEffectiveCommon(baseForm.remark) }}
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
  </fe-page-main>
</template>

<script>

import FePageMain from '@/components/base/fe-page-main.vue'
import DetailInput from '@/components/base/detail-input.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import AttachmentList from '@/components/attachment-list.vue'
import { cloneDeep } from 'lodash'
import SaleDetail from '@/views/agreement/composables/sale-detail.vue'

export default {
  components: { SaleDetail, AttachmentList, FeFormItem, DetailInput, FePageMain },
  data () {
    return {
      pid: this.$route.query.pid,
      baseForm: {
        agreementId: ''
      },
      loading: {
        detail: false
      }
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    getDetail () {
      this.loading.datail = true
      this.$api.pledgeManagement.agreement.detail(this.pid).then(result => {
        this.baseForm = result.data.data || {}
      }).finally(() => {
        this.loading.detail = false
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
