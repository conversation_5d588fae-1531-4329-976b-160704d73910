<template>
  <fe-page-main show-back-btn>
    <template #btn-inner>
      <el-button type="primary" :loading="loading.submit" @click="submit">
        保存并提交
      </el-button>
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form ref="baseFormRef" v-auto-placeholder :model="baseForm" :rules="rules">
          <div class="area-title">
            <p class="title">
              基本信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="质押协议编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseForm.pledgeCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="出质企业" prop="pledgorId">
                  <el-select
                    v-model="baseForm.pledgorId"
                    clearable
                    placeholder="请选择"
                    @change="changeCoreCompany"
                  >
                    <el-option v-for="item in coreCompanyList" :key="item.pid" :label="item.companyName" :value="item.pid" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="销售协议名称（编号）" prop="agreementCode">
                  <el-select
                    v-model="baseForm.agreementCode"
                    filterable
                    clearable
                    placeholder="请选择"
                    @change="changeAgreement"
                  >
                    <el-option v-for="item in agreementList" :key="item.pid" :label="`${item.agreementName}（${item.agreementCode}）`" :value="item.agreementCode" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="质权企业" prop="pledgeeId">
                  <el-select
                    v-model="baseForm.pledgeeId"
                    clearable
                    placeholder="请选择"
                    @change="changeFinancialCompany"
                  >
                    <el-option v-for="item in financialCompanyList" :key="item.pid" :label="item.companyName" :value="item.pid" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="生效日期" prop="effectDate">
                  <el-date-picker v-model="baseForm.effectDate" v-bind="datePickAttr" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="到期日期" prop="expirationDate">
                  <el-date-picker v-model="baseForm.expirationDate" v-bind="datePickAttr" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <sale-detail :pid="baseForm.agreementId" title="销售协议信息" />
          <div class="area-title">
            <p class="title">
              质押协议信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <fe-form-item label="是否签署协议" prop="contractSigningOption">
                  <el-radio-group v-model="baseForm.contractSigningOption">
                    <el-radio v-for="item in $utils.getEnableDictStatus('PC_SIGNING_METHOD')" :key="item.value" :value="Number(item.value)">
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <attachment-list v-model="baseForm" :business-id="pid" business-type="PLEDGE_AGREEMENT" />
          <div class="area-title">
            <p class="title">
              备注信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="24">
                <el-form-item prop="remark">
                  <el-input v-model="baseForm.remark" type="textarea" :rows="3" maxlength="200" show-word-limit placeholder="请输入备注信息" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
  </fe-page-main>
</template>

<script>
import FeFormItem from '@/components/base/fe-form-item.vue'
import DetailInput from '@/components/base/detail-input.vue'
import FePageMain from '@/components/base/fe-page-main.vue'
import AttachmentList from '@/components/attachment-list.vue'
import { datePickAttr } from '@/assets/lib/misc'
import { cloneDeep } from 'lodash'
import SaleDetail from '@/views/agreement/composables/sale-detail.vue'

export default {
  components: { SaleDetail, AttachmentList, FePageMain, DetailInput, FeFormItem },
  data () {
    return {
      pid: this.$route.query.pid,
      baseForm: {
        pledgeCode: '',
        contractSigningOption: 0
      },
      coreCompanyList: [],
      financialCompanyList: [],
      agreementList: [],
      rules: {
        agreementCode: this.$rulesToolkit.createRules({ required: true, name: '销售协议名称（编号）', trigger: 'change' }),
        pledgorId: this.$rulesToolkit.createRules({ required: true, name: '出质企业', trigger: 'change' }),
        pledgeeId: this.$rulesToolkit.createRules({ required: true, name: '质权企业', trigger: 'change' }),
        effectDate: this.$rulesToolkit.createRules({ required: true, name: '生效日期', trigger: 'change' }),
        expirationDate: this.$rulesToolkit.createRules({ required: true, name: '到期日期', trigger: 'change' }),
        contractSigningOption: this.$rulesToolkit.createRules({ required: true, name: '是否签署协议', trigger: 'change' })
      },
      loading: {
        submit: false
      }
    }
  },
  computed: {
    datePickAttr () {
      return datePickAttr
    }
  },
  created () {
    this.getCoreCompanyList()
    this.getFinancialCompanyList()
  },
  methods: {
    getAgreementList (buyerCode) {
      this.$api.pledgeManagement.agreement.getAgreementList({ buyerCode }).then(result => {
        this.agreementList = result.data.data || []
      })
    },
    changeAgreement (agreementCode) {
      const data = this.agreementList.find(item => item.agreementCode === agreementCode)
      this.baseForm.agreementId = data.pid
      this.baseForm.agreementName = data.agreementName
    },
    // 出质人列表
    getCoreCompanyList () {
      const companyParams = {
        type: 'CUSTOMER',
        status: 'EFFECTIVE',
        enabled: 1
      }
      this.$api.charge.getCompanyList(companyParams).then(result => {
        this.coreCompanyList = result.data.data || []
      }).finally(() => {
      })
    },
    // 质权人列表
    getFinancialCompanyList () {
      const companyParams = {
        type: 'FUND',
        enabled: 1
      }
      this.$api.charge.getCompanyList(companyParams).then(result => {
        this.financialCompanyList = result.data.data || []
      }).finally(() => {
      })
    },
    changeCoreCompany (pid) {
      const data = this.coreCompanyList.find(item => item.pid === pid)
      this.baseForm.pledgorName = data.companyName
      this.baseForm.pledgorCode = data.socialCreditCode

      this.baseForm.agreementCode = ''
      this.baseForm.agreementId = ''
      this.baseForm.agreementName = ''
      this.getAgreementList(data.companyCode)
    },
    changeFinancialCompany (pid) {
      const data = this.financialCompanyList.find(item => item.pid === pid)
      this.baseForm.pledgeeName = data.companyName
      this.baseForm.pledgeeCode = data.socialCreditCode
    },
    submit () {
      this.$refs.baseFormRef.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.baseForm)
        this.$api.pledgeManagement.agreement.save(formData).then(result => {
          if (result.data.data) {
            this.$message.success('保存成功')
            this.$router.back()
          }
        }).finally(() => {
          this.loading.submit = false
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
