<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import DetailInput from '@/components/base/detail-input.vue'
import AttachmentList from '@/components/attachment-list.vue'
import OperationLog from '@/components/operation-log.vue'
import { datePickAttr, inputNumberIntAttr } from '@/assets/lib/misc'
import dayjs from 'dayjs'
import { cloneDeep, defaultsDeep } from 'lodash'
import PrePayDetail from '@/views/loan/components/pre-pay-detail.vue'
import PlanList from '@/views/loan/components/plan-list.vue'
import ProfilesList from '@/views/creditManagement/components/profiles-list.vue'
import BigNumber from 'bignumber.js'
import { Document, QuestionFilled } from '@element-plus/icons-vue'
import AmountInput from '@/components/amount-input.vue'
import { statusCheck } from '@/assets/lib/status-check'

export default {
  components: { QuestionFilled, AmountInput, Document, ProfilesList, PlanList, PrePayDetail, OperationLog, AttachmentList, DetailInput, FeFormItem, FePageMain },
  data () {
    return {
      pid: '',
      infoForm: {
      },
      diffPlanAmount: 0,
      totalPlanAmount: 0,
      customerRequiredAmount: 0,
      remainingRequiredAmount: 0,
      originalInfoForm: {},
      creditInfo: {},
      prePayInfo: {},
      productionPlanList: [],
      logList: [],
      loading: {
        confirm: false,
        detail: false
      },
      rules: {
        planUseDate: this.$rulesToolkit.createRules({ name: '申请打款日期', required: true, trigger: 'change' }),
        approvedAmount: this.$rulesToolkit.createRules({ name: '建议融资金额（元）', required: true }),
        customerRequiredAmount: this.$rulesToolkit.createRules({ name: '用户打款金额（元）', required: true }),
        usagePeriod: this.$rulesToolkit.createRules({ name: '用款期限（天）', required: true }),
        contractSigningOption: this.$rulesToolkit.createRules({
          name: '是否必须签署协议',
          required: true,
          trigger: 'change'
        })
      },
      canEditContractSigningOption: 1
    }
  },
  computed: {
    datePickAttr () {
      return datePickAttr
    },
    inputNumberIntAttr () {
      return inputNumberIntAttr
    },
    expectedRepaymentDate () {
      return Number(dayjs(this.infoForm.planUseDate).add(this.infoForm.usagePeriod || 0, 'day').format('x'))
    }
  },
  created () {
    this.pid = this.$route.query.pid
    this.getDetail()
  },
  methods: {
    // calculateCustomerRequiredAmount () {
    //   // 用户打款金额 = 申请融资金额 / 融资比例 - 申请融资金额
    //   // 申请融资金额
    //   const planUseAmount = new BigNumber(this.infoForm.approvedAmount)
    //   // 融资比例
    //   const rate = new BigNumber(this.creditInfo.financingRatio ?? 100).div(100)
    //   this.infoForm.customerRequiredAmount = planUseAmount.div(rate).minus(planUseAmount).decimalPlaces(0, 0).toNumber()
    // },
    reject () {
      this.$prompt('确认驳回此受理？', '确认驳回', {
        inputType: 'textarea',
        inputPlaceholder: '请填写驳回原因',
        customClass: 'require-confirm',
        inputValidator: (value) => {
          if (!value) {
            return '请填写驳回原因'
          }
        }
      }).then(({ value }) => {
        this.loading.submit = true
        this.$api.loan.apply.reject({ pid: this.pid, remark: value }).then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    acceptance () {
      this.$refs.infoForm.validate().then(() => {
        // 用户打款金额 > 申请融资金额 / 融资比例 - 申请融资金额
        // 用户打款金额
        const customerRequiredAmount = new BigNumber(this.infoForm.customerRequiredAmount)
        // 申请融资金额
        const planUseAmount = new BigNumber(this.infoForm.approvedAmount)
        // 融资比例
        const rate = new BigNumber(this.creditInfo.financingRatio ?? 100).div(100)
        if (!customerRequiredAmount.isGreaterThanOrEqualTo(planUseAmount.div(rate).minus(planUseAmount))) {
          return this.$message.error('用户打款金额小于申请融资金额 / 融资比例 - 申请融资金额')
        }
        const formData = cloneDeep(this.infoForm)
        this.loading.confirm = true
        this.$api.loan.apply.accept(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        }).finally(() => {
          this.loading.confirm = false
        })
      })
    },
    viewPrePayment () {
      this.$refs.prePayRef.init(this.prePayInfo)
    },
    getDetail () {
      this.loading.detail = true
      this.$api.loan.apply.detail(this.pid).then(result => {
        this.infoForm = defaultsDeep(result.data.data, { productDocuments: [] })
        this.originalInfoForm = cloneDeep(this.infoForm)
        this.logList = this.infoForm.operatingRecordEntityList ?? []
        this.getCreditDetail(this.infoForm.applyCreditId).then(() => {
          this.canEditContractSigningOption = this.creditInfo.productSnapshot.useContractSigningConfig
          if (this.infoForm.contractSigningOption === null || this.infoForm.contractSigningOption === undefined) {
            this.infoForm.contractSigningOption = this.creditInfo.productSnapshot.useContractSigningOption
          }
        })
        if (this.infoForm.prepaymentId) {
          this.getPrePayDetail(this.infoForm.prepaymentId).then(() => {
            this.getProductionPlanList()
          })
        }
        statusCheck(this.infoForm, 'status', 'pendingAccept', { name: 'loanCreditDetail', query: { pid: this.pid } })
      }).finally(() => {
        this.loading.detail = false
      })
    },
    getPrePayDetail (pid) {
      return new Promise((resolve, reject) => {
        if (!pid) return reject(new Error('pid is required'))
        this.$api.agreement.payment.info(pid).then(result => {
          this.prePayInfo = result.data.data || {}
          resolve(result.data.data)
        }).catch(e => {
          reject(e)
        })
      })
    },
    getProductionPlanList () {
      return new Promise((resolve, reject) => {
        this.$api.agreement.plan.list({
          custNo: this.infoForm.useCompanyCode,
          submitAmountMonths: this.infoForm.planDate,
          submitAmountTendays: this.infoForm.prepaymentPeriod
        }).then(result => {
          this.productionPlanList = result.data.data || []
          resolve(result.data.data ?? [])
        }).catch(error => {
          reject(error)
        })
      })
    },
    getCreditDetail (pid) {
      return new Promise((resolve, reject) => {
        if (!pid) {
          return reject(new Error('pid is required'))
        }
        Promise.all([
          this.$api.creditManagement.credit.info(pid)
          // this.$api.creditManagement.quotaViewing.info(pid)
        ]).then(result => {
          this.creditInfo = defaultsDeep(result[0].data.data, { productSnapshot: {} })
          // const remainingAmount = new BigNumber(result[1].data.data?.remainingAmount ?? 0)
          /* if (this.infoForm.pid && this.$route.name !== 'loanApplyAdd' && pid === this.originalInfoForm?.applyCreditId) {
            remainingAmount = remainingAmount.plus(this.infoForm.planUseAmount ?? 0)
          } */
          // this.creditInfo.remainingAmount = remainingAmount.toNumber()
          resolve(this.creditInfo)
        }).catch(error => {
          reject(error)
        })
      })
    },
    calculateMaxAmount () {
      const actualFund = new BigNumber(this.prePayInfo?.actualFund ?? 0)
      this.infoForm.approvedAmount = BigNumber.max(new BigNumber(this.infoForm.approvedAmount || 0), 0).toNumber()
      this.infoForm.customerRequiredAmount = BigNumber.max(new BigNumber(this.infoForm.customerRequiredAmount || 0), 0).toNumber()
      this.infoForm.remainingRequiredAmount = BigNumber.max(new BigNumber(this.infoForm.remainingRequiredAmount || 0), 0).toNumber()
      this.prePayInfo.balance = BigNumber.max(new BigNumber(this.prePayInfo.balance || 0), 0).toNumber()

      const balanceAmount = new BigNumber(this.prePayInfo.balance)
      // 融资比例
      const rate = new BigNumber(this.creditInfo.financingRatio ?? 100).div(100)
      const approvedAmount = BigNumber.min(actualFund.plus(balanceAmount).multipliedBy(rate), actualFund)
      this.infoForm.approvedAmount = approvedAmount.decimalPlaces(2, BigNumber.ROUND_UP)
      const remainingAmount = BigNumber.max(actualFund.plus(balanceAmount).minus(approvedAmount).minus(balanceAmount), 0)
      this.infoForm.remainingRequiredAmount = remainingAmount.decimalPlaces(2, BigNumber.ROUND_UP)

      const requiredAmount = balanceAmount.plus(remainingAmount)
      this.infoForm.customerRequiredAmount = requiredAmount.decimalPlaces(2, BigNumber.ROUND_UP)
      const totalPlanAmount = approvedAmount.plus(remainingAmount)
      this.totalPlanAmount = totalPlanAmount.decimalPlaces(2, BigNumber.ROUND_UP)

      const requiredAmount1 = BigNumber.max(approvedAmount.div(rate).minus(approvedAmount), 0)
      this.customerRequiredAmount = requiredAmount1.decimalPlaces(2, BigNumber.ROUND_UP)
      const remainingAmount1 = BigNumber.max(requiredAmount1.minus(balanceAmount), 0)
      this.remainingRequiredAmount = remainingAmount1.decimalPlaces(2, BigNumber.ROUND_UP)

      const diffPlanAmount = actualFund.minus(totalPlanAmount)
      this.diffPlanAmount = diffPlanAmount.decimalPlaces(2, BigNumber.ROUND_UP)
    },
    calculateRequiredAmount () {
      console.info('calculateRequiredAmount')
      // 确保为非负值，直接修正原始值
      const actualFund = new BigNumber(this.prePayInfo?.actualFund ?? 0)
      this.infoForm.approvedAmount = BigNumber.max(new BigNumber(this.infoForm.approvedAmount || 0), 0).toNumber()
      this.infoForm.customerRequiredAmount = BigNumber.max(new BigNumber(this.infoForm.customerRequiredAmount || 0), 0).toNumber()
      this.infoForm.remainingRequiredAmount = BigNumber.max(new BigNumber(this.infoForm.remainingRequiredAmount || 0), 0).toNumber()
      this.prePayInfo.balance = BigNumber.max(new BigNumber(this.prePayInfo.balance || 0), 0).toNumber()

      const approvedAmount = new BigNumber(this.infoForm.approvedAmount)
      const remainingAmount = new BigNumber(this.infoForm.remainingRequiredAmount)
      const balanceAmount = new BigNumber(this.prePayInfo.balance)

      const requiredAmount = balanceAmount.plus(remainingAmount)
      this.infoForm.customerRequiredAmount = requiredAmount.decimalPlaces(2, BigNumber.ROUND_UP)
      const totalPlanAmount = approvedAmount.plus(remainingAmount)
      this.totalPlanAmount = totalPlanAmount.decimalPlaces(2, BigNumber.ROUND_UP)

      // 融资比例
      const rate = new BigNumber(this.creditInfo.financingRatio ?? 100).div(100)
      console.info('rate:' + rate)
      const requiredAmount1 = BigNumber.max(approvedAmount.div(rate).minus(approvedAmount), 0)
      console.info('approvedAmount:' + approvedAmount)
      console.info('requiredAmount1:' + requiredAmount1)
      this.customerRequiredAmount = requiredAmount1.decimalPlaces(2, BigNumber.ROUND_UP)
      const remainingAmount1 = BigNumber.max(requiredAmount1.minus(balanceAmount), 0)
      this.remainingRequiredAmount = remainingAmount1.decimalPlaces(2, BigNumber.ROUND_UP)

      const diffPlanAmount = actualFund.minus(totalPlanAmount)
      this.diffPlanAmount = diffPlanAmount.decimalPlaces(2, BigNumber.ROUND_UP)
    },
    remainingRequiredAmountIns () {
      // const amount = new BigNumber(this.remainingRequiredAmount ?? 0)
      // if (amount.isEqualTo(new BigNumber(0))) {
      //   return
      // }
      this.infoForm.remainingRequiredAmount = this.remainingRequiredAmount
    },
    setDisableDate (data) {
      return dayjs().startOf('day').isAfter(data)
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <template #btn-inner>
      <el-button :loading="loading.submit" type="danger" @click="reject">
        驳回受理
      </el-button>
      <el-button :loading="loading.confirm" type="primary" @click="acceptance">
        提交
      </el-button>
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form ref="infoForm" v-auto-placeholder :model="infoForm" :rules="rules">
          <div class="area-title">
            <p class="title">
              申请企业信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="企业名称">
                  <detail-input :model-value="infoForm.useCompanyName" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="统一社会信用代码">
                  <detail-input :model-value="infoForm.useSocialCreditCode" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              融资申请信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="申请融资编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.useCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="planUseDate" label="申请打款日期">
                  <el-date-picker v-model="infoForm.planUseDate" v-bind="datePickAttr" :disabled-date="setDisableDate" />
                  <!--<detail-input :model-value="$utils.dateFormat(infoForm.planUseDate)" />-->
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="授信资金方">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.financialCompanyName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item
                  label="关联授信申请" is-link :can-jump="!!infoForm.applyCreditId"
                  :route-info="{ name: 'creditManagementApplicationDetail', query: { pid: infoForm.applyCreditId } }"
                >
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.applyCreditCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="生产计划年月">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.planDate)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="生产计划旬">
                  <detail-input :model-value="$utils.statusFormat(infoForm.planPeriod, 'decade')" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="prepaymentId" label="提前打款信息" for="other">
                  <template #label>
                    <div class="flex">
                      <span>提前打款信息</span>
                      <el-link :disabled="!infoForm.prepaymentId" type="primary" class="ml-2" @click="viewPrePayment">
                        <el-tooltip content="点击查看" placement="right" effect="light">
                          <el-icon>
                            <Document />
                          </el-icon>
                        </el-tooltip>
                      </el-link>
                    </div>
                  </template>
                  <detail-input custom>
                    <template #custom>
                      {{ $utils.isEffectiveCommon(infoForm.prepaymentDate) }}
                      <span v-if="infoForm.prepaymentPeriod">（{{
                        $utils.statusFormat(infoForm.prepaymentPeriod, 'decade')
                      }})</span>
                    </template>
                  </detail-input>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="实际应打款金额（元）">
                  <template #label>
                    <div class="flex items-center">
                      <span>实际应打款金额（元）</span>
                      <span class="ml-1"> - 当前打款金额（{{ $utils.moneyFormat(totalPlanAmount) }} 元）</span>
                      <el-tooltip content="建议融资金额 + 需补打款金额" placement="top" effect="light">
                        <el-icon>
                          <el-icon>
                            <QuestionFilled />
                          </el-icon>
                        </el-icon>
                      </el-tooltip>
                      <span class="ml-1"> = </span>
                      <span class="ml-1">{{ $utils.moneyFormat(diffPlanAmount) }} 元</span>
                    </div>
                  </template>
                  <detail-input :model-value="$utils.moneyFormat(prePayInfo.actualFund)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="客户锁定账户余额（元）">
                  <detail-input :model-value="$utils.moneyFormat(prePayInfo.balance)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="申请融资金额(元)">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.planUseAmount)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="授信额度">
                  <detail-input :model-value="$utils.moneyFormat(creditInfo.creditAmount)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="授信可用额度">
                  <detail-input :model-value="$utils.moneyFormat(creditInfo.remainingAmount)" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              融资批复信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item prop="approvedAmount" label="建议融资金额（元）">
                  <template #label>
                    <div class="flex">
                      <span>建议融资金额（元）</span>
                      <el-link type="primary" @click="calculateMaxAmount">
                        <el-tooltip content="点击计算，最大融资金额。" placement="right" effect="light">
                          <el-icon>
                            <Refresh />
                          </el-icon>
                        </el-tooltip>
                      </el-link>
                    </div>
                  </template>
                  <amount-input
                    v-model="infoForm.approvedAmount" placeholder="请输入建议融资金额"
                    @change="calculateRequiredAmount"
                  />
                  <!--                  <el-input-number v-model="infoForm.approvedAmount" v-bind="inputNumberAttr"/>-->
                </fe-form-item>
              </el-col>
              <!--              <el-col :span="12">-->
              <!--                <fe-form-item prop="customerRequiredAmount" label="用户打款金额（元）">-->
              <!--                  <template #label>-->
              <!--                    <div class="flex">-->
              <!--                      <span>用户打款金额（元）</span>-->
              <!--                      <el-link class="" type="primary" @click="calculateCustomerRequiredAmount">-->
              <!--                        <el-tooltip content="点击计算，自动计算用户最少打款金额。" placement="right" effect="light">-->
              <!--                          <el-icon><Refresh /></el-icon>-->
              <!--                        </el-tooltip>-->
              <!--                      </el-link>-->
              <!--                    </div>-->
              <!--                  </template>-->
              <!--                  <amount-input v-model="infoForm.customerRequiredAmount" placeholder="请输入用户打款金额" />-->
              <!--&lt;!&ndash;                  <el-input-number v-model="infoForm.customerRequiredAmount" v-bind="inputNumberAttr"/>&ndash;&gt;-->
              <!--                </fe-form-item>-->
              <!--              </el-col>-->
              <el-col :span="12">
                <fe-form-item prop="customerRequiredAmount" label="用户打款金额（元）">
                  <template #label>
                    <div class="flex items-center">
                      <span>用户打款金额（元）</span>
                      <el-tooltip
                        content="用户打款金额 = 申请融资金额 / 融资比例 - 申请融资金额" placement="top"
                        effect="light"
                      >
                        <el-icon class="ml-1">
                          <el-icon>
                            <QuestionFilled />
                          </el-icon>
                        </el-icon>
                      </el-tooltip>
                      <span class="ml-1">最低打款金额：{{ $utils.moneyFormat(customerRequiredAmount) }} 元</span>
                    </div>
                  </template>
                  <detail-input :model-value="$utils.moneyFormat(infoForm.customerRequiredAmount)" />
                  <!--                  <el-input-number v-model="infoForm.planUseAmount" v-bind="inputNumberAttr" placeholder="请输入申请融资金额" />-->
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="remainingRequiredAmount" label="需补打款金额（元）">
                  <template #label>
                    <div class="flex items-center">
                      <span>需补打款金额（元）</span>
                      <el-tooltip
                        content="需补打款金额 = 用户打款金额 - 客户锁定账户余额" placement="top"
                        effect="light"
                      >
                        <el-icon class="ml-1">
                          <el-icon>
                            <QuestionFilled />
                          </el-icon>
                        </el-icon>
                      </el-tooltip>
                      <el-link class="" type="danger" :underline="false" @click="remainingRequiredAmountIns">
                        <span class="ml-1">最低需补金额：{{ $utils.moneyFormat(remainingRequiredAmount) }} 元</span>
                      </el-link>
                    </div>
                  </template>
                  <amount-input
                    v-model="infoForm.remainingRequiredAmount" placeholder="请输入需补打款金额"
                    @change="calculateRequiredAmount"
                  />
                  <!--                  <el-input-number v-model="infoForm.customerRequiredAmount" v-bind="inputNumberAttr" placeholder="请输入用户打款金额" />-->
                </fe-form-item>
              </el-col>
              <el-col :span="6">
                <fe-form-item prop="usagePeriod" label="用款期限（天）">
                  <el-input-number v-model="infoForm.usagePeriod" v-bind="inputNumberIntAttr" />
                </fe-form-item>
              </el-col>
              <el-col :span="6">
                <fe-form-item label="预计还款日期">
                  <detail-input :model-value="$utils.dateFormat(expectedRepaymentDate)" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              融资协议信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="是否必须签署协议" prop="contractSigningOption">
                  <el-radio-group v-model="infoForm.contractSigningOption" :disabled="!canEditContractSigningOption">
                    <el-radio
                      v-for="item in $utils.getEnableDictStatus('contractSigningOption')" :key="item.value"
                      :value="item.value"
                    >
                      {{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <!-- 申请资料 -->
          <profiles-list :local-base-table="infoForm.productDocuments" readonly />
          <!-- 生产计划信息 -->
          <plan-list :production-plan-list="productionPlanList" :is-init="!infoForm.prepaymentId" />
          <attachment-list v-model="infoForm" :business-id="pid" business-type="USE_CREDIT_APPLY" readonly />
          <div class="area-title">
            <p class="title">
              备注信息
            </p>
          </div>
          <div class="form-inner">
            <el-form-item>
              {{ $utils.isEffectiveCommon(infoForm.remark) }}
            </el-form-item>
          </div>
          <div class="area-title">
            <p class="title">
              操作记录
            </p>
          </div>
          <div class="form-inner">
            <operation-log :table-list-data="logList" />
          </div>
        </el-form>
      </div>
    </div>
    <!-- 提前打款信息 -->
    <pre-pay-detail ref="prePayRef" />
  </fe-page-main>
</template>

<style>

</style>
