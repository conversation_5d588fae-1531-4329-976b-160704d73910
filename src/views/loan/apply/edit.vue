<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import { datePickAttr } from '@/assets/lib/misc'
import DetailInput from '@/components/base/detail-input.vue'
import AttachmentList from '@/components/attachment-list.vue'
import { cloneDeep, defaultsDeep, find } from 'lodash'
import OperationLog from '@/components/operation-log.vue'
import dayjs from 'dayjs'
import ProfilesList from '@/views/creditManagement/components/profiles-list.vue'
import PrePayDetail from '@/views/loan/components/pre-pay-detail.vue'
import PlanList from '@/views/loan/components/plan-list.vue'
import BigNumber from 'bignumber.js'
import { Document } from '@element-plus/icons-vue'
import AmountInput from '@/components/amount-input.vue'

export default {
  components: { AmountInput, PrePayDetail, ProfilesList, OperationLog, AttachmentList, DetailInput, FeFormItem, FePageMain },
  data () {
    return {
      pid: '',
      originalInfoForm: {},
      infoForm: {
        productDocuments: []
      },
      diffPlanAmount: 0,
      totalPlanAmount: 0,
      customerRequiredAmount: 0,
      remainingRequiredAmount: 0,
      realBalance: 0,
      unpaidPrincipal: 0,
      rules: {
        planUseAmount: this.$rulesToolkit.createRules({ name: '申请融资金额', required: true }),
        financialCompanyId: this.$rulesToolkit.createRules({ name: '授信资金方', required: true, trigger: 'change' }),
        applyCreditId: this.$rulesToolkit.createRules({ name: '关联授信申请', required: true, trigger: 'change' }),
        // prepaymentId: this.$rulesToolkit.createRules({ name: '提前打款信息', required: true, trigger: 'change' }),
        planDate: this.$rulesToolkit.createRules({ name: '生产计划年月', required: true, trigger: 'change' }),
        planPeriod: this.$rulesToolkit.createRules({ name: '生产计划旬', required: true, trigger: 'change' }),
        planUseDate: this.$rulesToolkit.createRules({ name: '申请打款日期', required: true, trigger: 'change' }),
        remainingRequiredAmount: this.$rulesToolkit.createRules({ name: '需补打款金额', required: true })
      },
      capitalCompanyList: [],
      creditList: [],
      prePayList: [],
      prePayInfo: {},
      creditInfo: {
        productSnapshot: {}
      },
      productionPlanList: [],
      logList: [],
      loading: {
        submit: false,
        detail: false
      }
    }
  },
  computed: {
    datePickAttr () {
      return datePickAttr
    }
  },
  created () {
    this.init()
    this.getCapitalCompanyList()
  },
  methods: {
    init () {
      if (this.$route.query.pid) {
        this.pid = this.$route.query.pid
        this.getDetail()
      } else {
        this.infoForm.useCompanyName = this.$route.query.companyName || ''
        this.infoForm.useCompanyCode = this.$route.query.companyCode || ''
        this.infoForm.useCompanyId = this.$route.query.companyId || ''
        this.infoForm.useSocialCreditCode = this.$route.query.socialCreditCode || ''
        this.getPrePayList()
        this.getUnpaidPrincipal()
      }
    },
    getUnpaidPrincipal () {
      return new Promise((resolve, reject) => {
        this.$api.loan.apply.getUnpaidPrincipal({ principalId: this.infoForm.useCompanyId }).then(result => {
          this.unpaidPrincipal = result.data.data ?? 0
          resolve(this.unpaidPrincipal)
        }).catch(e => {
          reject(e)
        })
      })
    },
    getDetail () {
      this.loading.detail = true
      this.$api.loan.apply.detail(this.pid).then(result => {
        this.infoForm = defaultsDeep(result.data.data, { productDocuments: [] })
        this.originalInfoForm = cloneDeep(this.infoForm)
        this.logList = this.infoForm.operatingRecordEntityList ?? []
        this.getUnpaidPrincipal().then(() => {
          this.calculateRequiredAmount()
        })
        this.getPrePayList()
        this.getCreditList()
        this.getCreditDetail(this.infoForm.applyCreditId).then(() => {
          this.calculateRequiredAmount()
        })
        if (this.infoForm.prepaymentId) {
          this.getPrePayDetail(this.infoForm.prepaymentId).then(() => {
            this.getProductionPlanList()
          })
        }
      }).finally(() => {
        this.loading.detail = false
      })
    },
    save (showMessage = true) {
      return new Promise((resolve, reject) => {
        this.$refs.infoForm.validate().then(() => {
          const validateFile = this.infoForm.productDocuments.some(
            (item) => !item.fileJson && item.isRequired === 1
          )
          if (validateFile) {
            return this.$message.error('缺少必须的申请材料,请重新上传')
          }
          if (new BigNumber(this.infoForm.planUseAmount ?? 0).isGreaterThan(this.creditInfo.remainingAmount ?? 0)) {
            return this.$message.error('申请融资金额大于可用授信额度')
          }
          // 用户打款金额 > 申请融资金额 / 融资比例 - 申请融资金额
          // 用户打款金额
          const customerRequiredAmount = new BigNumber(this.infoForm.customerRequiredAmount)
          // 申请融资金额
          const planUseAmount = new BigNumber(this.infoForm.planUseAmount)
          // 融资比例
          const rate = new BigNumber(this.creditInfo.financingRatio ?? 100).div(100)
          if (!customerRequiredAmount.isGreaterThanOrEqualTo(planUseAmount.div(rate).minus(planUseAmount))) {
            return this.$message.error('用户打款金额小于申请融资金额 / 融资比例 - 申请融资金额')
          }
          const formData = cloneDeep(this.infoForm)
          // formData.planDate = Number(dayjs(formData.planDate).format('x'))
          // formData.prepaymentDate = Number(dayjs(formData.prepaymentDate).format('x'))
          this.loading.submit = true
          this.$api.loan.apply.save(formData).then(result => {
            if (showMessage) {
              this.$utils.resultBaseMessage(result)
              this.$router.back()
            }
            if (result.data.data && !this.infoForm.pid) {
              this.infoForm.pid = result.data.data.pid || result.data.data
              this.pid = this.infoForm.pid
            }
            resolve(result)
          }).catch(error => {
            reject(error)
          }).finally(() => {
            this.loading.submit = false
          })
        }).catch(error => {
          reject(error)
        })
      })
    },
    confirm () {
      this.save(false).then(() => {
        this.$api.loan.apply.confirm(this.infoForm.pid).then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        })
      })
    },
    calculateMaxAmount () {
      const actualFund = new BigNumber(this.prePayInfo?.actualFund ?? 0)
      this.infoForm.planUseAmount = BigNumber.max(new BigNumber(this.infoForm.planUseAmount || 0), 0).toNumber()
      this.infoForm.customerRequiredAmount = BigNumber.max(new BigNumber(this.infoForm.customerRequiredAmount || 0), 0).toNumber()
      this.infoForm.remainingRequiredAmount = BigNumber.max(new BigNumber(this.infoForm.remainingRequiredAmount || 0), 0).toNumber()
      this.prePayInfo.balance = BigNumber.max(new BigNumber(this.prePayInfo.balance || 0), 0).toNumber()

      const balanceAmount = new BigNumber(this.prePayInfo.balance)
      const unpaidPrincipal = new BigNumber(this.unpaidPrincipal || 0)
      const realBalance = BigNumber.max(new BigNumber(balanceAmount).minus(unpaidPrincipal), 0)
      this.realBalance = realBalance.decimalPlaces(2, BigNumber.ROUND_UP)
      // 融资比例
      const rate = new BigNumber(this.creditInfo.financingRatio ?? 100).div(100)
      const planUseAmount = BigNumber.min(actualFund.plus(realBalance).multipliedBy(rate), actualFund)
      const remainingAmount = BigNumber.max(actualFund.plus(realBalance).minus(planUseAmount).minus(realBalance), 0)
      this.infoForm.remainingRequiredAmount = remainingAmount.decimalPlaces(2, BigNumber.ROUND_UP)

      const requiredAmount = realBalance.plus(remainingAmount)
      this.infoForm.customerRequiredAmount = requiredAmount.decimalPlaces(2, BigNumber.ROUND_UP)
      const totalPlanAmount = planUseAmount.plus(remainingAmount)
      this.totalPlanAmount = totalPlanAmount.decimalPlaces(2, BigNumber.ROUND_UP)

      const requiredAmount1 = BigNumber.max(planUseAmount.div(rate).minus(planUseAmount), 0)
      this.customerRequiredAmount = requiredAmount1.decimalPlaces(2, BigNumber.ROUND_UP)
      const remainingAmount1 = BigNumber.max(requiredAmount1.minus(realBalance), 0)
      this.remainingRequiredAmount = remainingAmount1.decimalPlaces(2, BigNumber.ROUND_UP)

      const diffPlanAmount = actualFund.minus(totalPlanAmount)
      this.diffPlanAmount = diffPlanAmount.decimalPlaces(2, BigNumber.ROUND_UP)
      console.info('planUseAmount:' + planUseAmount)
      console.info('diffPlanAmount:' + diffPlanAmount)
      this.infoForm.planUseAmount = planUseAmount.plus(diffPlanAmount).decimalPlaces(2, BigNumber.ROUND_DOWN)
      console.info('this.infoForm.planUseAmount:' + this.infoForm.planUseAmount)
    },
    calculateRequiredAmount () {
      console.info('calculateRequiredAmount')
      // 确保为非负值，直接修正原始值
      const actualFund = new BigNumber(this.prePayInfo?.actualFund ?? 0)
      this.infoForm.planUseAmount = BigNumber.max(new BigNumber(this.infoForm.planUseAmount || 0), 0).toNumber()
      this.infoForm.customerRequiredAmount = BigNumber.max(new BigNumber(this.infoForm.customerRequiredAmount || 0), 0).toNumber()
      this.infoForm.remainingRequiredAmount = BigNumber.max(new BigNumber(this.infoForm.remainingRequiredAmount || 0), 0).toNumber()
      this.prePayInfo.balance = BigNumber.max(new BigNumber(this.prePayInfo.balance || 0), 0).toNumber()

      const planUseAmount = new BigNumber(this.infoForm.planUseAmount)
      const remainingAmount = new BigNumber(this.infoForm.remainingRequiredAmount)
      const balanceAmount = new BigNumber(this.prePayInfo.balance)
      const unpaidPrincipal = new BigNumber(this.unpaidPrincipal || 0)
      const realBalance = BigNumber.max(new BigNumber(balanceAmount).minus(unpaidPrincipal), 0)
      this.realBalance = realBalance.decimalPlaces(2, BigNumber.ROUND_UP)

      const requiredAmount = realBalance.plus(remainingAmount)
      this.infoForm.customerRequiredAmount = requiredAmount.decimalPlaces(2, BigNumber.ROUND_UP)
      const totalPlanAmount = planUseAmount.plus(remainingAmount)
      this.totalPlanAmount = totalPlanAmount.decimalPlaces(2, BigNumber.ROUND_UP)

      // 融资比例
      const rate = new BigNumber(this.creditInfo.financingRatio ?? 100).div(100)
      const requiredAmount1 = BigNumber.max(planUseAmount.div(rate).minus(planUseAmount), 0)
      this.customerRequiredAmount = requiredAmount1.decimalPlaces(2, BigNumber.ROUND_UP)
      const remainingAmount1 = BigNumber.max(requiredAmount1.minus(realBalance), 0)
      this.remainingRequiredAmount = remainingAmount1.decimalPlaces(2, BigNumber.ROUND_UP)

      const diffPlanAmount = actualFund.minus(totalPlanAmount)
      this.diffPlanAmount = diffPlanAmount.decimalPlaces(2, BigNumber.ROUND_UP)
    },
    financialCompanyIdChange (companyId, init) {
      const companyInfo = find(this.capitalCompanyList, { companyId }) || {}
      this.infoForm.financialCompanyName = companyInfo.companyName || ''
      this.infoForm.financialSocialCreditCode = companyInfo.socialCreditCode || ''
      this.infoForm.financialCompanyCode = companyInfo.companyCode || ''
      this.getCreditList()
      if (init !== true) {
        this.infoForm.applyCreditId = ''
      }
    },
    applyCreditIdChange (pid, init) {
      const creditInfo = find(this.creditList, { pid }) || {}
      this.infoForm.applyCreditCode = creditInfo.applyCreditCode || ''
      this.infoForm.productName = creditInfo.productName
      this.getCreditDetail(pid).then(result => {
        const productDocuments = result.productSnapshot?.useCreditDocumentsList ?? []
        productDocuments.forEach(item => {
          delete item.pid
        })
        this.infoForm.productDocuments = result.productSnapshot?.useCreditDocumentsList ?? []
        this.infoForm.productId = result.productSnapshot?.pid
        this.infoForm.contractSigningOption = result.productSnapshot?.useContractSigningOption
        this.prepaymentIdChange(this.infoForm.prepaymentId, init)
        this.calculateRequiredAmount()
      })
    },
    // 提前打款信息改变事件
    prepaymentIdChange (pid, init) {
      if (!pid) {
        this.infoForm.prepaymentDate = ''
        this.infoForm.prepaymentPeriod = ''
        this.infoForm.planDate = ''
        this.infoForm.planPeriod = ''
        // this.infoForm.planUseAmount = null
        // this.infoForm.customerRequiredAmount = null
        this.prePayInfo = {}
        this.productionPlanList = []
        this.infoForm.planId = ''
        this.infoForm.planCode = ''
        return false
      }
      const prePayInfo = find(this.prePayList, { pid }) || {}
      this.infoForm.prepaymentDate = prePayInfo.agreementYearMonth || ''
      this.infoForm.prepaymentPeriod = prePayInfo.agreementTendays || ''
      if (init !== true) {
        this.infoForm.planDate = this.infoForm.prepaymentDate
        this.infoForm.planPeriod = this.infoForm.prepaymentPeriod
      }
      this.getPrePayDetail(pid).then(() => {
        if (init !== true) {
          console.info('getPrePayDetail')
          // 确保为非负值，直接修正原始值
          const actualFund = new BigNumber(this.prePayInfo?.actualFund ?? 0)
          this.infoForm.planUseAmount = BigNumber.max(new BigNumber(this.infoForm.planUseAmount || 0), 0).toNumber()
          this.infoForm.customerRequiredAmount = BigNumber.max(new BigNumber(this.infoForm.customerRequiredAmount || 0), 0).toNumber()
          this.infoForm.remainingRequiredAmount = BigNumber.max(new BigNumber(this.infoForm.remainingRequiredAmount || 0), 0).toNumber()
          this.prePayInfo.balance = BigNumber.max(new BigNumber(this.prePayInfo.balance || 0), 0).toNumber()

          const planUseAmount = new BigNumber(this.infoForm.planUseAmount)
          const remainingAmount = new BigNumber(this.infoForm.remainingRequiredAmount)
          const balanceAmount = new BigNumber(this.prePayInfo.balance)

          const requiredAmount = balanceAmount.plus(remainingAmount)
          this.infoForm.customerRequiredAmount = requiredAmount.decimalPlaces(2, BigNumber.ROUND_UP)
          const totalPlanAmount = planUseAmount.plus(remainingAmount)
          this.totalPlanAmount = totalPlanAmount.decimalPlaces(2, BigNumber.ROUND_UP)

          // 融资比例
          const rate = new BigNumber(this.creditInfo.financingRatio ?? 100).div(100)
          const requiredAmount1 = BigNumber.max(planUseAmount.div(rate).minus(planUseAmount), 0)
          this.customerRequiredAmount = requiredAmount1.decimalPlaces(2, BigNumber.ROUND_UP)
          const remainingAmount1 = BigNumber.max(requiredAmount1.minus(balanceAmount), 0)
          this.remainingRequiredAmount = remainingAmount1.decimalPlaces(2, BigNumber.ROUND_UP)

          const diffPlanAmount = actualFund.minus(totalPlanAmount)
          this.diffPlanAmount = diffPlanAmount.decimalPlaces(2, BigNumber.ROUND_UP)

          // 融资比例
          // const rate = new BigNumber(this.creditInfo.financingRatio ?? 100).div(100)
          // 享受提前奖励应补款金额
          // const differFund = new BigNumber(prePayInfo.differFund ?? 0).minus(prePayInfo.balanceAmount ?? 0)
          // 申请融资金额
          // const planUseAmount = differFund.times(rate).decimalPlaces(0, BigNumber.ROUND_UP)
          // 用户打款金额
          // const customerRequiredAmount = planUseAmount.div(rate).minus(planUseAmount).decimalPlaces(0, BigNumber.ROUND_UP)
          // this.infoForm.planUseAmount = planUseAmount.toNumber()
          // this.infoForm.customerRequiredAmount = customerRequiredAmount.toNumber()
          // this.infoForm.differFund = differFund.toNumber()
          // this.calculateCustomerRequiredAmount()
        }
        this.getProductionPlanList().then((list = []) => {
          this.infoForm.planId = list.map(item => item.pid).join(',')
          this.infoForm.planCode = list.map(item => item.planNo).join(',')
        })
      })
    },
    getCapitalCompanyList () {
      this.$api.warehouse.companyList({ type: 'FUND' }).then(result => {
        this.capitalCompanyList = result.data.data || []
      })
    },
    getCreditList () {
      this.$api.creditManagement.credit.list({
        status: 'EFFECT',
        financialCompanyId: this.infoForm.financialCompanyId,
        companyId: this.infoForm.useCompanyId
      }).then(result => {
        this.creditList = result.data.data || []
      })
    },
    getPrePayList () {
      this.$api.agreement.payment.list({ custNo: this.infoForm.useCompanyCode }).then(result => {
        this.prePayList = result.data.data || []
      })
    },
    getPrePayDetail (pid) {
      return new Promise((resolve, reject) => {
        if (!pid) return reject(new Error('pid is required'))
        this.$api.agreement.payment.info(pid).then(result => {
          this.prePayInfo = result.data.data || {}
          resolve(result.data.data)
        }).catch(e => {
          reject(e)
        })
      })
    },
    getProductionPlanList () {
      return new Promise((resolve, reject) => {
        this.$api.agreement.plan.list({
          custNo: this.infoForm.useCompanyCode,
          submitAmountMonths: dayjs(this.infoForm.planDate).format('YYYY-MM'),
          submitAmountTendays: this.infoForm.prepaymentPeriod
        }).then(result => {
          this.productionPlanList = result.data.data || []
          resolve(result.data.data ?? [])
        }).catch(error => {
          reject(error)
        })
      })
    },
    getCreditDetail (pid) {
      return new Promise((resolve, reject) => {
        if (!pid) {
          return reject(new Error('pid is required'))
        }
        Promise.all([
          this.$api.creditManagement.credit.info(pid)
          // this.$api.creditManagement.quotaViewing.info(pid)
        ]).then(result => {
          this.creditInfo = defaultsDeep(result[0].data.data, { productSnapshot: {} })
          // const remainingAmount = new BigNumber(result[1].data.data?.remainingAmount ?? 0)
          /* if (this.infoForm.pid && this.$route.name !== 'loanApplyAdd' && pid === this.originalInfoForm?.applyCreditId) {
            remainingAmount = remainingAmount.plus(this.infoForm.planUseAmount ?? 0)
          } */
          // this.creditInfo.remainingAmount = remainingAmount.toNumber()
          resolve(this.creditInfo)
        }).catch(error => {
          reject(error)
        })
      })
    },
    setDisableDate (data) {
      return dayjs().startOf('day').isAfter(data)
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <template #btn-inner>
      <el-button :loading="loading.submit" type="primary" @click="save">
        保存
      </el-button>
      <el-button :loading="loading.submit" type="primary" @click="confirm">
        提交
      </el-button>
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form ref="infoForm" :model="infoForm" :rules="rules" scroll-to-error>
          <div class="area-title">
            <p class="title">
              申请企业信息
            </p>
          </div>
          <div class="detail-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="企业名称">
                  <detail-input :model-value="infoForm.useCompanyName" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="统一社会信用代码">
                  <detail-input :model-value="infoForm.useSocialCreditCode" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              融资申请信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="申请融资编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.useCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="planUseDate" label="申请打款日期">
                  <el-date-picker
                    v-model="infoForm.planUseDate" :disabled-date="setDisableDate" v-bind="datePickAttr"
                    placeholder="请选择申请打款日期"
                  />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="financialCompanyId" label="授信资金方">
                  <el-select
                    v-model="infoForm.financialCompanyId" placeholder="请选择授信资金方"
                    @change="financialCompanyIdChange"
                  >
                    <el-option
                      v-for="item in capitalCompanyList" :key="item.pid" :label="item.companyName"
                      :value="item.companyId"
                    />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item
                  label="关联授信申请" prop="applyCreditId" is-link :can-jump="!!infoForm.applyCreditId"
                  :route-info="{ name: 'creditManagementApplicationDetail', query: { pid: infoForm.applyCreditId } }"
                >
                  <el-select
                    v-model="infoForm.applyCreditId" placeholder="请选择关联授信申请"
                    @change="applyCreditIdChange"
                  >
                    <el-option
                      v-for="item in creditList" :key="item.pid"
                      :label="`${item.applyCreditCode}（${item.productName}）`" :value="item.pid"
                    />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="授信额度">
                  <detail-input :model-value="$utils.moneyFormat(creditInfo.creditAmount)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="可用额度">
                  <detail-input :model-value="$utils.moneyFormat(creditInfo.remainingAmount)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="planUseAmount" label="申请融资金额（元）">
                  <template #label>
                    <div class="flex">
                      <span>申请融资金额（元）</span>
                      <el-link type="primary" @click="calculateMaxAmount">
                        <el-tooltip content="点击计算，最大融资金额。" placement="right" effect="light">
                          <el-icon>
                            <Refresh />
                          </el-icon>
                        </el-tooltip>
                      </el-link>
                    </div>
                  </template>
                  <amount-input
                    v-model="infoForm.planUseAmount" placeholder="请输入申请融资金额"
                    @change="calculateRequiredAmount"
                  />
                  <!--                  <el-input-number v-model="infoForm.planUseAmount" v-bind="inputNumberAttr" placeholder="请输入申请融资金额" />-->
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <!-- 申请资料 -->
          <profiles-list v-model:local-base-table="infoForm.productDocuments" />
          <attachment-list v-model="infoForm" :business-id="pid" business-type="USE_CREDIT_APPLY" />
          <div class="area-title">
            <p class="title">
              备注信息
            </p>
          </div>
          <div class="form-inner">
            <el-input
              v-model="infoForm.remark" type="textarea" :rows="4" :maxlength="2000" show-word-limit
              placeholder="请填写备注信息"
            />
          </div>
          <div class="area-title">
            <p class="title">
              操作记录
            </p>
          </div>
          <div class="form-inner">
            <operation-log :table-list-data="logList" />
          </div>
        </el-form>
      </div>
    </div>
    <!-- 提前打款信息 -->
    <pre-pay-detail ref="prePayRef" />
  </fe-page-main>
</template>

<style>

</style>
