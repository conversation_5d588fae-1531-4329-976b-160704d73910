<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import DetailInput from '@/components/base/detail-input.vue'
import AttachmentList from '@/components/attachment-list.vue'
import OperationLog from '@/components/operation-log.vue'
import dayjs from 'dayjs'
import { cloneDeep, defaultsDeep } from 'lodash'
import PrePayDetail from '@/views/loan/components/pre-pay-detail.vue'
import PlanList from '@/views/loan/components/plan-list.vue'
import ProfilesList from '@/views/creditManagement/components/profiles-list.vue'
import { Document } from '@element-plus/icons-vue'
import { statusCheck } from '@/assets/lib/status-check'
import BigNumber from 'bignumber.js'

export default {
  components: { Document, ProfilesList, PlanList, PrePayDetail, OperationLog, AttachmentList, DetailInput, FeFormItem, FePageMain },
  data () {
    return {
      pid: '',
      infoForm: {
        creditApplyVo: {
          productSnapshot: {}
        }
      },
      prePayInfo: {},
      productionPlanList: [],
      unpaidPrincipal: 0,
      realBalance: 0,
      logList: [],
      loading: {
        detail: false,
        confirm: false
      },
      dialog: {
        audit: false
      },
      approvalInfoForm: {},
      rules: {
        approvalFlag: this.$rulesToolkit.createRules({ name: '是否审批通过', required: true })
      }
    }
  },
  computed: {
    expectedRepaymentDate () {
      return Number(dayjs(this.infoForm.planUseDate).add(this.infoForm.usagePeriod || 0, 'day').format('x'))
    }
  },
  created () {
    this.pid = this.$route.query.pid
    this.getDetail()
  },
  methods: {
    getUnpaidPrincipal () {
      return new Promise((resolve, reject) => {
        this.$api.loan.apply.getUnpaidPrincipal({ principalId: this.infoForm.useCompanyId }).then(result => {
          this.unpaidPrincipal = result.data.data ?? 0
          resolve(this.unpaidPrincipal)
        }).catch(e => {
          reject(e)
        })
      })
    },
    localAudit (type) {
      this.approvalInfoForm.approvalFlag = type
      this.dialog.audit = true
    },
    audit () {
      this.$confirm('确认提交此审批?', '确认提交审批').then(() => {
        this.loading.confirm = true
        const formData = cloneDeep(this.approvalInfoForm)
        formData.pid = this.infoForm.pid
        this.$api.loan.apply.createProcess(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        })
      })
    },
    viewPrePayment () {
      this.$refs.prePayRef.init(this.prePayInfo)
    },
    getDetail () {
      this.loading.detail = true
      this.$api.loan.apply.detail(this.pid).then(result => {
        this.infoForm = defaultsDeep(result.data.data, { productDocuments: [], creditApplyVo: { productSnapshot: {} } })
        this.logList = this.infoForm.operatingRecordEntityList ?? []
        if (this.$route.name === 'loanApplyAudit') {
          statusCheck(this.infoForm, 'financialApproveStatus', 'pendingApproval', { name: 'loanCreditDetail', query: { pid: this.pid } })
        }
        if (this.infoForm.prepaymentId) {
          this.getPrePayDetail(this.infoForm.prepaymentId).then(() => {
            this.getProductionPlanList()
            this.calculateRealBalance()
          })
        }
        this.getUnpaidPrincipal().then(() => {
          this.calculateRealBalance()
        })
      }).finally(() => {
        this.loading.detail = false
      })
    },
    calculateRealBalance () {
      const balanceAmount = new BigNumber(this.prePayInfo.balance)
      const unpaidPrincipal = new BigNumber(this.unpaidPrincipal || 0)
      const realBalance = BigNumber.max(new BigNumber(balanceAmount).minus(unpaidPrincipal), 0)
      this.realBalance = realBalance.decimalPlaces(2, BigNumber.ROUND_UP)
    },
    getPrePayDetail (pid) {
      return new Promise((resolve, reject) => {
        if (!pid) return reject(new Error('pid is required'))
        this.$api.agreement.payment.info(pid).then(result => {
          this.prePayInfo = result.data.data || {}
          resolve(result.data.data)
        }).catch(e => {
          reject(e)
        })
      })
    },
    getProductionPlanList () {
      return new Promise((resolve, reject) => {
        this.$api.agreement.plan.list({ custNo: this.infoForm.useCompanyCode, submitAmountMonths: this.infoForm.planDate, submitAmountTendays: this.infoForm.prepaymentPeriod }).then(result => {
          this.productionPlanList = result.data.data || []
          resolve(result.data.data ?? [])
        }).catch(error => {
          reject(error)
        })
      })
    },
    confirmApproval () {
      this.$refs.approvalInfoFormRef.validate().then(() => {
        this.loading.confirm = true
        const formData = cloneDeep(this.approvalInfoForm)
        formData.pid = this.infoForm.pid
        this.$api.loan.apply.audit(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        }).finally(() => {
          this.loading.confirm = false
        })
      })
    },
    closeDialog () {
      this.$refs.approvalInfoFormRef.resetFields()
      this.approvalInfoForm = {}
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <template v-if="$route.name === 'loanApplyAudit'" #btn-inner>
      <el-button v-if="infoForm.creditApplyVo.productSnapshot.useBpmnEnable === 1" type="primary" @click="audit">
        提交审批
      </el-button>
      <template v-else>
        <el-button type="primary" @click="localAudit('1')">
          通过
        </el-button>
      </template>
      <el-button type="danger" @click="localAudit('0')">
        驳回
      </el-button>
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form>
          <div class="area-title">
            <p class="title">
              申请企业信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="企业名称">
                  <detail-input :model-value="infoForm.useCompanyName" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="统一社会信用代码">
                  <detail-input :model-value="infoForm.useSocialCreditCode" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              融资申请信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="申请融资编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.useCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="申请打款日期">
                  <detail-input :model-value="$utils.dateFormat(infoForm.planUseDate)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="授信资金方">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.financialCompanyName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="关联授信申请" is-link :can-jump="infoForm.applyCreditId" :route-info="{ name: 'creditManagementApplicationDetail', query: { pid: infoForm.applyCreditId } }">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.applyCreditCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="生产计划年月">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.planDate)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="生产计划旬">
                  <detail-input :model-value="$utils.statusFormat(infoForm.planPeriod, 'decade')" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="prepaymentId" label="提前打款信息" for="other">
                  <template #label>
                    <div class="flex">
                      <span>提前打款信息</span>
                      <el-link :disabled="!infoForm.prepaymentId" type="primary" class="ml-2" @click="viewPrePayment">
                        <el-tooltip content="点击查看" placement="right" effect="light">
                          <el-icon><Document /></el-icon>
                        </el-tooltip>
                      </el-link>
                    </div>
                  </template>
                  <detail-input custom>
                    <template #custom>
                      {{ $utils.isEffectiveCommon(infoForm.prepaymentDate) }}
                      <span v-if="infoForm.prepaymentPeriod">（{{ $utils.statusFormat(infoForm.prepaymentPeriod, 'decade') }})</span>
                    </template>
                  </detail-input>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="实际应打款金额（元）">
                  <detail-input :model-value="$utils.moneyFormat(prePayInfo.actualFund)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="客户锁定账户余额（元）">
                  <detail-input :model-value="$utils.moneyFormat(prePayInfo.balance)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="历史未还融资本金（元）">
                  <detail-input :model-value="$utils.moneyFormat(unpaidPrincipal)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="客户锁定账户余额 - 历史未还融资本金（元）">
                  <detail-input :model-value="$utils.moneyFormat(realBalance)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="planUseAmount" label="申请融资金额(元)">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.planUseAmount)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="customerRequiredAmount" label="用户打款金额（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.customerRequiredAmount)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="remainingRequiredAmount" label="需补打款金额（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.remainingRequiredAmount)" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <template v-if="['financialApproval', 'pendingEffect', 'effect', 'payment', 'overdue', 'end'].includes(infoForm.status)">
            <div class="area-title">
              <p class="title">
                融资批复信息
              </p>
            </div>
            <div class="form-inner">
              <el-row :gutter="20">
                <el-col :span="12">
                  <fe-form-item label="建议融资金额（元）">
                    <detail-input :model-value="$utils.moneyFormat(infoForm.approvedAmount)" />
                  </fe-form-item>
                </el-col>
                <el-col :span="12">
                  <fe-form-item label="需补打款金额（元）">
                    <detail-input :model-value="$utils.moneyFormat(infoForm.remainingRequiredAmount)" />
                  </fe-form-item>
                </el-col>
                <el-col :span="12">
                  <fe-form-item label="用款期限(天)">
                    <detail-input :model-value="$utils.isEffectiveCommon(infoForm.usagePeriod)" />
                  </fe-form-item>
                </el-col>
                <el-col :span="12">
                  <fe-form-item label="预计还款日期">
                    <detail-input :model-value="$utils.dateFormat(expectedRepaymentDate)" />
                  </fe-form-item>
                </el-col>
              </el-row>
            </div>
            <div class="area-title">
              <p class="title">
                融资协议信息
              </p>
            </div>
            <div class="form-inner">
              <el-row :gutter="20">
                <el-col :span="12">
                  <fe-form-item label="是否必须签署协议">
                    <el-radio-group v-model="infoForm.contractSigningOption" disabled>
                      <el-radio v-for="item in $utils.getEnableDictStatus('contractSigningOption')" :key="item.value" :value="item.value">
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </fe-form-item>
                </el-col>
              </el-row>
            </div>
          </template>
          <!-- 申请资料 -->
          <profiles-list :local-base-table="infoForm.productDocuments" readonly />
          <plan-list :production-plan-list="productionPlanList" :is-init="!infoForm.prepaymentId" />
          <attachment-list v-model="infoForm" :business-id="infoForm.pid" business-type="USE_CREDIT_APPLY" readonly />
          <div class="area-title">
            <p class="title">
              备注信息
            </p>
          </div>
          <div class="form-inner">
            <el-form-item>
              {{ $utils.isEffectiveCommon(infoForm.remark) }}
            </el-form-item>
          </div>
          <div class="area-title">
            <p class="title">
              操作记录
            </p>
          </div>
          <div class="form-inner">
            <operation-log :table-list-data="logList" />
          </div>
        </el-form>
      </div>
    </div>
    <dialog-inner v-model="dialog.audit" title="审批" :loading="loading.confirm" width="30%" destroy-on-close @submit="confirmApproval" @close="closeDialog">
      <div class="form-area">
        <el-form ref="approvalInfoFormRef" :model="approvalInfoForm" :rules="rules">
          <div class="form-inner">
            <!--<fe-form-item label="是否审批通过" prop="approvalFlag">
              <el-radio-group v-model="approvalInfoForm.approvalFlag">
                <el-radio value="1">
                  通过
                </el-radio>
                <el-radio value="0">
                  驳回
                </el-radio>
              </el-radio-group>
            </fe-form-item>-->
            <fe-form-item label="备注信息">
              <el-input v-model="approvalInfoForm.remark" type="textarea" maxlength="100" rows="3" show-word-limit placeholder="请输入备注信息" />
            </fe-form-item>
          </div>
        </el-form>
      </div>
    </dialog-inner>
    <!-- 提前打款信息 -->
    <pre-pay-detail ref="prePayRef" />
  </fe-page-main>
</template>

<style>

</style>
