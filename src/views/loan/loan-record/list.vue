<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeSearchInner from '@/components/base/fe-search-inner.vue'
import { basePageListMixin } from '@fero/commons-vue'
import { utils } from '@/assets/lib/utils'
import { cloneDeep, find, groupBy } from 'lodash'
import DetailInput from '@/components/base/detail-input.vue'
import dayjs from 'dayjs'

export default {
  components: { DetailInput, FeSearchInner, FePageMain },
  mixins: [basePageListMixin],
  data () {
    return {
      columnOptionList: [
        { label: '放款编号', prop: 'loanCode', minWidth: 180, formatter: this.$utils.isEffective },
        { label: '融资企业', prop: 'payeeName', minWidth: 180, formatter: this.$utils.isEffective },
        { label: '资金方名称', prop: 'lenderName', minWidth: 180, formatter: this.$utils.isEffective },
        { label: '放款金额（元）', prop: 'applyAmount', minWidth: 180, formatter: this.$utils.tableMoneyFormat },
        { label: '放款状态', prop: 'status', minWidth: 180, formatter: this.$utils.tableStatusFormat('LOAN_APPLY_STATUS') },
        { label: '融资申请编号', prop: 'useCode', minWidth: 180, formatter: this.$utils.isEffective, slotName: 'useCode' },
        { label: '计划放款日期', prop: 'loanPlanDate', minWidth: 180, formatter: this.$utils.tableDateFormat },
        // { label: '发起放款日期', prop: 'loanInitiationDate', minWidth: 180, formatter: this.$utils.tableDateFormat },
        { label: '实际放款日期', prop: 'actualLoanDate', minWidth: 180, formatter: this.$utils.tableDateFormat }
        // { label: '支付流水编号', prop: 'paymentNumber', minWidth: 180, formatter: this.$utils.isEffective }
      ],
      requestPayment: null,
      infoDialog: {
        visible: false
      },
      repayForm: {},
      dialogAction: '',
      payeeBankList: [],
      lenderBankAccountList: [],
      payeeBankAccountList: [],
      rules: {
        lenderBank: this.$rulesToolkit.createRules({ required: true, name: '放款方开户行', trigger: 'change' }),
        lenderBankAccount: this.$rulesToolkit.createRules({ required: true, name: '放款方银行账号', trigger: 'change' }),
        payeeBank: this.$rulesToolkit.createRules({ required: true, name: '收款方开户行', trigger: 'change' }),
        payeeBankAccount: this.$rulesToolkit.createRules({ required: true, name: '收款方银行账号', trigger: 'change' }),
        actualLoanDate: this.$rulesToolkit.createRules({ required: true, name: '实际放款日期', trigger: 'change' })
      }
    }
  },
  computed: {
    searchEffectiveDate: utils.computedDate('actualLoanDateMin', 'actualLoanDateMax'),
    dynamicRoute () {
      return (id) => {
        const routeData1 = this.$router.resolve({
          name: 'loanCreditDetail',
          query: { pid: id }
        })
        return routeData1.href
      }
    }
  },
  methods: {
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.loan.loanRecord.pageList(this.searchForm).then(result => {
        if (result.data.data) {
          this.tableList = result.data.data
        }
      }).finally(() => {
        this.loading.list = false
      })
    },
    handleSelectionChange (selection) {
      if (!selection || selection.length === 0) {
        return false
      }
      const lastSelected = selection[selection.length - 1]
      this.requestPayment = lastSelected
      if (selection.length > 1) {
        this.$refs.tableList.tableEvent('clearSelection')
        this.$refs.tableList.tableEvent('toggleRowSelection', lastSelected, true)
      }
    },
    payApply (action, pid) {
      this.dialogAction = action
      this.infoDialog.visible = true
      this.$api.loan.loanPlan.detail(pid).then(result => {
        this.repayForm = result.data.data ?? {}
        this.getBankList({ companyId: this.repayForm.lenderId }).then(bankList => {
          this.lenderBankAccountList = bankList
        })
        this.getBankList({ companyId: this.repayForm.payeeId }).then(bankList => {
          const group = groupBy(bankList, 'openingBank')
          const list = []
          Object.keys(group).forEach(openingBank => {
            list.push({
              openingBank,
              openingBankNo: group[openingBank][0].openingBankNo,
              bankAccountList: group[openingBank]
            })
          })
          this.payeeBankList = list
          const activeBank = find(list, { openingBank: this.repayForm.payeeBank }) ?? {}
          this.payeeBankAccountList = activeBank.bankAccountList
        })
      })
    },
    confirm (pid) {
      this.$refs.repayForm.validate().then(() => {
        const formData = cloneDeep(this.repayForm)
        this.loading.submit = true
        // 根据 dialogAction 调用不同的接口
        // const apiCall = this.dialogAction === 'pay'
        //   ? this.$api.loan.loanPlan.pay(formData)
        //   : this.$api.loan.loanPlan.confirmPay(formData)
        this.$api.loan.loanPlan.confirmPay(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.infoDialog.visible = false
          this.getList()
        }).finally(() => {
          this.loading.submit = false
        })
      })
    },
    lenderBankAccountChange (value) {
      const activeBankAccount = find(this.lenderBankAccountList, { bankNo: value })
      this.repayForm.lenderBank = activeBankAccount.openingBank
      this.repayForm.lenderAccountName = activeBankAccount.accountNoName
      this.repayForm.lenderBankNo = activeBankAccount.openingBankNo
    },
    getBankList (query) {
      return new Promise((resolve, reject) => {
        this.$api.principal.bankAccount.list(query).then(result => {
          resolve(result.data.data ?? [])
        }).catch(error => {
          reject(error)
        })
      })
    },
    setRowColor ({ row }) {
      if (dayjs().isAfter(row.loanPlanDate) && !['disbursed'].includes(row.status)) {
        return 'danger-row'
      }
    },
    closeDia () {
      this.repayForm = {}
      this.dialogAction = ''
      this.$refs.repayForm.resetFields()
    },
  }
}
</script>

<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <fe-form-item label="融资企业">
            <el-input v-model="searchForm.payeeName" placeholder="请输入融资企业" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="融资申请编号">
            <el-input v-model="searchForm.useCode" placeholder="请输入融资申请编号" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="放款状态">
            <el-select v-model="searchForm.status" placeholder="请选择放款状态" clearable>
              <el-option v-for="item in $utils.getEnableDictStatus('LOAN_APPLY_STATUS')" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="实际放款日期">
            <el-date-picker
              v-model="searchEffectiveDate"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="shortcuts"
              format="YYYY-MM-DD"
              value-format="x"
              placeholder="请选择实际放款日期"
            />
          </fe-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner ref="tableList" v-loading="loading.list" :table-data="tableList.records" :row-class-name="setRowColor" show-set-columns :column-option="columnOptionList" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe title="信息明细" @change-page-num="changePageNum" @change-page-size="changePageSize" @selection-change="handleSelectionChange">
        <template #btn-inner>
          <!--<el-button :disabled="!requestPayment || requestPayment.status === 'progressDisburse' || requestPayment.status === 'disbursed'" type="primary" @click="payApply('pay', requestPayment.pid)">-->
          <!--  放款-->
          <!--</el-button>-->
        </template>
        <template #table-columns-before>
          <el-table-column type="selection" />
        </template>
        <template #useCode="scope">
          <el-link type="primary" :href="dynamicRoute(scope.row.useId)" target="_blank">
            {{ scope.row.useCode }}
          </el-link>
        </template>
        <template #table-columns-after>
          <!--          <el-table-column label="操作" width="80" fixed="right">-->
          <!--            <table-link :to="{ name: 'loanPayApplyDetail' }" type="primary">详情</table-link>-->
          <!--          </el-table-column>-->
        </template>
      </table-inner>
    </div>
    <dialog-inner v-model="infoDialog.visible" :loading="loading.submit" width="50%" title="确认放款信息" @submit="confirm(repayForm.pid)" @close="closeDia">
      <div class="form-area">
        <el-form ref="repayForm" :model="repayForm" :rules="rules">
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <fe-form-item prop="lenderBankAccount" label="放款方银行账号">
                  <el-select v-model="repayForm.lenderBankAccount" placeholder="请选择放款方银行账号" @change="lenderBankAccountChange">
                    <el-option v-for="item in lenderBankAccountList" :key="item.pid" :label="item.bankNo" :value="item.bankNo" />
                  </el-select>
                  <!--<detail-input :model-value="$utils.isEffectiveCommon(repayForm.lenderBankAccount)" />-->
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="放款方开户行">
                  <detail-input :model-value="$utils.isEffectiveCommon(repayForm.lenderBank)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款方账户名称">
                  <detail-input :model-value="$utils.isEffectiveCommon(repayForm.payeeAccountName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款方银行账号">
                  <detail-input :model-value="$utils.isEffectiveCommon(repayForm.payeeBankAccount)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款方开户银行">
                  <detail-input :model-value="$utils.isEffectiveCommon(repayForm.payeeBank)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款方开户行号">
                  <detail-input :model-value="$utils.isEffectiveCommon(repayForm.payeeBankNo)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="本次放款金额（元）">
                  <detail-input :model-value="$utils.moneyFormat(repayForm.applyAmount)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="实际放款日期" prop="actualLoanDate">
                  <el-date-picker v-model="repayForm.actualLoanDate" v-bind="datePickAttr" placeholder="请选择实际放款日期" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </dialog-inner>
  </fe-page-main>
</template>

<style>
</style>
