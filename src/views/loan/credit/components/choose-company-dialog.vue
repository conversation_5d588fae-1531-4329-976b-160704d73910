<template>
  <dialog-inner
    v-model="showDialog"
    title="融资申请选择"
    destroy-on-close
    width="600px"
    @submit="dialogSubmit"
  >
    <div class="form-area company-pick">
      <el-form ref="loanApplyInitForm" :model="baseForm" :rules="rules" scroll-to-error>
        <div class="form-inner">
          <el-row>
            <el-col :span="24">
              <fe-form-item prop="productId" label="金融产品">
                <el-select
                  v-model="baseForm.productId"
                  placeholder="请选择金融产品"
                  filterable
                >
                  <el-option
                    v-for="item in productList"
                    :key="item.pid"
                    :label="item.productName"
                    :value="item.pid"
                  />
                </el-select>
              </fe-form-item>
            </el-col>
            <el-col :span="24">
              <fe-form-item prop="companyId" label="融资企业">
                <el-select
                  v-model="baseForm.companyId"
                  placeholder="请选择融资企业"
                  filterable
                  @change="changeUseCompany"
                >
                  <el-option
                    v-for="item in customerList"
                    :key="item.companyId"
                    :label="item.companyName"
                    :value="item.companyId"
                  />
                </el-select>
              </fe-form-item>
            </el-col>
            <!--<el-col :span="10" :offset="2">-->
            <!--  <fe-form-item prop="financialCompanyId" label="资金方" required>-->
            <!--    <el-select-->
            <!--      v-model="baseForm.financialCompanyId"-->
            <!--      placeholder="请选择资金方"-->
            <!--      filterable-->
            <!--      :multiple="multiple"-->
            <!--    >-->
            <!--      <el-option-->
            <!--        v-for="item in customerList"-->
            <!--        :key="item.companyId"-->
            <!--        :label="item.companyName"-->
            <!--        :value="item.companyId"-->
            <!--      />-->
            <!--    </el-select>-->
            <!--  </fe-form-item>-->
            <!--</el-col>-->
            <!--<el-col :span="10" :offset="2">-->
            <!--  <fe-form-item prop="coreCompanyId" label="核心企业">-->
            <!--    <el-select-->
            <!--      v-model="baseForm.coreCompanyId"-->
            <!--      placeholder="请选择核心企业"-->
            <!--      filterable-->
            <!--      :multiple="multiple"-->
            <!--    >-->
            <!--      <el-option-->
            <!--        v-for="item in customerList"-->
            <!--        :key="item.companyId"-->
            <!--        :label="item.companyName"-->
            <!--        :value="item.companyId"-->
            <!--      />-->
            <!--    </el-select>-->
            <!--  </fe-form-item>-->
            <!--</el-col>-->
            <!--<el-col :span="10">-->
            <!--  <fe-form-item prop="operateCompanyId" label="运营方" required>-->
            <!--    <el-select-->
            <!--      v-model="baseForm.operateCompanyId"-->
            <!--      placeholder="请选择运营方"-->
            <!--      filterable-->
            <!--      :multiple="multiple"-->
            <!--    >-->
            <!--      <el-option-->
            <!--        v-for="item in customerList"-->
            <!--        :key="item.companyId"-->
            <!--        :label="item.companyName"-->
            <!--        :value="item.companyId"-->
            <!--      />-->
            <!--    </el-select>-->
            <!--  </fe-form-item>-->
            <!--</el-col>-->
          </el-row>
        </div>
      </el-form>
    </div>
  </dialog-inner>
</template>

<script>

import { cloneDeep, find } from 'lodash'

export default {
  props: {
    companyType: {
      type: String,
      default: 'CUSTOMER'
    },
    routerPath: {
      type: String,
      required: true
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      showDialog: false,
      // 企业选择
      customerList: [],
      // 选中的企业字段
      baseForm: {
        productId: undefined,
        companyId: undefined,
        companyCode: undefined
      },
      rules: {
        productId: this.$rulesToolkit.createRules({ name: '金融产品', required: true, trigger: 'change' }),
        companyId: this.$rulesToolkit.createRules({ name: '融资企业', required: true, trigger: 'change' })
      },
      productList: []
    }
  },
  created () {
    this.getProductList()
  },
  methods: {
    /** @public */
    dialogInit () {
      this.customerList = []
      this.checkCustomer = null
      this.showDialog = true
      this.$api.warehouse
        .companyList({
          type: this.companyType,
          status: 'EFFECTIVE',
          enabled: '1'
        })
        .then((result) => {
          if (result.data.data) {
            this.customerList = result.data.data
          }
        })
        .finally(() => {})
    },
    changeUseCompany (val) {
      const useCompany = find(this.customerList, { companyId: val }) ?? {}
      this.baseForm.companyName = useCompany.companyName
      this.baseForm.companyCode = useCompany.companyCode
      this.baseForm.socialCreditCode = useCompany.socialCreditCode
    },
    getProductList () {
      this.$api.creditManagement.credit.productList().then((result) => {
        this.productList = result.data.data ?? []
      })
    },
    dialogSubmit () {
      this.$refs.loanApplyInitForm.validate().then(() => {
        const query = {
          productId: this.baseForm.productId,
          companyId: this.baseForm.companyId,
          companyName: this.baseForm.companyName,
          companyCode: this.baseForm.companyCode,
          socialCreditCode: this.baseForm.socialCreditCode
        }
        this.$router.push({
          name: this.routerPath,
          query
        })
      })
    }
  }
}
</script>

<style>
.company-pick .el-select__wrapper {
  padding-top: 4px;
  padding-bottom: 4px;
}
</style>
