<template>
  <!-- 核心企业确权 -->
  <div class="core-enterprise-rights-confirmation">
    <div class="left">
      <h3>{{ formConfig[0].groupName }}</h3>
      <el-form>
        <div v-for="item in formConfig[0].questionList" :key="item.pid">
          <el-form-item v-if="!item.questionType" :label="item.stem" label-width="70%" label-position="left">
            <el-radio-group>
              <el-radio value="apple">是</el-radio>
              <el-radio value="pear">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-else :label="item.stem" class="form-textarea">
            <el-input
              :maxlength="100"
              :rows="4"
              type="textarea"
              placeholder="请输入"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="right">
      <h3>{{ formConfig[1].groupName }}</h3>
      <el-form>
        <div v-for="item in formConfig[1].questionList" :key="item.pid">
          <el-form-item v-if="!item.questionType" :label="item.stem" label-width="70%" label-position="left">
            <el-radio-group>
              <el-radio value="apple">是</el-radio>
              <el-radio value="pear">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-else :label="item.stem" class="form-textarea">
            <el-input
              :maxlength="100"
              :rows="4"
              type="textarea"
              placeholder="请输入"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    formConfig: {
      type: Object,
      default () {
        return []
      }
    }
  },
  data () {
    return {
      
    }
  }
}
</script>

<style lang="scss" scoped>
.core-enterprise-rights-confirmation {
  display: flex;
  .left, .right {
    width: 50%;
    :deep(.form-textarea) {
      display: flex;
      flex-direction: column;
      align-items: start;
      margin-top: 20px;
      .el-form-item__content {
        width: 80% !important;
      }
      .el-textarea {
        width: 100% !important;
      }
    }
    :deep(.el-form-item__label) {
      word-wrap: break-word !important;
    }
  }
}
</style>
