<template>
  <!-- 贸易合同 -->
  <div class="trade-invoice">
    <el-table :data="tableData">
      <el-table-column v-for="item in columnOptionList" :key="item.prop" :prop="item.prop" :label="item.label" :width="item.minWidth" />
    </el-table>
  </div>
</template>

<script>
export default {
  data () {
    return {
      columnOptionList: [
        { label: '贸易发票编号', prop: 'tradeCode', formatter: this.$utils.isEffective, minWidth: 150 },
        { label: '贸易发票名称', prop: 'tradeName', formatter: this.$utils.isEffective, minWidth: 150 },
        { label: '状态', prop: 'ifSync', formatter: this.$utils.tableStatusFormat('ASSETS_IF_SYNC', { valueTypeToString: true }), minWidth: 100 },
        { label: '发票来源', prop: 'contractSource', formatter: this.$utils.isEffective, minWidth: 100, slotName: 'contractSource' },
        { label: '价税合计（元）', prop: 'contractAmount', formatter: this.$utils.isEffective, minWidth: 150 },
        { label: '买方', prop: 'buyerName', formatter: this.$utils.isEffective, minWidth: 180 },
        { label: '卖方', prop: 'sellerName', formatter: this.$utils.isEffective, minWidth: 150 },
        { label: '发票代码', prop: 'signDate', formatter: this.$utils.tableDateFormat, minWidth: 150 },
        { label: '发票号码', prop: 'signDate', formatter: this.$utils.tableDateFormat, minWidth: 150 },
        { label: '开票日期', prop: 'signDate', formatter: this.$utils.tableDateFormat, minWidth: 150 },
        { label: '创建时间', prop: 'createTime', formatter: this.$utils.tableDateFormat, minWidth: 150 }
      ]
    }
  }
}
</script>
