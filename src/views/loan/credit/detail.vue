<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import DetailInput from '@/components/base/detail-input.vue'
import AttachmentList from '@/components/attachment-list.vue'
import OperationLog from '@/components/operation-log.vue'
import { cloneDeep, defaultsDeep, get } from 'lodash'
import PrePayDetail from '@/views/loan/components/pre-pay-detail.vue'
import ProfilesList from '@/views/creditManagement/components/profiles-list.vue'
import { statusCheck } from '@/assets/lib/status-check'
import TradeReceivableDetail from '@/views/assetsManagement/components/trade-receivable-detail.vue'
import { nextTick } from 'vue'
import BigNumber from 'bignumber.js'
import dayjs from 'dayjs'

export default {
  components: { TradeReceivableDetail, ProfilesList, PrePayDetail, OperationLog, AttachmentList, DetailInput, FeFormItem, FePageMain },
  data () {
    return {
      pid: '',
      infoForm: {
        creditApplyVo: {
          productSnapshot: {}
        }
      },
      logList: [],
      loading: {
        detail: false,
        confirm: false
      },
      dialog: {
        audit: false
      },
      approvalInfoForm: {},
      rules: {
        approvalFlag: this.$rulesToolkit.createRules({ name: '是否审批通过', required: true })
      },
      interest: 0
    }
  },
  computed: {
  },
  created () {
    this.pid = this.$route.query.pid
    this.getDetail()
  },
  methods: {
    calculateInterest () {
      if (!this.infoForm.approvedAmount || !this.infoForm.creditApplyVo.productAnnualInterest || !this.infoForm.planUseDate || !this.infoForm.planRepaymentDate) {
        this.interest = 0
        return false
      }
      // 融资金额
      const approvedAmount = new BigNumber(this.infoForm.approvedAmount ?? 0)
      // 融资利率（年化）
      const productAnnualRate = new BigNumber(this.infoForm.creditApplyVo.productAnnualInterest ?? 0)
      // 融资期限
      const limit = dayjs(this.infoForm.planRepaymentDate).startOf('day').diff(dayjs(this.infoForm.planUseDate).startOf('day'), 'day')
      if (limit <= 0) {
        this.interest = 0
        return false
      }
      const interest = approvedAmount.times(limit).times(productAnnualRate).div(100).div(360)
      this.interest = interest.toNumber()
    },
    localAudit (type) {
      this.approvalInfoForm.approvalFlag = type
      this.dialog.audit = true
    },
    audit () {
      this.$confirm('确认提交此审批?', '确认提交审批').then(() => {
        this.loading.confirm = true
        const formData = cloneDeep(this.approvalInfoForm)
        formData.pid = this.infoForm.pid
        this.$api.loan.apply.createProcess(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        })
      })
    },
    getDetail () {
      this.loading.detail = true
      this.$api.loan.apply.detail(this.pid).then(result => {
        this.infoForm = defaultsDeep(result.data.data, { productDocuments: [], creditApplyVo: { productSnapshot: {} } })
        this.logList = this.infoForm.operatingRecordEntityList ?? []
        if (this.$route.name === 'loanApplyAudit') {
          this.calculateInterest()
          statusCheck(this.infoForm, 'financialApproveStatus', 'pendingApproval', { name: 'loanCreditDetail', query: { pid: this.pid } })
        }
        const receivableInfo = get(this.infoForm, 'receivableList[0]')
        if (receivableInfo) {
          nextTick(() => {
            this.$refs.receivableDetailRef.init(receivableInfo)
          })
        }
      }).finally(() => {
        this.loading.detail = false
      })
    },
    confirmApproval () {
      this.$refs.approvalInfoFormRef.validate().then(() => {
        this.loading.confirm = true
        const formData = cloneDeep(this.approvalInfoForm)
        formData.pid = this.infoForm.pid
        this.$api.loan.apply.audit(formData).then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        }).finally(() => {
          this.loading.confirm = false
        })
      })
    },
    closeDialog () {
      this.$refs.approvalInfoFormRef.resetFields()
      this.approvalInfoForm = {}
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <template v-if="$route.name === 'loanApplyAudit'" #btn-inner>
      <el-button v-if="infoForm.creditApplyVo.productSnapshot.useBpmnEnable === 1" type="primary" @click="audit">
        提交审批
      </el-button>
      <template v-else>
        <el-button type="primary" @click="localAudit('1')">
          通过
        </el-button>
      </template>
      <el-button type="danger" @click="localAudit('0')">
        驳回
      </el-button>
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form>
          <div class="area-title">
            <p class="title">
              申请企业信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="企业名称">
                  <detail-input :model-value="infoForm.useCompanyName" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="统一社会信用代码">
                  <detail-input :model-value="infoForm.useSocialCreditCode" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              融资方案
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="金融产品">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.productName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="financialCompanyId" label="授信资金方">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.financialCompanyName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="申请融资编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.useCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item
                  label="关联授信申请" prop="applyCreditId" is-link :can-jump="!!infoForm.applyCreditId"
                  :route-info="{ name: 'creditManagementApplicationDetail', query: { pid: infoForm.applyCreditId } }"
                >
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.applyCreditCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="年化利率（%）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.creditApplyVo.productAnnualInterest)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="planUseAmount" label="申请融资金额（元）">
                  <template #label>
                    <div class="flex">
                      <span>申请融资金额（元）</span>
                    </div>
                  </template>
                  <detail-input :model-value="$utils.moneyFormat(infoForm.planUseAmount)" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              收款信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="银行账号">
                  <detail-input :model-value="infoForm.payeeBankAccount" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="账户名称">
                  <detail-input :model-value="infoForm.payeeName" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="开户银行">
                  <detail-input :model-value="infoForm.payeeBank" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="开户行号">
                  <detail-input :model-value="infoForm.payeeBankNo" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <template v-if="['financialApproval', 'confirmApproval', 'pendingEffect', 'effect', 'payment', 'repayment', 'overdue', 'end'].includes(infoForm.status)">
            <div class="area-title">
              <p class="title">
                融资批复信息
              </p>
            </div>
            <div class="form-inner">
              <el-row :gutter="20">
                <el-col :span="12">
                  <fe-form-item prop="planUseDate" label="计划放款日（起息日）">
                    <detail-input :model-value="$utils.dateFormat(infoForm.planUseDate)" />
                  </fe-form-item>
                </el-col>
                <el-col :span="12">
                  <fe-form-item prop="planRepaymentDate" label="计划还款日（到期日）">
                    <detail-input :model-value="$utils.dateFormat(infoForm.planRepaymentDate)" />
                  </fe-form-item>
                </el-col>
                <el-col :span="12">
                  <fe-form-item prop="approvedAmount" label="批复融资金额（元）">
                    <template #label>
                      <div class="flex">
                        <span>批复融资金额（元）</span>
                        <span v-if="interest > 0" class="text-orange-500 ml-4">预计利息：{{ $utils.moneyFormat(interest) }}</span>
                      </div>
                    </template>
                    <detail-input :model-value="$utils.moneyFormat(infoForm.approvedAmount)" />
                  </fe-form-item>
                </el-col>
                <el-col :span="12">
                  <fe-form-item label="计划用款期限（天）">
                    <detail-input :model-value="infoForm.usagePeriod" />
                  </fe-form-item>
                </el-col>
              </el-row>
            </div>
            <div class="area-title">
              <p class="title">
                融资协议信息
              </p>
            </div>
            <div class="form-inner">
              <el-row :gutter="20">
                <el-col :span="12">
                  <fe-form-item label="是否必须签署协议">
                    <el-radio-group v-model="infoForm.contractSigningOption" disabled>
                      <el-radio v-for="item in $utils.getEnableDictStatus('contractSigningOption')" :key="item.value" :value="item.value">
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </fe-form-item>
                </el-col>
              </el-row>
            </div>
          </template>
          <div class="area-title">
            <p class="title">
              应收账款
            </p>
          </div>
          <trade-receivable-detail v-if="infoForm.receivableList?.length > 0" ref="receivableDetailRef" :show-title="false" view-mode="sub" />
          <el-empty v-else description="请选择应收账款" />
          <!-- 申请资料 -->
          <profiles-list :local-base-table="infoForm.productDocuments" readonly />
          <attachment-list v-model="infoForm" :business-id="infoForm.pid" business-type="USE_CREDIT_APPLY" readonly />
          <div class="area-title">
            <p class="title">
              备注信息
            </p>
          </div>
          <div class="form-inner">
            <el-form-item>
              {{ $utils.isEffectiveCommon(infoForm.remark) }}
            </el-form-item>
          </div>
          <div class="area-title">
            <p class="title">
              操作记录
            </p>
          </div>
          <div class="form-inner">
            <operation-log :table-list-data="logList" />
          </div>
        </el-form>
      </div>
    </div>
    <dialog-inner v-model="dialog.audit" title="审批" :loading="loading.confirm" width="30%" destroy-on-close @submit="confirmApproval" @close="closeDialog">
      <div class="form-area">
        <el-form ref="approvalInfoFormRef" :model="approvalInfoForm" :rules="rules">
          <div class="form-inner">
            <!--<fe-form-item label="是否审批通过" prop="approvalFlag">
              <el-radio-group v-model="approvalInfoForm.approvalFlag">
                <el-radio value="1">
                  通过
                </el-radio>
                <el-radio value="0">
                  驳回
                </el-radio>
              </el-radio-group>
            </fe-form-item>-->
            <fe-form-item label="备注信息">
              <el-input v-model="approvalInfoForm.remark" type="textarea" maxlength="100" rows="3" show-word-limit placeholder="请输入备注信息" />
            </fe-form-item>
          </div>
        </el-form>
      </div>
    </dialog-inner>
    <!-- 提前打款信息 -->
    <pre-pay-detail ref="prePayRef" />
  </fe-page-main>
</template>

<style>

</style>
