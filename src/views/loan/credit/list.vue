<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import { basePageListMixin } from '@fero/commons-vue'
import ChooseCompanyDialog from '@/views/loan/credit/components/choose-company-dialog.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import FeTableLink from '@/components/base/fe-table-link.vue'
import ContractTableLink from '@/components/contract-table-link.vue'
import ScopePropFormatter from '@/components/scope-prop-formatter.vue'
import RepayDetailDialog from '@/views/loan/components/repay-detail-dialog.vue'

export default {
  components: { RepayDetailDialog, ScopePropFormatter, ContractTableLink, FeTableLink, FeFormItem, ChooseCompanyDialog, FePageMain },
  mixins: [basePageListMixin],
  data () {
    return {
      columnOptionList: [
        { label: '融资申请编号', prop: 'useCode', formatter: this.$utils.isEffective, minWidth: 150 },
        { label: '客户企业', prop: 'useCompanyName', formatter: this.$utils.isEffective, minWidth: 150 },
        { label: '融资状态', prop: 'status', formatter: this.$utils.tableStatusFormat('USE_APPLY_STATUS'), minWidth: 100, slotName: 'status' },
        // { label: '审批状态', prop: 'financialApproveStatus', formatter: this.$utils.tableStatusFormat('USE_APPLY_FINANCIAL_STATUS'), minWidth: 100 },
        { label: '合同状态', prop: 'contractSigningStatus', formatter: this.$utils.tableStatusFormat('PC_CONTRACT_SIGN'), minWidth: 100, slotName: 'contractSigningStatus' },
        { label: '资金方', prop: 'financialCompanyName', formatter: this.$utils.isEffective, minWidth: 150 },
        { label: '产品名称', prop: 'productName', formatter: this.$utils.isEffective, minWidth: 150 },
        { label: '融资金额（元）', prop: 'approvedAmount', formatter: this.$utils.tableMoneyFormat, minWidth: 180 },
        { label: '融资日期', prop: 'planUseDate', formatter: this.$utils.tableDateFormat, minWidth: 150 },
        { label: '授信编号', prop: 'applyCreditCode', formatter: this.$utils.isEffective, minWidth: 150, slotName: 'applyCreditCode' },
        { label: '创建时间', prop: 'createTime', formatter: this.$utils.tableDateFormat, minWidth: 150 }
      ]
    }
  },
  computed: {
  },
  methods: {
    preview (row) {
      this.$refs.repayDetailDialog.init(row.pid)
    },
    addLoanApply () {
      this.$refs.companyDialog.dialogInit()
    },
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.loan.apply.pageList(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    del (row) {
      this.$confirm('确认删除该融资申请？', '确认删除').then(() => {
        this.$api.loan.apply.delete(row.pid).then(result => {
          this.$utils.resultBaseMessageV2(result)
          this.getList()
        })
      })
    },
    // confirmPayment (row) {
    //   this.$confirm('确认付款？', '确认').then(() => {
    //     this.$api.loan.apply.confirmPayment({ pid: row.pid }).then(result => {
    //       this.$utils.resultBaseMessageV2(result)
    //       this.getList()
    //     })
    //   })
    // },
    setStatusColor ({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'status') {
        switch (row.status) {
          case 'effect':
            return 'success'
          case 'returned':
            return 'danger'
        }
      } else if (column.property === 'financialApproveStatus') {
        switch (row.financialApproveStatus) {
          case 'effect':
            return 'success'
          case 'returned':
            return 'danger'
        }
      }
    },
    setRowColor ({ row }) {
      if (row.status === 'overdue') {
        return 'danger-row'
      }
    }
  }
}
</script>

<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <fe-form-item label="融资申请编号">
            <el-input v-model="searchForm.useCode" placeholder="请输入融资申请编号" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="产品名称">
            <el-input v-model="searchForm.productName" placeholder="请输入产品名称" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="融资状态">
            <el-select v-model="searchForm.status" placeholder="请选择融资状态">
              <el-option v-for="item in $utils.getEnableDictStatus('USE_APPLY_STATUS')" :key="item.value" :value="item.value" :label="item.label" />
            </el-select>
          </fe-form-item>
        </el-col>
        <!--<el-col :span="6">-->
        <!--  <fe-form-item label="生产计划年月">-->
        <!--    <el-date-picker v-model="searchPrepaymentDate" type="monthrange" format="YYYY-MM" value-format="x" start-placeholder="请选择生产计划开始年月" end-placeholder="请选择生产计划结束年月" />-->
        <!--  </fe-form-item>-->
        <!--</el-col>-->
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" :table-data="tableList.records" show-set-columns :column-option="columnOptionList" :cell-class-name="setStatusColor" :row-class-name="setRowColor" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe title="信息明细" @change-page-num="changePageNum" @change-page-size="changePageSize">
        <template #btn-inner>
          <el-button type="primary" @click="addLoanApply">
            新增申请
          </el-button>
        </template>
        <template #status="scope">
          <el-link v-if="['repayment', 'overdue', 'end'].includes(scope.row.status)" type="primary" @click="preview(scope.row)">
            <scope-prop-formatter :scope="scope" />
          </el-link>
          <scope-prop-formatter v-else :scope="scope" />
        </template>
        <template #contractSigningStatus="scope">
          <contract-table-link type="use" :scope="scope" />
        </template>
        <template #applyCreditCode="scope">
          <fe-table-link :scope="scope" :route-info="{ name: 'creditManagementApplicationDetail' }" pid-key="applyCreditId" />
        </template>
        <template #table-columns-after>
          <el-table-column fixed="right" label="操作" width="120">
            <template #default="scope">
              <table-link :to="{ name: 'loanCreditDetail', query: { pid: scope.row.pid } }" type="primary">
                详情
              </table-link>
              <table-link v-if="['notSubmitted'].includes(scope.row.status)" :to="{ name: 'loanCreditEdit', query: { pid: scope.row.pid } }" type="primary">
                编辑
              </table-link>
              <!--<table-link v-if="['financialApproval'].includes(scope.row.status) && ['pendingApproval'].includes(scope.row.financialApproveStatus)" :to="{ name: 'loanApplyAudit', query: { pid: scope.row.pid } }" type="primary">
                审批
              </table-link>-->
              <!--<table-link v-if="['pendingAccept'].includes(scope.row.status)" :to="{ name: 'loanApplyAcceptance', query: { pid: scope.row.pid } }" type="primary">-->
              <!--  受理-->
              <!--</table-link>-->
              <!--<el-link v-if="['WAIT_SIGN'].includes(scope.row.contractSigningStatus)" type="primary">-->
              <!--  发起签署-->
              <!--</el-link>-->
<!--              <el-link v-if="['payment'].includes(scope.row.status)" type="primary" @click="confirmPayment(scope.row)">-->
<!--                确认付款-->
<!--              </el-link>-->
<!--              <el-link v-if="['notSubmitted'].includes(scope.row.status)" type="danger" @click="del(scope.row)">-->
<!--                删除-->
<!--              </el-link>-->
            </template>
          </el-table-column>
        </template>
      </table-inner>
    </div>
    <choose-company-dialog ref="companyDialog" router-path="loanCreditAdd" />
    <repay-detail-dialog ref="repayDetailDialog" type="use" />
  </fe-page-main>
</template>

<style>

</style>
