<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import { datePickAttr } from '@/assets/lib/misc'
import DetailInput from '@/components/base/detail-input.vue'
import AttachmentList from '@/components/attachment-list.vue'
import { cloneDeep, defaultsDeep, find, get } from 'lodash'
import OperationLog from '@/components/operation-log.vue'
import dayjs from 'dayjs'
import ProfilesList from '@/views/creditManagement/components/profiles-list.vue'
import BigNumber from 'bignumber.js'
import AmountInput from '@/components/amount-input.vue'
import TradeReceivableDialog from '@/views/assetsManagement/components/trade-receivable-dialog.vue'
import TradeReceivableDetail from '@/views/assetsManagement/components/trade-receivable-detail.vue'
import { nextTick } from 'vue'

export default {
  components: { TradeReceivableDetail, TradeReceivableDialog, AmountInput, ProfilesList, OperationLog, AttachmentList, DetailInput, FeFormItem, FePageMain },
  data () {
    return {
      pid: '',
      originalInfoForm: {},
      infoForm: {
        productDocuments: []
      },
      rules: {
        planUseAmount: this.$rulesToolkit.createRules({ name: '申请融资金额', required: true }),
        financialCompanyId: this.$rulesToolkit.createRules({ name: '授信资金方', required: true, trigger: 'change' }),
        applyCreditId: this.$rulesToolkit.createRules({ name: '关联授信申请', required: true, trigger: 'change' }),
        payeeBankAccount: this.$rulesToolkit.createRules({ name: '银行账号', required: true, trigger: 'change' })
      },
      capitalCompanyList: [],
      creditList: [],
      creditInfo: {
        productSnapshot: {}
      },
      coreCreditInfo: {},
      logList: [],
      loading: {
        submit: false,
        detail: false
      },
      interest: 0,
      bankAccountList: [],
      receivableInfo: {}
    }
  },
  computed: {
    datePickAttr () {
      return datePickAttr
    }
  },
  created () {
    this.init()
    this.getCapitalCompanyList()
  },
  methods: {
    init () {
      if (this.$route.query.pid) {
        this.pid = this.$route.query.pid
        this.getDetail()
      } else {
        this.infoForm.useCompanyName = this.$route.query.companyName || ''
        this.infoForm.useCompanyCode = this.$route.query.companyCode || ''
        this.infoForm.useCompanyId = this.$route.query.companyId || ''
        this.infoForm.useSocialCreditCode = this.$route.query.socialCreditCode || ''
        this.$api.creditManagement.credit.productInfo(this.$route.query.productId).then((result) => {
          const resData = defaultsDeep(result.data.data, { productDocuments: [] })
          this.infoForm.productName = resData.productName
          this.infoForm.productId = resData.pid
          this.infoForm.financialCompanyId = resData.financialCompanyId
          this.infoForm.financialCompanyName = resData.financialCompanyName
          this.infoForm.financialCompanyCode = resData.financialCompanyCode
          this.infoForm.financialSocialCreditCode = resData.financialSocialCreditCode
          this.getCreditList()
        })
        this.getCompanyAccount(this.infoForm.useCompanyId)
      }
    },
    getDetail () {
      this.loading.detail = true
      this.$api.loan.apply.detail(this.pid).then(result => {
        this.infoForm = defaultsDeep(result.data.data, { productDocuments: [] })
        this.originalInfoForm = cloneDeep(this.infoForm)
        this.logList = this.infoForm.operatingRecordEntityList ?? []
        this.getCreditList()
        this.getCreditDetail(this.infoForm.applyCreditId).then(result => {
          this.calculateInterest()
        })
        this.getCoreCompanyCreditInfo(this.infoForm.coreCompanyId, this.infoForm.productId)
        const receivableInfo = get(this.infoForm, 'receivableList[0]')
        this.receivableInfo = receivableInfo ?? {}
        if (receivableInfo) {
          nextTick(() => {
            this.$refs.receivableDetailRef.init(receivableInfo)
          })
        }
      }).finally(() => {
        this.loading.detail = false
      })
    },
    getCompanyAccount (pid) {
      this.$api.business.getlog(pid).then(result => {
        this.bankAccountList = result.data.data?.bankList ?? []
        const defaultAccount = find(this.bankAccountList, { isDefault: 1 })
        if (defaultAccount) {
          this.infoForm.payeeBankAccount = defaultAccount.bankNo
          this.infoForm.payeeBankNo = defaultAccount.openingBankNo
          this.infoForm.payeeBank = defaultAccount.openingBank
          this.infoForm.payeeName = defaultAccount.accountNoName
        }
      })
    },
    changeBankAccount (value) {
      const account = find(this.bankAccountList, { bankNo: value }) ?? {}
      this.infoForm.payeeBankNo = account.openingBankNo
      this.infoForm.payeeBank = account.openingBank
      this.infoForm.payeeName = account.accountNoName
    },
    save (showMessage = true) {
      return new Promise((resolve, reject) => {
        this.$refs.infoForm.validate().then(() => {
          const validateFile = this.infoForm.productDocuments.some(
            (item) => !item.fileJson && item.isRequired === 1
          )
          // 申请授信金额
          const planUseAmount = new BigNumber(this.infoForm.planUseAmount ?? 0)
          // 融资比例（百分数）
          const financingRatio = new BigNumber(this.creditInfo.financingRatio ?? 0)
          // 应收账款金额
          const receivableAmount = new BigNumber(this.receivableInfo.receivableAmount ?? 0)
          if (validateFile) {
            return this.$message.error('缺少必填申请材料,请重新上传')
          }
          if (planUseAmount.isGreaterThan(this.creditInfo.remainingAmount ?? 0)) {
            return this.$message.error('申请融资金额大于融资企业可用授信额度')
          }
          if (this.creditInfo.productSnapshot.supportCore && !this.infoForm.coreApplyCreditId) {
            return this.$message.error('核心企业授信不存在')
          }
          if (this.creditInfo.productSnapshot.supportCore && (planUseAmount.isGreaterThan(this.coreCreditInfo.remainingAmount ?? 0))) {
            return this.$message.error('申请融资金额大于核心企业可用授信额度')
          }
          // 融资期限
          const limit = dayjs(this.infoForm.planRepaymentDate).startOf('day').diff(dayjs(this.infoForm.planUseDate).startOf('day'), 'day')
          if (limit < 0) {
            return this.$message.error('申请还款日（到期日）不能小于申请放款日（起息日）')
          }
          if (!this.infoForm.receivableList || this.infoForm.receivableList.length === 0) {
            return this.$message.error('请选择应收账款')
          }
          if (planUseAmount.isGreaterThan(receivableAmount.times(financingRatio).div(100))) {
            return this.$message.error('申请融资金额大于应收账款金额 * 融资比例')
          }
          const formData = cloneDeep(this.infoForm)
          this.loading.submit = true
          this.$api.loan.apply.save(formData).then(result => {
            if (showMessage) {
              this.$utils.resultBaseMessage(result)
              this.$router.back()
            }
            if (result.data.data && !this.infoForm.pid) {
              this.infoForm.pid = result.data.data.pid || result.data.data
              this.pid = this.infoForm.pid
            }
            resolve(result)
          }).catch(error => {
            reject(error)
          }).finally(() => {
            this.loading.submit = false
          })
        }).catch(error => {
          reject(error)
        })
      })
    },
    confirm () {
      this.save(false).then(() => {
        this.$api.loan.apply.confirm(this.infoForm.pid).then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        })
      })
    },
    calculateInterest () {
      if (!this.infoForm.planUseAmount || !this.creditInfo.productAnnualInterest || !this.infoForm.planUseDate || !this.infoForm.planRepaymentDate) {
        this.interest = 0
        return false
      }
      // 融资金额
      const planUseAmount = new BigNumber(this.infoForm.planUseAmount ?? 0)
      // 融资利率（年化）
      const productAnnualRate = new BigNumber(this.creditInfo.productAnnualInterest ?? 0)
      // 融资期限
      const limit = dayjs(this.infoForm.planRepaymentDate).startOf('day').diff(dayjs(this.infoForm.planUseDate).startOf('day'), 'day')
      const interest = planUseAmount.times(limit).times(productAnnualRate).div(100).div(360)
      this.interest = interest.toNumber()
    },
    applyCreditIdChange (pid, init) {
      const creditInfo = find(this.creditList, { pid }) || {}
      this.infoForm.applyCreditCode = creditInfo.applyCreditCode || ''
      this.getCreditDetail(pid).then(result => {
        const productDocuments = result.productSnapshot?.useCreditDocumentsList ?? []
        productDocuments.forEach(item => {
          delete item.pid
        })
        this.infoForm.productDocuments = result.productSnapshot?.useCreditDocumentsList ?? []
        this.infoForm.contractSigningOption = result.productSnapshot?.useContractSigningOption
        this.infoForm.coreCompanyName = result.productSnapshot?.coreCompanyName || ''
        this.infoForm.coreCompanyId = result.productSnapshot?.coreCompanyId || ''
        this.infoForm.coreSocialCreditCode = result.productSnapshot?.coreSocialCreditCode || ''
        this.infoForm.coreCompanyCode = result.productSnapshot?.coreCompanyCode || ''
        this.infoForm.operateCompanyId = result.productSnapshot?.operateCompanyId || ''
        this.infoForm.operateSocialCreditCode = result.productSnapshot?.operateSocialCreditCode || ''
        this.infoForm.operateCompanyCode = result.productSnapshot?.operateCompanyCode || ''
        this.infoForm.operateCompanyName = result.productSnapshot?.operateCompanyName || ''
        this.calculateInterest()
        if (this.infoForm.coreCompanyId) {
          this.getCoreCompanyCreditInfo(this.infoForm.coreCompanyId, this.infoForm.productId)
        }
      })
    },
    getCoreCompanyCreditInfo (companyId, productId) {
      if (!companyId) return false
      this.$api.creditManagement.coreCredit.getCoreInfo(companyId, productId).then(result => {
        this.coreCreditInfo = result.data.data ?? {}
        this.infoForm.coreApplyCreditId = get(this.coreCreditInfo, 'creditApplyList[0].pid')
      })
    },
    getCapitalCompanyList () {
      this.$api.warehouse.companyList({ type: 'FUND' }).then(result => {
        this.capitalCompanyList = result.data.data || []
      })
    },
    getCreditList () {
      this.$api.creditManagement.credit.list({
        status: 'EFFECT',
        financialCompanyId: this.infoForm.financialCompanyId,
        companyId: this.infoForm.useCompanyId,
        productId: this.infoForm.productId
      }).then(result => {
        this.creditList = result.data.data || []
      })
    },
    getCreditDetail (pid) {
      return new Promise((resolve, reject) => {
        if (!pid) {
          return reject(new Error('pid is required'))
        }
        Promise.all([
          this.$api.creditManagement.credit.info(pid)
          // this.$api.creditManagement.quotaViewing.info(pid)
        ]).then(result => {
          this.creditInfo = defaultsDeep(result[0].data.data, { productSnapshot: {} })
          resolve(this.creditInfo)
        }).catch(error => {
          reject(error)
        })
      })
    },
    setDisableDate (data) {
      return dayjs().startOf('day').isAfter(data)
    },
    pickReceivable () {
      this.$refs.pickReceivableRef.pick().then(result => {
        this.infoForm.receivableList = [{ receivableId: result.receivableId }]
        this.$nextTick(() => {
          this.$refs.receivableDetailRef.init(result.receivableId).then(result => {
            this.receivableInfo = result
          })
        })
      })
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <template #btn-inner>
      <el-button :loading="loading.submit" type="primary" @click="save">
        保存
      </el-button>
      <el-button :loading="loading.submit" type="primary" @click="confirm">
        提交
      </el-button>
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form ref="infoForm" :model="infoForm" :rules="rules" scroll-to-error>
          <div class="area-title">
            <p class="title">
              申请企业信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="企业名称">
                  <detail-input :model-value="infoForm.useCompanyName" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="统一社会信用代码">
                  <detail-input :model-value="infoForm.useSocialCreditCode" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              融资方案
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="金融产品">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.productName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="financialCompanyId" label="授信资金方">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.financialCompanyName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="申请融资编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.useCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item
                  label="关联授信申请" prop="applyCreditId" is-link :can-jump="!!infoForm.applyCreditId"
                  :route-info="{ name: 'creditManagementApplicationDetail', query: { pid: infoForm.applyCreditId } }"
                >
                  <el-select
                    v-model="infoForm.applyCreditId" placeholder="请选择关联授信申请"
                    @change="applyCreditIdChange"
                  >
                    <el-option
                      v-for="item in creditList" :key="item.pid"
                      :label="`${item.applyCreditCode}（${item.productName}）`" :value="item.pid"
                    />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="planUseDate" label="申请放款日（起息日）">
                  <el-date-picker
                    v-model="infoForm.planUseDate" :disabled-date="setDisableDate" v-bind="datePickAttr"
                    placeholder="请选择申请放款日（起息日）"
                    @change="calculateInterest"
                  />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="planRepaymentDate" label="申请还款日（到期日）">
                  <el-date-picker
                    v-model="infoForm.planRepaymentDate" :disabled-date="setDisableDate" v-bind="datePickAttr"
                    placeholder="请选择申请还款日（到期日）"
                    @change="calculateInterest"
                  />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="年化利率（%）">
                  <detail-input :model-value="$utils.moneyFormat(creditInfo.productAnnualInterest)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="planUseAmount" label="申请融资金额（元）">
                  <template #label>
                    <div class="flex">
                      <span>申请融资金额（元）</span>
                      <span v-if="interest > 0" class="text-orange-500 ml-4">预计利息：{{ $utils.moneyFormat(interest) }}</span>
                    </div>
                  </template>
                  <amount-input
                    v-model="infoForm.planUseAmount" :currency-input-option="{ valueRange: { min: 0.01 } }" placeholder="请输入申请融资金额"
                    @change="calculateInterest"
                  />
                  <!--                  <el-input-number v-model="infoForm.planUseAmount" v-bind="inputNumberAttr" placeholder="请输入申请融资金额" />-->
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              收款信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item prop="payeeBankAccount" label="银行账号">
                  <el-select v-model="infoForm.payeeBankAccount" @change="changeBankAccount">
                    <el-option v-for="item in bankAccountList" :key="item.pid" :label="item.bankNo" :value="item.bankNo" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="账户名称">
                  <detail-input :model-value="infoForm.payeeName" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="开户银行">
                  <detail-input :model-value="infoForm.payeeBank" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="开户行号">
                  <detail-input :model-value="infoForm.payeeBankNo" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              授信额度
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="融资企业名称">
                  <detail-input :model-value="infoForm.useCompanyName" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="融资企业可用额度">
                  <detail-input :model-value="$utils.moneyFormat(creditInfo.remainingAmount)" />
                </fe-form-item>
              </el-col>
              <template v-if="creditInfo.productSnapshot.supportCore">
                <el-col :span="12">
                  <fe-form-item label="核心企业名称">
                    <detail-input :model-value="infoForm.coreCompanyName" />
                  </fe-form-item>
                </el-col>
                <el-col :span="12">
                  <fe-form-item label="核心企业可用额度">
                    <detail-input :model-value="$utils.moneyFormat(coreCreditInfo.remainingAmount)" />
                  </fe-form-item>
                </el-col>
              </template>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              应收账款
            </p>
            <div class="btn-inner">
              <el-button type="primary" @click="pickReceivable">
                选择应收账款
              </el-button>
            </div>
          </div>
          <trade-receivable-detail v-if="infoForm.receivableList?.length > 0" ref="receivableDetailRef" :show-title="false" view-mode="sub" />
          <el-empty v-else description="请选择应收账款" />
          <!-- 申请资料 -->
          <profiles-list v-model:local-base-table="infoForm.productDocuments" />
          <attachment-list v-model="infoForm" :business-id="infoForm.pid" business-type="USE_CREDIT_APPLY" />
          <div class="area-title">
            <p class="title">
              备注信息
            </p>
          </div>
          <div class="form-inner">
            <el-input
              v-model="infoForm.remark" type="textarea" :rows="4" :maxlength="2000" show-word-limit
              placeholder="请填写备注信息"
            />
          </div>
          <div class="area-title">
            <p class="title">
              操作记录
            </p>
          </div>
          <div class="form-inner">
            <operation-log :table-list-data="logList" />
          </div>
        </el-form>
      </div>
    </div>
    <trade-receivable-dialog ref="pickReceivableRef" :default-search="{ assetsStatus: 'UNFINANCED' }" />
  </fe-page-main>
</template>

<style>

</style>
