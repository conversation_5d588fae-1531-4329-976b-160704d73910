<template>
  <div class="login-page" v-loading="loading.info">
    <div class="login-container">
      <div class="login-content">
        <div class="login-left">
          <img :src="logo" width="300px" height="100px" alt="云镝">
          <div class="main-logo-inner">
            <span>云镝数字资产管理平台</span>
          </div>
          <img :src="productDrawing" width="400px" height="400px" alt="云镝">
<!--          <div class="slogan-inner">-->
<!--            <p class="sub-desc">-->
<!--              高效便捷，全程无忧，为企业提供智能化的供应链融资服务-->
<!--            </p>-->
<!--          </div>-->
        </div>
        <div class="login-right">
          <div class="login-main">
            <div class="login-header">
              <h2>欢迎登录</h2>
              <span>{{ platformType }}</span>
            </div>
            <div class="login-way">
              <el-radio-group v-model="loginType" size="large" :style="{width: '400px'}" @change="changeType">
                <el-radio-button label="账号密码登录" value="password" />
                <el-radio-button label="手机验证码登录" value="code" />
              </el-radio-group>
            </div>
            <el-form v-model="loginForm" class="login-form" label-position="top" @submit.prevent="loginSubmit">
              <el-form-item v-if="loginType === 'password'" prop="username">
                <el-input v-model="loginForm.username" placeholder="请输入账号" />
              </el-form-item>
              <el-form-item v-if="loginType === 'code'" prop="mobile">
                <el-input v-model="loginForm.mobile" placeholder="请输入手机号" />
              </el-form-item>
              <el-form-item v-if="loginType === 'password'" prop="password">
                <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" />
              </el-form-item>
              <el-form-item class="verification-form-item" prop="checkCode">
                <el-input v-model="loginForm.checkCode" placeholder="请输入图片验证码" />
                <a v-loading="loading.checkCode" class="verification-img" href="javascript:" @click="getCaptcha">
                  <img :src="imagePath" alt="verification-img">
                </a>
              </el-form-item>
              <el-form-item v-if="loginType === 'code'" class="verification-form-item" prop="mobileCode">
                <el-input v-model="loginForm.validCode" placeholder="请输入手机验证码" />
                <el-button :loading="loading.sendCode" :disabled="!sendCodeBtnStatus" type="primary" class="send-code-btn" @click="sendMobileCode()">
                  {{ sendCodeBtnText }}
                </el-button>
              </el-form-item>
              <div class="login-info">
                <div>登录 <span class="agreement-link"> {{ productName }} </span> 将获得以下权限</div>
                <div class="privacy-info">您的昵称、手机号、邮箱、企业产品信息</div>
                <div class="decorative-line" />
                <div class="privacy-agreement">
                  <el-checkbox v-model="isAgreed" />
                  <div class="privacy-text">我同意<span class="agreement-link">《用户服务协议》</span>和<span class="agreement-link">《隐私政策》</span></div>
                </div>
                <div class="forget-password">
                  <a href="/findPassword">忘记密码</a>
                </div>
              </div>
              <el-button :loading="loading.submit" class="login-submit-btn" type="primary" native-type="submit">
                登录
              </el-button>
              <!-- <div class="login-link-inner">
                <a v-if="loginType === 'password'" href="javascript:" @click="changeType('code')">手机验证码登录</a>
                <a v-else-if="loginType === 'code'" href="javascript:" @click="changeType('password')">密码登录</a>
              </div> -->
              <div class="archival-information">
                <div>{{ companyName }}</div>
                <div class="archival-number">
                  <div>ICP备案号：{{ icpArchival }}</div>
                  <div class="archival-box">
                    <img :src="recordIcon" alt="备案信息" class="archival-icon">
                    <span>{{ websiteArchival }}</span>
                  </div>
                </div>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { cloneDeep, get } from 'lodash'
import { app } from '@/assets/lib/app'
import logo from '@/assets/images/logo-dark.png'
import productDrawing from '@/assets/images/left-login.png'
import recordIcon from '@/assets/images/icon-record.png'
export default {
  name: 'PageLogin',
  components: {
  },
  props: {
    cookie: {
      type: Object,
      required: true,
      default () {
        return {}
      }
    },
    useBaseStore: {
      type: Function,
      required: true,
      default () {
        return undefined
      }
    },
    logoImg: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      logo,
      productDrawing,
      recordIcon,
      icpArchival: import.meta.env.VITE_ICP_ARCHIVAL,
      websiteArchival: import.meta.env.VITE_WEBSITE_ARCHIVAL,
      companyName: import.meta.env.VITE_COMPANY_NAME,
      productName: import.meta.env.VITE_PRODUCT_NAME,
      platformType: import.meta.env.VITE_PLATFORM_TYPE,
      isAgreed: import.meta.env.VITE_LOIGIN_IS_AGREED ? import.meta.env.VITE_LOIGIN_IS_AGREED === 'true' : false,
      loginType: 'password',
      sendCodeBtnText: '获取手机验证码',
      sendCodeBtnStatus: false,
      imagePath: '',
      loading: {
        submit: false
      },
      loginForm: {
        username: '',
        password: '',
        key: '',
        checkCode: '',
        grant_type: 'password',
        scope: 'all'
      }
    }
  },
  watch: {},
  mounted () {
    this.baseStore.removeEmployeeInfo()
  },
  created () {
    this.baseStore = this.useBaseStore(this.$pinia)
    this.checkSendCodeStatus()
    this.getCaptcha()
  },
  methods: {
    changeType (value) {
      this.loginType = value
    },
    getCaptcha () {
      this.loading.checkCode = true
      this.$api.base.captcha().then(result => {
        this.loginForm.key = result.data.key
        this.imagePath = result.data.image
        this.loginForm.checkCode = result.data.code
      }).finally(() => {
        this.loading.checkCode = false
      })
    },
    loginSubmit () {
      if (!this.isAgreed) {
        this.$message.warning('请同意用户使用协议和隐私政策')
        return
      }
      if (this.loginType === 'password') {
        if (!this.loginForm.username) {
          this.$message.error('请输入账号')
          return false
        }
        if (!this.loginForm.password) {
          this.$message.error('请输入密码')
          return false
        }
        if (!this.loginForm.checkCode) {
          this.$message.error('请输入验证码')
          return false
        }
      } else {
        if (!this.loginForm.mobile) {
          this.$message.error('请输入手机号')
          return false
        }
        if (!this.loginForm.validCode) {
          this.$message.error('请输入手机验证码')
          return false
        }
      }
      this.loading.submit = true
      const formData = cloneDeep(this.loginForm)
      delete formData.key
      delete formData.checkCode
      delete formData.validCode
      if (this.loginType === 'code') {
        formData.grant_type = 'phone'
        delete formData.username
      } else {
        formData.grant_type = 'password'
        delete formData.mobile
      }
      this.$api.base.login(
        formData,
        {
          Authorization: 'Basic d2ViOndlYl9zZWNyZXQ=',
          'Captcha-Key': this.loginForm.key,
          'Captcha-Code': this.loginForm.checkCode,
          'Phone-Code': this.loginForm.validCode
        },
        {
          customErr: { default: true }
        }
      ).then(result => {
        this.cookie.set('token', result.data.access_token, { expires: 30 })
        this.cookie.set('refresh_token', result.data.refresh_token, { expires: 30 })
        const employeeInfo = result.data || {}
        employeeInfo.username = employeeInfo.account
        employeeInfo.name = employeeInfo.user_name
        this.baseStore.employeeInfo = employeeInfo
        const getFeature = this.$api.base.getFeature
        getFeature().then(result => {
          this.baseStore.featureList = result.data.data || []
          app._context.components.AuthManage.props.featureList.default = this.baseStore.featureList
        }).catch(e => {
          console.error('获取用户功能权限列表失败', e)
        }).finally(() => {
          if (import.meta.env.VITE_REDIRECT_STATUS === 'ON' && this.$route.query.redirecturl) {
            window.location.href = this.$route.query.redirecturl
          } else {
            this.$router.push({ name: 'index' })
          }
        })
      }).catch(e => {
        if (![200, 401].includes(e.response.status)) this.$message.error(get(e, 'response.data.error_description', '') || '系统异常，请稍后重试')
      }).finally(() => {
        this.loading.submit = false
      })
    },
    checkSendCodeStatus (countdown) {
      this.sendCodeBtnStatus = false
      let nowCountdown = countdown || 0
      const savedSendCodeCountDown = window.localStorage.getItem('sendCodeCountDown')
      if (!countdown && savedSendCodeCountDown) {
        nowCountdown = Number(savedSendCodeCountDown)
      }
      if (nowCountdown <= 0) {
        this.sendCodeBtnStatus = true
        this.sendCodeBtnText = '发送手机验证码'
      } else {
        this.timer = setInterval(() => {
          this.sendCodeBtnText = `${nowCountdown}秒后重新获取`
          if (nowCountdown <= 0) {
            this.sendCodeBtnStatus = true
            this.sendCodeBtnText = '发送手机验证码'
            clearInterval(this.timer)
            this.timer = null
          }
          nowCountdown = nowCountdown - 1
          window.localStorage.setItem('sendCodeCountDown', nowCountdown)
        }, 1000)
      }
    },
    sendMobileCode () {
      if (!this.loginForm.mobile) {
        this.$message.error('请输入手机号')
        return false
      }
      if (!this.loginForm.checkCode) {
        this.$message.error('请输入验证码')
        return false
      }
      this.loading.sendCode = true
      this.$api.base.getLoginSmsCode(
        {
          mobile: this.loginForm.mobile
        },
        {
          'Captcha-Key': this.loginForm.key,
          'Captcha-Code': this.loginForm.checkCode
        }
      ).then(result => {
        this.$utils.resultBaseMessage(result)
        this.checkSendCodeStatus(60)
      }).finally(() => {
        this.loading.sendCode = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  min-width: 1100px;
  background-size: cover;
  background: no-repeat 100% url("../assets/images/login-bg.png");
  background-size: cover;
}

.login-container {
  width: 1000px;
  position: relative;
  margin: 0 auto;
  background-color: #003b92;
  border-radius: 12px;
  padding: 30px;
}

.login-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-radius: 12px;
  //background: #fff;
  //box-shadow: 0 6px 32px 0 rgba(94, 137, 189, 0.1);
  margin: 0 auto;
  height: 510px;
  //background-color: #999999;
}

.login-left {
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  //background: url("@/assets/images/bg-login.png") no-repeat;
  background-size: cover;
  border-top-left-radius: 12px;
  border-bottom-left-radius: 12px;

  .main-logo-inner {
    width: 100%;
    height: 48px;
    text-align: center;
    margin-top: 20px;
    margin-bottom: 20px;

    span {
      font-size: 33px;
      color: #34b4f7;
      font-weight: bold;
    }
  }

  .slogan-inner {
    color: #fff;
    z-index: 10;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 40px;

    .sub-desc {
      font-size: 24px;
      line-height: 36px;
      margin-top: 20px;
    }
  }
}

.login-right {
  width: 50%;
  height: 100%;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-main {
  display: flex;
  flex-direction: column;
  align-items: baseline;
  .verification-form-item {
    .el-input {
      width: calc(100% - 110px);
    }
    .verification-img {
      display: inline-block;
      width: 100px;
      height: 40px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .send-code-btn {
      height: 42px;
      width: 100px;
    }
    &:deep(.el-form-item__content) {
      justify-content: space-between;
    }
  }
}
.login-header {
  display: flex;
  justify-content: start;
  gap: 10px;
  width: 100%;
  align-items: center;
  margin-bottom: 10px;
  h2 {
    color: #FFFFFF;
    font-size: 24px;
    font-weight: 700;
  }
  span {
    color: #FFFFFF;
    font-size: 24px;
    font-weight: 600;
  }
}
.login-form {
  position: relative;
  width: 400px;
  padding: 0;
}
.login-submit-btn {
  width: 100%;
  font-size: 18px;
  color: #fff;
  //margin-top: 20px;
  padding: 10px 15px;
  height: 40px;
}

.login-form .el-form-item {
  margin-bottom: 14px;
  &:deep(.el-input__inner) {
    font-size: 14px;
    height: 40px;
    line-height: 40px;
  }
  &:deep(.el-form-item__label) {
    font-size: 16px;
  }
}
.privacy-agreement {
  display: flex;
  justify-content: start;
  align-items: start;
  gap: 4px;
  margin-top: 10px;
}
.el-checkbox {
  display: flex;
  align-items: start;
  position: relative;
  top: 2px;
}
.agreement-link {
  color: #61a6f8;
}
.privacy-text {
  text-align: left;
}
.privacy-info {
  color: #9a9a9a;
}
.decorative-line {
  width: 100%;
  height: 6px;
  border-bottom: 1px dashed #9a9a9a;
  margin: 10px 0;
}
.login-info {
  color: #9a9a9a;
  display: flex;
  flex-direction: column;
  align-items: start;
  width: 100%;
  margin-bottom: 5px;
}
.forget-password {
  display: flex;
  justify-content: right;
  margin-top: 5px;
  width: 100%;
  a {
    color: #FFFFFF;
    text-decoration: none;
  }
}
.archival-information {
  display: flex;
  flex-direction: column;
  align-items: start;
  margin-top: 30px;
  gap: 4px;
  width: 100%;
  font-size: 12px;
  color: #999;
  .archival-number {
    display: flex;
    justify-content: start;
    gap: 10px;
  }
  .archival-box {
    display: flex;
    align-items: center;
    .archival-icon {
      width: 16px;
      height: 16px;
      margin-right: 5px;
    }
  }
}
.login-way {
  margin-bottom: 15px;
  width: 100%;
  .el-radio-group {
    width: 100%;
  }
  .el-radio-button {
    width: 50%;
    &:deep(.el-radio-button__inner) {
      width: 100%;
    }
  }
}
</style>
