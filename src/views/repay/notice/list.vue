<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeSearchInner from '@/components/base/fe-search-inner.vue'
import { basePageListMixin } from '@fero/commons-vue'

export default {
  components: { FeSearchInner, FePageMain },
  mixins: [basePageListMixin],
  data () {
    return {
      columnOptionList: [
        {
          label: '还款通知编号',
          prop: 'repayNoticeCode',
          minWidth: 160,
          formatter: this.$utils.isEffective
        },
        {
          label: '还款类型',
          prop: 'payType',
          minWidth: 120,
          formatter: this.$utils.tableStatusFormat('REPAY_NOTICE_TYPE')
        },
        {
          label: '还款方',
          prop: 'payerName',
          minWidth: 150,
          formatter: this.$utils.isEffective
        },
        {
          label: '收款方',
          prop: 'payeeName',
          minWidth: 150,
          formatter: this.$utils.isEffective
        },
        {
          label: '通知还款金额（元）',
          prop: 'payAmount',
          minWidth: 150,
          formatter: this.$utils.tableMoneyFormat
        },
        {
          label: '收款银行',
          prop: 'payeeBankName',
          minWidth: 150,
          formatter: this.$utils.isEffective
        },
        {
          label: '收款账号',
          prop: 'payeeAccount',
          minWidth: 150,
          formatter: this.$utils.isEffective
        },
        {
          label: '约定还款日期',
          prop: 'planPayDate',
          minWidth: 150,
          formatter: this.$utils.tableDateFormat
        },
       /* {
          label: '最晚打款日期',
          prop: 'planPayEndDate',
          minWidth: 150,
          formatter: this.$utils.tableDateFormat
        },*/
        {
          label: '状态',
          prop: 'status',
          minWidth: 120,
          formatter: this.$utils.tableStatusFormat('REPAY_NOTICE_STATUS')
        },
        {
          label: '还款通知备注',
          prop: 'remark',
          minWidth: 150,
          formatter: this.$utils.isEffective
        }
      ]
    }
  },
  methods: {
    /** @public */
    async getList () {
      try {
        this.loading.list = true
        const res = await this.$api.collection.notice.pageList(this.searchForm)
        this.tableList = res.data.data || {}
      } finally {
        this.loading.list = false
      }
    },
    confirmRepay (row) {
      this.$confirm('您已经完成还款。请确认是否已经向银行成功付款，且金额已正确入账。点击“确认”，我们将通知资金方完成您的还款操作。', '确认已完成还款').then(() => {
        this.$api.collection.notice.confirmRepay({ pid: row.pid }).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    }
  }
}
</script>

<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner
        :search-form="searchForm"
        @submit-search="search"
        @clear-search="clearSearch"
      >
        <el-col :span="6">
          <fe-form-item label="还款方">
            <el-input
              v-model="searchForm.payerName"
              placeholder="请输入还款方"
            />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="收款方">
            <el-input
              v-model="searchForm.payeeName"
              placeholder="请输入收款方"
            />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in $utils.getEnableDictStatus(
                  'REPAY_NOTICE_STATUS'
                )"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </fe-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner
        v-loading="loading.list"
        :table-data="tableList.records"
        show-set-columns
        :column-option="columnOptionList"
        :page-num="tableList.current"
        :page-size="tableList.size"
        :total="tableList.total"
        stripe
        title="信息明细"
        @change-page-num="changePageNum"
        @change-page-size="changePageSize"
      >
        <template #btn-inner>
          <el-button
            type="primary"
            @click="$router.push({ name: 'repayNoticeAdd' })"
          >
            新增通知
          </el-button>
        </template>

        <template #table-columns-after>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <table-link
                :to="{ name: 'repayNoticeDetail', query: { pid: scope.row.pid } }"
              >
                详情
              </table-link>
<!--              <el-link v-if="scope.row.status === 'NOTICED'" type="primary" @click="confirmRepay(scope.row)">-->
<!--                确认-->
<!--              </el-link>-->
            </template>
          </el-table-column>
        </template>
      </table-inner>
    </div>
  </fe-page-main>
</template>

<style>
</style>
