<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import { basePageListMixin } from '@fero/commons-vue'
import DetailInput from '@/components/base/detail-input.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'

export default {
  components: { FeFormItem, DetailInput, FePageMain },
  mixins: [basePageListMixin],
  data () {
    return {
      pid: this.$route.query.pid,
      baseInfoForm: {},
      categoryList: []
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    getDetail () {
      this.$api.collection.notice
        .info(this.pid)
        .then((result) => {
          if (result.data.data) {
            this.baseInfoForm = result.data.data
            this.handleNoticeType(this.baseInfoForm.payType)
          }
        })
        .finally(() => {})
    },
    handleNoticeType (e) {
      if (e === 'ADVANCE_PAYMENT') {
        this.categoryList = [
          {
            label: '提前打款',
            value: 'advancePayment'
          }
        ]
      } else if (e === 'FINANCING_REPAYMENT') {
        this.categoryList = [
          {
            label: '本金',
            value: 'principal'
          },
          {
            label: '利息',
            value: 'interest'
          },
          {
            label: '违约金',
            value: 'overdueFees'
          },
          {
            label: '服务费用',
            value: 'otherExpenses'
          }
        ]
      } else {
        this.categoryList = [
          {
            label: '服务费用',
            value: 'otherExpenses'
          }
        ]
      }
      this.reducePayAmount()
    },
    reducePayAmount () {
      this.baseInfoForm.payAmount =
        this.baseInfoForm.repayNoticeDetailVoList.reduce((pre, cur) => {
          return pre + cur.payAmount || 0
        }, 0)
    }
  }
}
</script>
<template>
  <fe-page-main show-back-btn>
    <div class="partition-area">
      <el-form
        ref="baseInfoFormRef"
        :model="baseInfoForm"
        scroll-to-error
      >
        <div class="form-area">
          <div class="area-title">
            <p class="title">
              还款通知信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="24">
              <el-col :span="12">
                <fe-form-item label="还款通知单编号">
                  <detail-input
                    :model-value="
                      $utils.isEffectiveCommon(baseInfoForm.repayNoticeCode)
                    "
                  />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="融资申请编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(baseInfoForm.financingCode)" :route-info="{ name: 'loanCreditDetail', query: { pid: baseInfoForm.financingId } }" is-link />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="还款方名称">
                  <detail-input
                    :model-value="
                      $utils.isEffectiveCommon(baseInfoForm.payerName)
                    "
                  />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款方名称">
                  <detail-input
                    :model-value="
                      $utils.isEffectiveCommon(baseInfoForm.payeeName)
                    "
                  />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款银行">
                  <detail-input
                    :model-value="
                      $utils.isEffectiveCommon(baseInfoForm.payeeBankName)
                    "
                  />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款账号" prop="payeeAccount">
                  <detail-input
                    :model-value="
                      $utils.isEffectiveCommon(baseInfoForm.payeeAccount)
                    "
                  />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="约定还款日期">
                  <detail-input :model-value="$utils.dateFormat(baseInfoForm.planPayDate)" />
                </fe-form-item>
              </el-col>
              <!--<el-col :span="12">
                <fe-form-item label="最晚打款日期">
                  <detail-input :model-value="$utils.dateFormat(baseInfoForm.planPayEndDate)" />
                </fe-form-item>
              </el-col>-->
            </el-row>
          </div>

          <div class="area-title">
            <p class="title">
              还款通知类目
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="24">
              <el-col :span="12">
                <fe-form-item label="还款类型">
                  <detail-input :model-value="$utils.statusFormat(baseInfoForm.payType, 'REPAY_NOTICE_TYPE')" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="本次还款金额">
                  <detail-input :model-value="$utils.moneyFormat(baseInfoForm.payAmount)" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <table-inner
            :table-header="false"
            :table-footer="false"
            :table-data="baseInfoForm.repayNoticeDetailVoList"
            class="table-input"
            row-key="pid"
            title="明细"
          >
            <template #table-columns-before>
              <el-table-column
                align="center"
                label="类目"
                prop="categoryCode"
                show-overflow-tooltip
                min-width="260"
                :formatter="$utils.tableStatusFormat('categoryList')"
              />
              <el-table-column
                align="center"
                label="金额（元）"
                prop="payAmount"
                show-overflow-tooltip
                min-width="220"
                :formatter="$utils.tableMoneyFormat"
              />
            </template>
          </table-inner>
          <div class="area-title">
            <p class="title">
              还款通知备注
            </p>
          </div>
          <div class="form-inner">
            <detail-input :model-value="baseInfoForm.remark" />
          </div>
        </div>
      </el-form>
    </div>
  </fe-page-main>
</template>

<style>
</style>
