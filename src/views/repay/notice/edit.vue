<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import { basePageListMixin } from '@fero/commons-vue'
import { find, groupBy } from 'lodash'
import DetailInput from '@/components/base/detail-input.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import { inputNumberAttr } from '@/assets/lib/misc'
import AmountInput from '@/components/amount-input.vue'
export default {
  components: { AmountInput, FeFormItem, DetailInput, FePageMain },
  mixins: [basePageListMixin],
  data () {
    return {
      loading: { submit: false },
      baseInfoForm: { payType: 'OTHER_EXPENSES' },
      financingList: [],
      payeeBankList: [],
      payeeAccountList: [],
      rules: {
        payAmount: this.$rulesToolkit.createRules({ name: '本次还款金额', range: { number: true, min: 0.01 }, required: true }),
        categoryCode: this.$rulesToolkit.createRules({
          name: '类目',
          required: true,
          trigger: 'change'
        }),
        payType: this.$rulesToolkit.createRules({
          name: '还款类型',
          required: true,
          trigger: 'change'
        }),
        financingId: this.$rulesToolkit.createRules({
          name: '关联融资申请',
          required: true,
          trigger: 'change'
        }),
        payerId: this.$rulesToolkit.createRules({
          name: '还款方名称',
          required: true,
          trigger: 'change'
        }),
        payeeId: this.$rulesToolkit.createRules({
          name: '收款方名称',
          required: true,
          trigger: 'change'
        })
      }
    }
  },
  computed: {
    inputNumberAttr () {
      return inputNumberAttr
    }
  },
  created () {
    // this.getFundList()
    this.getFinancingList()
    this.getPlatformInfo()
  },
  methods: {
    getPlatformInfo () {
      this.$api.base.myCompanyInfo().then((result) => {
        const resData = result.data.data ?? {}
        this.baseInfoForm.payeeId = resData.pid
        this.baseInfoForm.payeeName = resData.companyName
        const group = groupBy(resData.bankList, 'openingBank')
        const list = []
        Object.keys(group).forEach(openingBank => {
          list.push({
            openingBank,
            openingBankNo: group[openingBank][0].openingBankNo,
            bankAccountList: group[openingBank]
          })
        })
        this.payeeBankList = list
        const defaultBank = find(resData.bankList, { isDefault: 1 })
        if (defaultBank) {
          this.baseInfoForm.openingBankNo = defaultBank.openingBankNo
          this.baseInfoForm.payeeAccountId = defaultBank.pid
          this.baseInfoForm.receiptBankId = defaultBank.pid
          this.getPayeeBank(defaultBank.openingBankNo)
          this.changePayeeAccount(defaultBank.pid)
        }
      })
    },
    // 融资列表
    getFinancingList () {
      this.$api.collection.notice
        .creditList()
        .then((result) => {
          if (result.data.data) {
            this.financingList = result.data.data
          }
        })
        .finally(() => {})
    },
    changeFinancing (e) {
      // financingCode 需要获取
      const checkedFinancing = this.financingList.find((v) => v.pid === e)
      this.baseInfoForm.payerId = checkedFinancing.useCompanyId
      // this.baseInfoForm.payeeId = checkedFinancing.financialCompanyId
      this.baseInfoForm.financingCode = checkedFinancing.useCode

      this.baseInfoForm.payerName = checkedFinancing.useCompanyName
      this.baseInfoForm.payAmount = checkedFinancing.unpaidServiceFee
      // this.baseInfoForm.payeeName = checkedFinancing.financialCompanyName
      // this.changePayee(this.baseInfoForm.payeeId)
    },
    getPayeeBank (e) {
      const checkedPayee = this.payeeBankList.find((v) => v.openingBankNo === e)
      this.payeeAccountList = checkedPayee.bankAccountList
    },

    changePayeeAccount (e) {
      const checkedPayee = this.payeeAccountList.find((v) => v.pid === e)
      this.baseInfoForm.payeeAccount = checkedPayee.bankNo
      this.baseInfoForm.payeeBankName = checkedPayee.openingBank
    },
    submitSave (type) {
      this.$refs.baseInfoFormRef
        .validate()
        .then(() => {
          // const validateTab = this.baseInfoForm.repayNoticeDetailVoList.some(
          //   (item) => item.payAmount <= 0
          // )
          /* if (validateTab) {
            this.$message.error('明细金额需为0以上,请重新填写')
          } else {
          } */
          this.save()
          // type === 'save' ? this.save() : this.update()
        })
        .finally(() => {
        })
    },
    save () {
      const urls = 'create'
      // this.pid ? (urls = 'create') : (urls = 'update')
      this.loading.submit = true
      this.$api.collection.notice[urls](this.baseInfoForm)
        .then((result) => {
          if (result.data.code === 200) {
            this.$router.back()
          }
        }).finally(() => {
          this.loading.submit = false
        })
    }
    // update () {
    //   this.$api.creditManagement.credit
    //     .submit(this.baseInfoForm)
    //     .then((result) => {
    //       if (result.data.code == 200) {
    //         this.$router.back()
    //       }
    //     })
    //     .catch(() => {})
    // }
  }
}
</script>
<template>
  <fe-page-main show-back-btn>
    <template #btn-inner>
      <tab-btn
        :show-save-btn="false"
        :loading="loading.submit"
        :is-approval="false"
        @confirm-submit="submitSave()"
      />
    </template>
    <div class="partition-area">
      <el-form
        ref="baseInfoFormRef"
        :model="baseInfoForm"
        :rules="rules"
        scroll-to-error
      >
        <div class="form-area">
          <div class="area-title">
            <p class="title">
              还款通知信息
            </p>
          </div>

          <div class="form-inner">
            <el-row :gutter="24">
              <el-col :span="12">
                <fe-form-item label="还款通知单编号">
                  <detail-input
                    :model-value="
                      $utils.isEffectiveCommon(baseInfoForm.repayNoticeCode)
                    "
                  />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="融资申请编号" prop="financingId" is-link :can-jump="!!baseInfoForm.financingId" :route-info="{ name: 'loanCreditDetail', query: { pid: baseInfoForm.financingId } }">
                  <el-select
                    v-model="baseInfoForm.financingId"
                    clearable
                    filterable
                    placeholder="请选择融资申请编号"
                    @change="changeFinancing"
                  >
                    <el-option
                      v-for="item in financingList"
                      :key="item.pid"
                      :label="`${item.useCode}（${item.financialCompanyName}，${$utils.moneyFormat(item.approvedAmount)}元，${$utils.dateFormat(item.planUseDate)}）`"
                      :value="item.pid"
                    />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="还款方名称" prop="">
                  <detail-input :model-value=" $utils.isEffectiveCommon(baseInfoForm.payerName)" />
                  <!--<el-select
                    v-model="baseInfoForm.payerId"
                    clearable
                    filterable
                    placeholder="请选择还款方名称"
                    @change="changePayer"
                  >
                    <el-option
                      v-for="item in customList"
                      :key="item.pid"
                      :label="item.companyName"
                      :value="item.pid"
                    />
                  </el-select>-->
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款方名称" prop="">
                  <detail-input :model-value=" $utils.isEffectiveCommon(baseInfoForm.payeeName)" />
                  <!--<el-select
                    v-model="baseInfoForm.payeeId"
                    clearable
                    filterable
                    placeholder="请选择收款方名称"
                    @change="changePayee"
                  >
                    <el-option
                      v-for="item in fundList"
                      :key="item.pid"
                      :label="item.companyName"
                      :value="item.pid"
                    />
                  </el-select>-->
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款银行" prop="openingBankNo">
                  <el-select
                    v-model="baseInfoForm.openingBankNo"
                    clearable
                    filterable
                    placeholder="请选择收款银行"
                    @change="getPayeeBank"
                  >
                    <el-option
                      v-for="item in payeeBankList"
                      :key="item.openingBankNo"
                      :label="item.openingBank"
                      :value="item.openingBankNo"
                    />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款账号" prop="payeeAccountId">
                  <el-select
                    v-model="baseInfoForm.payeeAccountId"
                    clearable
                    filterable
                    placeholder="请选择收款账号"
                    @change="changePayeeAccount"
                  >
                    <el-option
                      v-for="item in payeeAccountList"
                      :key="item.pid"
                      :label="item.bankNo"
                      :value="item.pid"
                    />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="约定还款日期">
                  <el-date-picker
                    v-model="baseInfoForm.planPayDate"
                    type="date"
                    placeholder="请选择约定还款日期"
                    format="YYYY-MM-DD"
                    value-format="x"
                  />
                </fe-form-item>
              </el-col>
              <!--<el-col :span="12">
                <fe-form-item label="最晚打款日期">
                  <el-date-picker
                    v-model="baseInfoForm.planPayEndDate"
                    type="date"
                    placeholder="请选择最晚打款日期"
                    format="YYYY-MM-DD"
                    value-format="x"
                  />
                </fe-form-item>
              </el-col>-->
            </el-row>
          </div>

          <div class="area-title">
            <p class="title">
              还款通知类目
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="24">
              <el-col :span="12">
                <fe-form-item label="还款类型" prop="payType">
                  <detail-input :model-value="$utils.statusFormat(baseInfoForm.payType, 'REPAY_NOTICE_TYPE')" />
                  <!--<el-select
                    v-model="baseInfoForm.payType"
                    placeholder="请选择还款类型"
                    @change="changeNoticeType"
                  >
                    <el-option
                      v-for="item in $utils.getEnableDictStatus(
                        'REPAY_NOTICE_TYPE'
                      )"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>-->
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="本次还款金额" prop="payAmount">
                  <amount-input v-model="baseInfoForm.payAmount" placeholder="请输入本次还款金额" />
                  <!--                  <el-input-number v-model="baseInfoForm.payAmount" v-bind="inputNumberAttr" placeholder="请输入本次还款金额" />-->
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <!--<div class="table-title">
            <table-inner
              :key="goodsListLength"
              :table-footer="false"
              :table-data="baseInfoForm.repayNoticeDetailVoList"
              class="table-input"
              row-key="pid"
              title="还款通知类目"
            >
              <template #btn-inner>
                <el-button type="primary" @click="tableClick">
                  新增
                </el-button>
              </template>
              <template #table-columns-before>
                <el-table-column
                  align="center"
                  label="类目"
                  prop="categoryCode"
                  show-overflow-tooltip
                  min-width="260"
                >
                  <template #default="scope">
                    <el-form-item
                      label-width="0"
                      :prop="`repayNoticeDetailVoList[${scope.$index}].categoryCode`"
                      :rules="rules.categoryCode"
                    >
                      <el-select
                        v-model="scope.row.categoryCode"
                        placeholder="请选择类目"
                      >
                        <el-option
                          v-for="item in categoryList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  label="金额（元）"
                  prop="payAmount"
                  show-overflow-tooltip
                  min-width="220"
                  label-class-name="required-th"
                >
                  <template #default="scope">
                    <el-form-item
                      label-width="0"
                      :prop="`repayNoticeDetailVoList[${scope.$index}].payAmount`"
                      :rules="rules.payAmount"
                    >
                      <el-input-number
                        v-model="scope.row.payAmount"
                        v-bind="$constants.baseInputNumberProp"
                        placeholder="请输入金额（元，含税）"
                        @blur="reducePayAmount"
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
              </template>
              <template #table-columns-after>
                <el-table-column fixed="right" label="操作" width="90px">
                  <template #default="scope">
                    <el-link
                      type="danger"
                      @click="tableClick('del', scope.$index)"
                    >
                      删除
                    </el-link>
                  </template>
                </el-table-column>
              </template>
            </table-inner>
          </div>-->
          <div class="area-title">
            <p class="title">
              还款通知备注
            </p>
          </div>
          <div class="form-inner">
            <el-form-item>
              <el-input
                v-model="baseInfoForm.remark"
                :maxlength="200"
                :rows="3"
                type="textarea"
                placeholder="请输入还款通知备注"
                show-word-limit
              />
            </el-form-item>
          </div>
          <!--          <attachment-list
            v-model="baseInfoForm"
            :business-id="baseInfoForm.pid"
            business-type="REPAY_NOTICE"
          />-->
        </div>
      </el-form>
    </div>
  </fe-page-main>
</template>

<style>
</style>
