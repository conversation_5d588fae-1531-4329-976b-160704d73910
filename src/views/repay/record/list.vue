<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import FeSearchInner from '@/components/base/fe-search-inner.vue'
import { basePageListMixin } from '@fero/commons-vue'
import { utils } from '@/assets/lib/utils'
import ScopePropFormatter from '@/components/scope-prop-formatter.vue'

export default {
  components: { ScopePropFormatter, FeSearchInner, FePageMain },
  mixins: [basePageListMixin],
  data () {
    return {
      columnOptionList: [
        { label: '收款记录编号', prop: 'recordCode', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '付款方', prop: 'payerName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '收款方', prop: 'payeeName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '收款类型', prop: 'claimType', minWidth: 120, formatter: this.$utils.tableStatusFormat('CLAIM_TYPE') },
        { label: '收款金额(元)', prop: 'totalAmount', minWidth: 150, formatter: this.$utils.tableMoneyFormat },
        { label: '收款日期', prop: 'actualRepaymentDate', minWidth: 150, formatter: this.$utils.tableDateFormat },
        { label: '融资编号', prop: 'useCode', minWidth: 150, formatter: this.$utils.isEffective, slotName: 'useCode' },
        { label: '还款编号 / 服务费编号', prop: 'repaymentCode', minWidth: 150, formatter: this.$utils.isEffective, slotName: 'repaymentCode' }
      ]
    }
  },
  computed: {
    searchEffectiveDate: utils.computedDate('actualRepaymentDateMin', 'actualRepaymentDateMax'),
    dynamicUseRoute () {
      return (id) => {
        const routeData1 = this.$router.resolve({
          name: 'loanCreditDetail',
          query: { pid: id }
        })
        return routeData1.href
      }
    },
    dynamicRepayRoute () {
      return (id) => {
        const routeData1 = this.$router.resolve({
          name: 'releaseApplyDetail',
          query: { pid: id }
        })
        return routeData1.href
      }
    }
  },
  methods: {
    /** @public */
    getList () {
      this.loading.list = true
      this.$api.collection.record.pageList(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    }
  }
}
</script>

<template>
  <fe-page-main>
    <div class="partition-area">
      <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
        <el-col :span="6">
          <fe-form-item label="付款方">
            <el-input v-model="searchForm.payerName" placeholder="请输入付款方企业" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="收款方">
            <el-input v-model="searchForm.payeeName" placeholder="请输入收款方名称" />
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="收款类型">
            <el-select v-model="searchForm.claimType" placeholder="请选择收款类型">
              <el-option v-for="item in $utils.getEnableDictStatus('CLAIM_TYPE')" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </fe-form-item>
        </el-col>
        <el-col :span="6">
          <fe-form-item label="收款日期">
            <el-date-picker
              v-model="searchEffectiveDate"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :shortcuts="shortcuts"
              format="YYYY-MM-DD"
              value-format="x"
              placeholder="请选择收款日期"
            />
          </fe-form-item>
        </el-col>
      </fe-search-inner>
    </div>
    <div class="partition-table">
      <table-inner v-loading="loading.list" :table-data="tableList.records" show-set-columns :column-option="columnOptionList" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe title="信息明细" @change-page-num="changePageNum" @change-page-size="changePageSize">
        <template #useCode="scope">
          <el-link type="primary" :href="dynamicUseRoute(scope.row.useId)" target="_blank">
            {{ scope.row.useCode }}
          </el-link>
        </template>
        <template #repaymentCode="scope">
          <el-link v-if="scope.row.repaymentId" type="primary" :href="dynamicRepayRoute(scope.row.repaymentId)" target="_blank">
            {{ scope.row.repaymentCode }}
          </el-link>
          <scope-prop-formatter v-else :scope="scope" />
        </template>
        <template #table-columns-after>
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="scope">
              <table-link type="primary" :to="{ name: 'repayRecordDetail', query: { pid: scope.row.pid } }">
                详情
              </table-link>
            </template>
          </el-table-column>
        </template>
      </table-inner>
    </div>
  </fe-page-main>
</template>

<style>
</style>
