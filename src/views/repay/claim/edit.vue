<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import { datePickAttr } from '@/assets/lib/misc'
import DetailInput from '@/components/base/detail-input.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import PickNotice from '@/views/repay/claim/components/pick-notice.vue'
import TableFormErrorTooltip from '@/components/table-form-error-tooltip.vue'
import { cloneDeep, get } from 'lodash'
import BigNumber from 'bignumber.js'
import PickApply from '@/views/repay/claim/components/pick-apply.vue'
import { ElMessageBox } from 'element-plus'
import AmountInput from '@/components/amount-input.vue'

export default {
  name: 'Edit',
  components: { AmountInput, PickApply, TableFormErrorTooltip, PickNotice, FeFormItem, DetailInput, FePageMain },
  data () {
    return {
      type: this.$route.query.type,
      infoForm: {
        claimType: 'FINANCING_REPAYMENT',
        relList: []
      },
      noticeInfo: {},
      rules: {
        claimType: this.$rulesToolkit.createRules({ required: true, name: '认领类型', trigger: 'change' }),
        settlementDate: this.$rulesToolkit.createRules({ required: true, name: '结算日期', trigger: 'change' }),
        totalAmount: this.$rulesToolkit.createRules({ required: true, name: '本次认领总金额' }),
        principalAmount: this.$rulesToolkit.createRules({ required: true, name: '本次认领本金' }),
        interestAmount: this.$rulesToolkit.createRules({ required: true, name: '本次认领利息' }),
        lateFeesAmount: this.$rulesToolkit.createRules({ required: true, name: '本次认领违约金' }),
        otherFeesAmount: this.$rulesToolkit.createRules({ required: true, name: '本次认领其他费用' }),
        actualRepaymentDate: this.$rulesToolkit.createRules({ required: true, name: '收款日期', trigger: 'change' }),
        repaymentCode: this.$rulesToolkit.createRules({ required: true, name: '还款申请编号', trigger: 'change' }),
        repayNoticeCode: this.$rulesToolkit.createRules({ required: true, name: '还款通知单编号', trigger: 'change' })
      },
      loading: {
        confirm: false
      }
    }
  },
  computed: {
    datePickAttr () {
      return datePickAttr
    }
  },
  created () {
    this.init()
  },
  methods: {
    claimTypeChange () {
      this.infoForm.repayNoticeId = null
      this.infoForm.repayNoticeCode = null
      this.infoForm.useId = null
      this.infoForm.useCode = null
      this.infoForm.payerId = null
      this.infoForm.payerCode = null
      this.infoForm.payerName = null
      this.infoForm.payeeId = null
      this.infoForm.payeeCode = null
      this.infoForm.payeeName = null
      this.infoForm.repayApplyId = null
      this.infoForm.repayApplyCode = null
      this.noticeInfo = {}
    },
    init () {
      this.$api.collection.claim.noticeListByIdList({ pidList: this.$route.query.pidList }).then(res => {
        const resData = res.data.data ?? []
        const relList = []
        resData.forEach(item => {
          item.transactionId = item.pid
          item.amount = item.pendingClaimAmount
          delete item.pid
          relList.push(item)
        })
        this.infoForm.relList = relList
        this.infoForm.actualRepaymentDate = get(relList, '[0].transactionDate')
        this.calculateTotal()
      })
    },
    pickNotice () {
      this.$refs.pickNotice.pick().then(resData => {
        if (resData) {
          this.noticeInfo = resData
          this.infoForm.serviceFeeId = resData.pid
          this.infoForm.serviceFeeCode = resData.serviceFeeCode
          this.infoForm.totalServiceFee = resData.totalServiceFee
          this.infoForm.useId = resData.useId
          this.infoForm.useCode = resData.useCode
          this.infoForm.payerId = resData.payerId
          this.infoForm.payerCode = resData.payerCode
          this.infoForm.payerSocialCreditCode = resData.payerSocialCreditCode
          this.infoForm.payerName = resData.payerName
          this.infoForm.payeeId = resData.payeeId
          this.infoForm.payeeCode = resData.payeeCode
          this.infoForm.payeeSocialCreditCode = resData.payeeSocialCreditCode
          this.infoForm.payeeName = resData.payeeName

          this.infoForm.principalAmount = null
          this.infoForm.interestAmount = null
          this.infoForm.lateFeesAmount = null
          this.infoForm.otherFeesAmount = null
          this.infoForm.earlyRepaymentAmount = null
        }
      })
    },
    pickRepay () {
      this.$refs.pickApply.pick().then(applyInfo => {
        if (applyInfo) {
          this.infoForm.repaymentId = applyInfo.pid
          this.infoForm.repaymentCode = applyInfo.repaymentApplyCode
          this.infoForm.practicalReleaseAmount = applyInfo.practicalReleaseAmount
          this.infoForm.payerId = applyInfo.customerCompanyId
          this.infoForm.payerCode = applyInfo.customerCompanyCode
          this.infoForm.payerSocialCreditCode = applyInfo.customerSocialCreditCode
          this.infoForm.payerName = applyInfo.customerCompanyName
          this.infoForm.payeeId = applyInfo.financialCompanyId
          this.infoForm.payeeCode = applyInfo.financialCompanyCode
          this.infoForm.payeeSocialCreditCode = applyInfo.financialSocialCreditCode
          this.infoForm.payeeName = applyInfo.financialCompanyName
          this.infoForm.useId = applyInfo.useId
          this.infoForm.useCode = applyInfo.useCode
        }
      })
    },
    confirm () {
      this.$refs.infoForm.validate().then(async () => {
        if (this.infoForm.claimType === 'OTHER_EXPENSES' && !this.infoForm.serviceFeeId) {
          return this.$message.error('请选择服务费明细')
        }
        if (this.infoForm.claimType === 'FINANCING_REPAYMENT' && !this.infoForm.repaymentId) {
          return this.$message.error('请选择还款申请')
        }
        let relTableTotal = new BigNumber(0)
        this.infoForm.relList.forEach(item => {
          const amount = new BigNumber(item.amount)
          relTableTotal = relTableTotal.plus(amount)
        })
        if (!relTableTotal.isEqualTo(this.infoForm.totalAmount)) {
          return this.$message.error('本次认领总金额不等于交易流水本次认领金额之和')
        }

        // if (this.infoForm.payAmount) {
        //   if (this.infoForm.totalAmount < this.infoForm.payAmount) {
        //     await ElMessageBox.confirm('您选择的认领金额小于服务费明细的应付金额。每个服务费还款通知单只能认领一次，认领后无法再次操作，是否继续认领？', '是否继续认领？')
        //   }
        // }

        if (this.infoForm.practicalReleaseAmount) {
          if (this.infoForm.totalAmount > this.infoForm.practicalReleaseAmount) {
            await ElMessageBox.confirm('您选择的认领金额大于还款申请单的约定还款金额，是否继续认领？', '是否继续认领？')
          }
        }

        this.loading.confirm = true
        const formData = cloneDeep(this.infoForm)
        if (this.infoForm.claimType === 'OTHER_EXPENSES') {
          formData.otherFeesAmount = formData.totalAmount
        } else if (this.infoForm.claimType === 'ADVANCE_PAYMENT') {
          formData.earlyRepaymentAmount = formData.totalAmount
        }
        // 根据数据来源不同调用不同的接口
        const apiCall = this.type === 'manualEntry'
          ? this.$api.collection.claim.saveByHand(formData)
          : this.$api.collection.claim.save(formData)
        apiCall.then(result => {
          this.$utils.resultBaseMessage(result)
          this.$router.back()
        }).finally(() => {
          this.loading.confirm = false
        })
      })
    },
    calculateTotal () {
      let totalAmount = new BigNumber(0)
      this.infoForm.relList.forEach(item => {
        totalAmount = totalAmount.plus(item.amount)
      })
      this.infoForm.totalAmount = totalAmount.toNumber()
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <template #btn-inner>
      <el-button :loading="loading.confirm" type="primary" @click="confirm">
        确认认领
      </el-button>
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form ref="infoForm" :model="infoForm" :rules="rules">
          <div class="area-title">
            <p class="title">
              交易流水
            </p>
          </div>
          <div class="form-inner table-form-input">
            <el-table :data="infoForm.relList">
              <el-table-column prop="receiptAmount" label="收款金额(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />
              <el-table-column prop="pendingClaimAmount" label="待认领金额(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />
              <el-table-column prop="amount" label="本次认领金额(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip>
                <template #default="scope">
                  <el-form-item label="" :label-width="0" :prop="`relList[${scope.$index}].amount`" :rules="[{ required: true, message: '请填写本次认领金额' }, { type: 'number', min: 0.01, max: scope.row.pendingClaimAmount ?? 0, message: `本次认领金额必须大于0.01小于${scope.row.pendingClaimAmount ?? 0}` }]">
                    <amount-input v-model="scope.row.amount" placeholder="请输入本次认领金额（元）" @blur="calculateTotal" />
                    <!--                    <el-input-number v-model="scope.row.amount" v-bind="inputNumberAttr" @blur="calculateTotal" />-->
                    <template #error="errorScope">
                      <table-form-error-tooltip :err-scope="errorScope" />
                    </template>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="transactionNumber" label="交易流水号" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="transactionDate" label="交易日期" :formatter="$utils.tableDateFormat" show-overflow-tooltip />
              <el-table-column prop="payeeBankAccount" label="收款账号" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="otherPartyName" label="对方户名" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="otherPartyAccount" label="对方账号" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="remark" label="交易备注" :formatter="$utils.isEffective" show-overflow-tooltip />
            </el-table>
          </div>
          <div class="area-title">
            <p class="title">
              结算信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="认领类型" prop="claimType">
                  <el-select v-model="infoForm.claimType" placeholder="请选择认领类型" @change="claimTypeChange">
                    <el-option v-for="item in $utils.getEnableDictStatus('CLAIM_TYPE')" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col v-if="infoForm.claimType === 'FINANCING_REPAYMENT'" :span="12">
                <fe-form-item label="还款申请编号" prop="repaymentCode">
                  <detail-input custom>
                    <template #custom>
                      <div class="flex justify-between w-full">
                        <div>
                          <span>{{ $utils.isEffectiveCommon(infoForm.repaymentCode) }}</span>
                          <span v-if="infoForm.practicalReleaseAmount !== undefined">（{{ $utils.moneyFormat(infoForm.practicalReleaseAmount) }})</span>
                        </div>
                        <el-link class="mr-3" type="primary" @click="pickRepay">
                          选择
                        </el-link>
                      </div>
                    </template>
                  </detail-input>
                </fe-form-item>
              </el-col>
              <el-col v-else-if="infoForm.claimType === 'OTHER_EXPENSES'" :span="12">
                <fe-form-item label="服务费编号" prop="serviceFeeCode">
                  <detail-input custom>
                    <template #custom>
                      <div class="flex justify-between w-full">
                        <div>
                          <span>{{ $utils.isEffectiveCommon(infoForm.serviceFeeCode) }}</span>
                          <span v-if="infoForm.totalServiceFee !== undefined">（{{ $utils.moneyFormat(infoForm.totalServiceFee) }}）</span>
                        </div>
                        <el-link class="mr-3" type="primary" @click="pickNotice">
                          选择
                        </el-link>
                      </div>
                    </template>
                  </detail-input>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="payerName" label="付款方名称">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.payerName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="payerName" label="付款方社会信用代码">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.payerSocialCreditCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="payeeName" label="收款方名称">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.payeeName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="payeeName" label="收款方社会信用代码">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.payeeSocialCreditCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="本次认领金额（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.totalServiceFee)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <!--                <fe-form-item label="实际还款日期（元）" help-content="在融资还款时，该日期用于计算本金的利息及相关费用，请确保填写准确。">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.totalAmount)" />
                </fe-form-item>-->
                <fe-form-item prop="actualRepaymentDate" label="收款日期" help-content="在融资还款时，业务人员将参考该字段，填写用于计算本金、利息及相关费用的实际还款日期，请确保填写准确。">
                  <el-date-picker
                    v-model="infoForm.actualRepaymentDate"
                    type="date"
                    placeholder="请选择收款日期"
                    format="YYYY-MM-DD"
                    value-format="x"
                    v-bind="datePickAttr"
                  />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              备注信息
            </p>
          </div>
          <div class="form-inner">
            <el-input v-model="infoForm.remark" type="textarea" :rows="4" :maxlength="2000" show-word-limit placeholder="请填写备注信息" />
          </div>
        </el-form>
      </div>
    </div>
    <pick-notice ref="pickNotice" />
    <pick-apply ref="pickApply" />
  </fe-page-main>
</template>

<style lang="scss">

</style>
