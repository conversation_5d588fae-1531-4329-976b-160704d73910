<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import DetailInput from '@/components/base/detail-input.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
import { defaultsDeep } from 'lodash'

export default {
  components: { FeFormItem, DetailInput, FePageMain },
  data () {
    return {
      infoForm: {
        relList: []
      },
      loading: {
        detail: false
      }
    }
  },
  created () {
    this.getDetail()
  },
  methods: {
    getDetail () {
      this.loading.detail = true
      this.$api.collection.claim.detail(this.$route.query.pid).then(res => {
        this.infoForm = defaultsDeep(res.data.data, { relList: [] })
      }).finally(() => {
        this.loading.detail = false
      })
    }
  }
}
</script>

<template>
  <fe-page-main show-back-btn>
    <div class="partition-area">
      <div class="form-area">
        <el-form ref="infoForm" :model="infoForm">
          <div class="area-title">
            <p class="title">
              交易流水
            </p>
          </div>
          <div class="form-inner table-form-input">
            <el-table :data="infoForm.relList">
              <el-table-column prop="receiptAmount" label="收款金额(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />
              <el-table-column prop="amount" label="本次认领金额(元)" :formatter="$utils.tableMoneyFormat" show-overflow-tooltip />
              <el-table-column prop="transactionNumber" label="交易流水号" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="transactionDate" label="交易日期" :formatter="$utils.tableDateTimeFormat" show-overflow-tooltip />
              <el-table-column prop="payeeBankAccount" label="收款账号" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="otherPartyName" label="对方户名" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="otherPartyAccount" label="对方账号" :formatter="$utils.isEffective" show-overflow-tooltip />
              <el-table-column prop="remark" label="交易备注" :formatter="$utils.isEffective" show-overflow-tooltip />
            </el-table>
          </div>
          <div class="area-title">
            <p class="title">
              结算信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="20">
              <el-col :span="12">
                <fe-form-item label="认领类型" prop="claimType">
                  <detail-input :model-value="$utils.statusFormat(infoForm.claimType, 'CLAIM_TYPE')" />
                </fe-form-item>
              </el-col>
              <el-col v-if="infoForm.claimType === 'FINANCING_REPAYMENT'" :span="12">
                <fe-form-item label="还款申请">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.repaymentCode)" />
                </fe-form-item>
              </el-col>
              <el-col v-else-if="infoForm.claimType === 'OTHER_EXPENSES'" :span="12">
                <fe-form-item label="服务费编号">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.serviceFeeCode)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="payerName" label="还款方名称">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.payerName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="payeeName" label="收款方名称">
                  <detail-input :model-value="$utils.isEffectiveCommon(infoForm.payeeName)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="本次认领金额（元）">
                  <detail-input :model-value="$utils.moneyFormat(infoForm.totalAmount)" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item prop="actualRepaymentDate" label="收款日期">
                  <detail-input :model-value="$utils.dateFormat(infoForm.actualRepaymentDate)" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
          <div class="area-title">
            <p class="title">
              备注信息
            </p>
          </div>
          <div class="form-inner">
            {{ $utils.isEffectiveCommon(infoForm.remark) }}
          </div>
        </el-form>
      </div>
    </div>
  </fe-page-main>
</template>

<style lang="scss">

</style>
