<script>
import { basePageListMixin } from '@fero/commons-vue'
import { utils } from '@/assets/lib/utils'
import FeFormItem from '@/components/base/fe-form-item.vue'
import FeSearchInner from '@/components/base/fe-search-inner.vue'
import { cloneDeep } from 'lodash'

export default {
  name: 'cancel-claim-list',
  components: { FeSearchInner, FeFormItem },
  mixins: [basePageListMixin],
  data () {
    return {
      changeRouter: false,
      createdInit: false,
      visible: false,
      noticeColumnOptionList: [
        { label: '交易流水号', prop: 'transactionNumber', minWidth: 100, formatter: this.$utils.isEffective },
        { label: '交易日期', prop: 'transactionDate', minWidth: 120, formatter: this.$utils.tableDateFormat },
        { label: '收款账号', prop: 'payeeBankAccount', minWidth: 120, formatter: this.$utils.isEffective },
        { label: '对方户名', prop: 'otherPartyName', minWidth: 120, formatter: this.$utils.isEffective },
        { label: '对方账号', prop: 'otherPartyAccount', minWidth: 140, formatter: this.$utils.isEffective },
        { label: '收款金额（元）', prop: 'receiptAmount', minWidth: 120, formatter: this.$utils.tableMoneyFormat },
        { label: '交易备注', prop: 'remark', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '数据来源', prop: 'dataSource', minWidth: 100, formatter: this.$utils.tableStatusFormat('RECEIPT_TRANSACTIONS_DATA_SOURCE') },
        { label: '操作时间', prop: 'updateTime', minWidth: 150, formatter: this.$utils.tableDateTimeFormat },
        { label: '操作人员', prop: 'updateBy', minWidth: 150, formatter: this.$utils.isEffective }
      ]
    }
  },
  computed: {
    searchPaymentDate: utils.computedDate('transactionDateMin', 'transactionDateMax')
  },
  methods: {
    init () {
      this.clearSearch()
      this.visible = true
    },
    /** @public */
    getList () {
      const api = this.$api.collection.claim.cancelList
      this.loading.list = true
      const query = cloneDeep(this.searchForm)
      api(query).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    }
  }
}
</script>

<template>
  <dialog-inner v-model="visible" title="拒绝历史" :show-submit="false" cancel-title="关闭">
    <fe-search-inner :search-form="searchForm" class="mb-[15px]" @submit-search="search" @clear-search="clearSearch">
      <el-col :span="6">
        <fe-form-item label="对方户名">
          <el-input v-model="searchForm.otherPartyName" placeholder="请输入对方户名" />
        </fe-form-item>
      </el-col>
      <el-col :span="6">
        <fe-form-item label="对方账号">
          <el-input v-model="searchForm.otherPartyAccount" placeholder="请输入对方账号" />
        </fe-form-item>
      </el-col>
      <el-col :span="12">
        <fe-form-item label="交易日期">
          <el-date-picker v-model="searchPaymentDate" type="daterange" format="YYYY-MM-DD" value-format="x" />
        </fe-form-item>
      </el-col>
    </fe-search-inner>
    <table-inner v-loading="loading.list" :table-data="tableList.records" class="bg-white" show-set-columns :column-option="noticeColumnOptionList" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" row-key="pid" stripe title="信息明细" @change-page-num="changePageNum" @change-page-size="changePageSize" />
  </dialog-inner>
</template>

<style lang="scss">

</style>
