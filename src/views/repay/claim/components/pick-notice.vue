<script>
import { basePageListMixin } from '@fero/commons-vue'
import FeSearchInner from '@/components/base/fe-search-inner.vue'
let pickResolve = null
let pickReject = null
export default {
  name: 'PickNotice',
  components: { FeSearchInner },
  mixins: [basePageListMixin],
  data () {
    return {
      /** @public */
      changeRouter: false,
      /** @public */
      createdInit: false,
      lastSelected: null,
      columnOptionList: [
        { label: '融资编号', prop: 'useCode', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '服务费编号', prop: 'serviceFeeCode', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '服务费总额（元）', prop: 'totalServiceFee', minWidth: 120, formatter: this.$utils.tableMoneyFormat },
        { label: '平台方', prop: 'platformCompanyName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '运营方', prop: 'operateCompanyName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '资金方', prop: 'financialCompanyName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '核心企业', prop: 'coreCompanyName', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '状态', prop: 'isVerified', minWidth: 150, formatter: this.$utils.tableTrueFalseFormant({ true: '已核销', false: '未核销' }) },
        { label: '生成时间', prop: 'createTime', minWidth: 150, formatter: this.$utils.tableDateFormat }
      ],
      dialog: {
        notice: false
      }
    }
  },
  methods: {
    /** @public */
    pick () {
      return new Promise((resolve, reject) => {
        pickResolve = resolve
        pickReject = reject
        this.dialog.notice = true
        this.clearSearch()
      })
    },
    /** @public */
    getList () {
      this.loading.list = true
      const query = {
        isVerified: false,
        ...this.searchForm
      }
      this.$api.serviceFee.content.pageList(query).then(res => {
        this.tableList = res.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    handleSelectionChange (selection) {
      if (!selection || selection.length === 0) {
        return false
      }
      const lastSelected = selection[selection.length - 1]
      this.lastSelected = lastSelected
      if (selection.length > 1) {
        this.$refs.tableList.tableEvent('clearSelection')
        this.$refs.tableList.tableEvent('toggleRowSelection', lastSelected, true)
      }
    },
    confirmPick () {
      if (!this.lastSelected) {
        this.$message.error('请选择服务费明细')
        return false
      }
      if (pickResolve) {
        pickResolve(this.lastSelected)
        pickReject = null
      }
      this.dialog.notice = false
    },
    closeDialog () {
      if (pickReject) {
        pickReject(new Error('取消选择'))
      }
      pickReject = null
      pickResolve = null
      this.lastSelected = null
    }
  }
}
</script>

<template>
  <dialog-inner v-model="dialog.notice" title="选择服务费明细" destroy-on-close @close="closeDialog" @submit="confirmPick">
    <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
      <el-col :span="6">
        <fe-form-item label="融资编号">
          <el-input v-model="searchForm.useCode" placeholder="请输入融资编号" />
        </fe-form-item>
      </el-col>
    </fe-search-inner>
    <table-inner ref="tableList" v-loading="loading.list" :table-data="tableList.records" show-set-columns :column-option="columnOptionList" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe title="信息明细" row-key="pid" class="single-select-table" @change-page-num="changePageNum" @change-page-size="changePageSize" @selection-change="handleSelectionChange">
      <template #table-columns-before>
        <el-table-column type="selection" reserve-selection />
      </template>
    </table-inner>
  </dialog-inner>
</template>

<style scoped lang="scss">

</style>
