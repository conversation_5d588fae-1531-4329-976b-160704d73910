<script>
import { basePageListMixin } from '@fero/commons-vue'
import FeSearchInner from '@/components/base/fe-search-inner.vue'
import FeFormItem from '@/components/base/fe-form-item.vue'
let pickResolve = null
let pickReject = null
export default {
  name: 'PickApply',
  components: { FeFormItem, FeSearchInner },
  mixins: [basePageListMixin],
  data () {
    return {
      /** @public */
      changeRouter: false,
      /** @public */
      createdInit: false,
      lastSelected: null,
      columnOptionList: [
        { label: '还款申请编号', prop: 'repaymentApplyCode', minWidth: 160, formatter: this.$utils.isEffective },
        { label: '客户名称', prop: 'customerCompanyName', minWidth: 200, formatter: this.$utils.isEffective },
        { label: '融资申请编号', prop: 'useCode', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '还款日期', prop: 'plannedRepaymentDate', minWidth: 150, formatter: this.$utils.tableDateFormat },
        { label: '还款金额（元）', prop: 'practicalReleaseAmount', minWidth: 150, formatter: this.$utils.tableMoneyFormat },
        { label: '创建日期', prop: 'createTime', minWidth: 150, formatter: this.$utils.tableDateFormat },
        { label: '还款状态', prop: 'status', minWidth: 120, formatter: this.$utils.tableStatusFormat('REPAYMENT_APPLY_STATUS') }
      ],
      dialog: {
        notice: false
      }
    }
  },
  methods: {
    /** @public */
    pick () {
      return new Promise((resolve, reject) => {
        pickResolve = resolve
        pickReject = reject
        this.dialog.notice = true
        this.clearSearch()
      })
    },
    /** @public */
    getList () {
      this.loading.list = true
      const query = {
        status: '3',
        ...this.searchForm
      }
      this.$api.repayment.apply.pageList(query).then(res => {
        this.tableList = res.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    handleSelectionChange (selection) {
      if (!selection || selection.length === 0) {
        return false
      }
      const lastSelected = selection[selection.length - 1]
      this.lastSelected = lastSelected
      if (selection.length > 1) {
        this.$refs.tableList.tableEvent('clearSelection')
        this.$refs.tableList.tableEvent('toggleRowSelection', lastSelected, true)
      }
    },
    confirmPick () {
      if (!this.lastSelected) {
        this.$message.error('请选择还款申请')
        return false
      }
      if (pickResolve) {
        pickResolve(this.lastSelected)
        pickReject = null
      }
      this.dialog.notice = false
    },
    closeDialog () {
      if (pickReject) {
        pickReject(new Error('取消选择'))
      }
      pickReject = null
      pickResolve = null
      this.lastSelected = null
    }
  }
}
</script>

<template>
  <dialog-inner v-model="dialog.notice" title="选择还款申请" destroy-on-close @close="closeDialog" @submit="confirmPick">
    <fe-search-inner :search-form="searchForm" @submit-search="search" @clear-search="clearSearch">
      <el-col :span="6">
        <fe-form-item label="客户名称">
          <el-input v-model="searchForm.customerCompanyName" placeholder="请输入客户名称" />
        </fe-form-item>
      </el-col>
      <el-col :span="6">
        <fe-form-item label="融资申请编号">
          <el-input v-model="searchForm.useCode" placeholder="请输入融资申请编号" />
        </fe-form-item>
      </el-col>
    </fe-search-inner>
    <table-inner ref="tableList" v-loading="loading.list" :table-data="tableList.records" show-set-columns :column-option="columnOptionList" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe title="信息明细" row-key="pid" class="single-select-table" @change-page-num="changePageNum" @change-page-size="changePageSize" @selection-change="handleSelectionChange">
      <template #table-columns-before>
        <el-table-column type="selection" reserve-selection />
      </template>
    </table-inner>
  </dialog-inner>
</template>

<style scoped lang="scss">

</style>
