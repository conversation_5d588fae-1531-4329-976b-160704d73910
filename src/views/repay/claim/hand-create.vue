<template>
  <fe-page-main show-back-btn>
    <template #btn-inner>
      <el-button type="primary" :loading="loading.submit" @click="submit">
        保存并提交
      </el-button>
    </template>
    <div class="partition-area">
      <div class="form-area">
        <el-form ref="baseFormRef" :model="baseForm" :rules="rules">
          <div class="area-title">
            <p class="title">
              认领通知单信息
            </p>
          </div>
          <div class="form-inner">
            <el-row :gutter="80">
              <el-col :span="12">
                <fe-form-item label="交易流水号" prop="transactionNumber">
                  <el-input v-model="baseForm.transactionNumber" placeholder="请输入交易流水号" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="交易日期" prop="transactionDate">
                  <el-date-picker v-model="baseForm.transactionDate" v-bind="datePickAttr" placeholder="请选择交易日期" />
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款账号" prop="payeeBankAccount">
                  <el-select v-model="baseForm.payeeBankAccount" filterable placeholder="请输入收款账号" allow-create>
                    <el-option v-for="item in bankAccountList" :key="item.pid" :label="item.bankNo" :value="item.bankNo" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="对方户名" prop="otherPartyName">
                  <el-select v-model="baseForm.otherPartyName" filterable placeholder="请选择对方户名" allow-create @change="changeOtherPartyName">
                    <el-option v-for="item in bankAccountList" :key="item.pid" :label="item.accountNoName" :value="item.accountNoName" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="对方账号" prop="otherPartyAccount">
                  <el-select v-model="baseForm.otherPartyAccount" filterable placeholder="请选择对方账号" allow-create>
                    <el-option v-for="item in otherBankAccountList" :key="item.pid" :label="item.bankNo" :value="item.bankNo" />
                  </el-select>
                </fe-form-item>
              </el-col>
              <el-col :span="12">
                <fe-form-item label="收款金额（元）" prop="receiptAmount">
                  <amount-input v-model="baseForm.receiptAmount" placeholder="请输入收款金额（元）" />
                  <!--                  <el-input-number v-model="baseForm.receiptAmount" v-bind="inputNumberAttr" placeholder="请输入还款金额" />-->
                </fe-form-item>
              </el-col>
              <!--              <el-col :span="12">-->
              <!--                <fe-form-item label="还款总金额（元）" prop="receiptTotalAmount">-->
              <!--                  <el-input-number v-model="baseForm.receiptTotalAmount" v-bind="inputNumberAttr" placeholder="请输入还款总金额" />-->
              <!--                </fe-form-item>-->
              <!--              </el-col>-->
              <!--              <el-col :span="12">-->
              <!--                <fe-form-item label="已认领金额（元）" prop="claimedAmount">-->
              <!--                  <el-input-number v-model="baseForm.claimedAmount" v-bind="inputNumberAttr" placeholder="请输入已认领金额" />-->
              <!--                </fe-form-item>-->
              <!--              </el-col>-->
              <!--              <el-col :span="12">-->
              <!--                <fe-form-item label="待认领金额（元）" prop="pendingClaimAmount">-->
              <!--                  <el-input-number v-model="baseForm.pendingClaimAmount" v-bind="inputNumberAttr" placeholder="请输入待认领金额" />-->
              <!--                </fe-form-item>-->
              <!--              </el-col>-->
              <el-col :span="12">
                <fe-form-item label="交易备注" prop="remark">
                  <el-input v-model="baseForm.remark" placeholder="请输入交易备注" />
                </fe-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
    </div>
  </fe-page-main>
</template>

<script>
import FeFormItem from '@/components/base/fe-form-item.vue'
import FePageMain from '@/components/base/fe-page-main.vue'
import { cloneDeep } from 'lodash'
import { datePickAttr } from '@/assets/lib/misc'
import AmountInput from '@/components/amount-input.vue'

export default {
  components: { AmountInput, FePageMain, FeFormItem },
  data () {
    return {
      pid: this.$route.query.pid,
      baseForm: {
      },
      bankAccountList: [],
      otherBankAccountList: [],
      rules: {
        // transactionNumber: this.$rulesToolkit.createRules({ required: true, name: '交易流水号' }),
        transactionDate: this.$rulesToolkit.createRules({ required: true, name: '交易日期', trigger: 'change' }),
        payeeBankAccount: this.$rulesToolkit.createRules({ required: true, name: '收款账号', trigger: 'change' }),
        otherPartyName: this.$rulesToolkit.createRules({ required: true, name: '对方户名', trigger: 'change' }),
        otherPartyAccount: this.$rulesToolkit.createRules({ required: true, name: '对方账号', trigger: 'change' }),
        receiptAmount: this.$rulesToolkit.createRules({ required: true, name: '收款金额' })
        // receiptTotalAmount: this.$rulesToolkit.createRules({ required: true, name: '还款总金额' })
      },
      loading: {
        submit: false
      }
    }
  },
  computed: {
    datePickAttr () {
      return datePickAttr
    }
  },
  created () {
    this.getBankAccountList()
    if (this.pid) {
      this.getDetail()
    }
  },
  methods: {
    getDetail () {
      this.loading.detail = true
      this.$api.collection.claim.detailByHand(this.pid).then(result => {
        this.baseForm = result.data.data
      }).finally(() => {
        this.loading.detail = false
      })
    },
    getBankAccountList () {
      this.$api.principal.bankAccount.list().then(result => {
        this.bankAccountList = result.data.data
      })
    },
    changeOtherPartyName () {
      this.baseForm.otherPartyAccount = ''
      this.otherBankAccountList = []
      this.$api.principal.bankAccount.list({ accountNoName: this.baseForm.otherPartyName }).then(result => {
        this.otherBankAccountList = result.data.data
      })
    },
    submit () {
      this.$refs.baseFormRef.validate().then(() => {
        this.loading.submit = true
        const formData = cloneDeep(this.baseForm)
        this.$api.collection.claim.submitByHand(formData).then(result => {
          this.$utils.resultBaseMessageV2(result)
          this.$router.back()
        }).finally(() => {
          this.loading.submit = false
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
