<script>
import FePageMain from '@/components/base/fe-page-main.vue'
import { basePageListMixin } from '@fero/commons-vue'
import FeSearchInner from '@/components/base/fe-search-inner.vue'
import { utils } from '@/assets/lib/utils'
import FeFormItem from '@/components/base/fe-form-item.vue'
import dayjs from 'dayjs'
import CancelClaimList from '@/views/repay/claim/components/cancel-claim-list.vue'

export default {
  components: { CancelClaimList, FeFormItem, FeSearchInner, FePageMain },
  mixins: [basePageListMixin],
  data () {
    return {
      activeName: 'notice',
      selectList: [],
      noticeColumnOptionList: [
        { label: '交易流水号', prop: 'transactionNumber', minWidth: 100, formatter: this.$utils.isEffective },
        { label: '交易日期', prop: 'transactionDate', minWidth: 120, formatter: this.$utils.tableDateFormat },
        { label: '收款账号', prop: 'payeeBankAccount', minWidth: 120, formatter: this.$utils.isEffective },
        { label: '对方户名', prop: 'otherPartyName', minWidth: 120, formatter: this.$utils.isEffective },
        { label: '对方账号', prop: 'otherPartyAccount', minWidth: 140, formatter: this.$utils.isEffective },
        { label: '收款金额（元）', prop: 'receiptAmount', minWidth: 120, formatter: this.$utils.tableMoneyFormat },
        { label: '认领状态', prop: 'claimStatus', minWidth: 100, formatter: this.$utils.tableStatusFormat('RECEIPT_CLAIM_STATUS') },
        // { label: '还款总金额(元)', prop: 'receiptTotalAmount', minWidth: 120, formatter: this.$utils.tableMoneyFormat },
        { label: '已认领金额（元）', prop: 'claimedAmount', minWidth: 120, formatter: this.$utils.tableMoneyFormat },
        { label: '待认领金额（元）', prop: 'pendingClaimAmount', minWidth: 120, formatter: this.$utils.tableMoneyFormat },
        { label: '交易备注', prop: 'remark', minWidth: 150, formatter: this.$utils.isEffective },
        { label: '数据来源', prop: 'dataSource', minWidth: 100, formatter: this.$utils.tableStatusFormat('RECEIPT_TRANSACTIONS_DATA_SOURCE') }
      ],
      recordColumnOptionList: [
        { label: '收款认领编号', prop: 'claimCode', minWidth: 120, formatter: this.$utils.isEffective },
        { label: '收款记录编号', prop: 'recordCode', minWidth: 120, formatter: this.$utils.isEffective },
        { label: '付款方名称', prop: 'payerName', minWidth: 120, formatter: this.$utils.isEffective },
        { label: '收款方名称', prop: 'payeeName', minWidth: 120, formatter: this.$utils.isEffective },
        { label: '认领类型', prop: 'claimType', minWidth: 100, formatter: this.$utils.tableStatusFormat('CLAIM_TYPE') },
        { label: '本次认领金额（元）', prop: 'totalAmount', minWidth: 120, formatter: this.$utils.tableMoneyFormat },
        { label: '收款日期', prop: 'actualRepaymentDate', minWidth: 120, formatter: this.$utils.tableDateFormat },
        { label: '认领时间', prop: 'createTime', minWidth: 120, formatter: this.$utils.tableDateFormat },
        { label: '认领人', prop: 'createBy', minWidth: 100, formatter: this.$utils.isEffective }
      ]
    }
  },
  computed: {
    searchPaymentDate: utils.computedDate('transactionDateMin', 'transactionDateMax')
  },
  methods: {
    /** @public */
    getList () {
      let api = this.$api.collection.claim.noticePageList
      if (this.activeName === 'record') {
        api = this.$api.collection.claim.recordPageList
      }
      this.loading.list = true
      api(this.searchForm).then(result => {
        this.tableList = result.data.data
      }).finally(() => {
        this.loading.list = false
      })
    },
    del () {
      if (this.selectList.length === 0) {
        this.$message.error('请先选择需要拒绝的银行流水')
        return
      }
      if (this.selectList.length > 1) {
        this.$message.error('只能选择一条待拒绝的银行流水')
        return
      }
      const row = this.selectList[0]
      if (row.claimStatus !== 'pendingClaim') {
        this.$message.error('只能拒绝待认领的银行流水')
        return
      }
      this.$confirm('确认拒绝该认领？', '确认拒绝').then(() => {
        this.$api.collection.claim.del(row.pid).then(result => {
          this.$utils.resultBaseMessageV2(result)
          this.getList()
        })
      })
    },
    selectionChange (list) {
      this.selectList = list
    },
    mergeClaim () {
      if (this.selectList.length === 0) {
        this.$message.error('请先选择需要认领的银行流水')
        return
      }
      // 检查数据来源是否一致
      const dataSourceArray = this.selectList.map(item => item.dataSource)
      if (!dataSourceArray.every(source => source === dataSourceArray[0])) {
        return this.$message.error('请选择相同数据来源银行流水合并认领')
      }
      // 获取数据来源类型
      const dataSource = this.selectList[0].dataSource
      // 根据数据来源设置 type 参数
      let type = ''
      if (dataSource === 'manualEntry') {
        type = 'manualEntry'
      } else if (dataSource === 'settlementPlatform') {
        type = 'settlementPlatform'
      }
      // 拼接 pidList 参数
      const pidList = this.selectList.map(item => item.pid).join(',')
      // 跳转到认领页面
      this.$router.push({ name: 'repayClaimAdd', query: { pidList, type } })
    },
    cancel (row) {
      this.$confirm('确认要取消认领？', '确认').then(() => {
        this.$api.collection.claim.cancel(row.pid).then(result => {
          this.$utils.resultBaseMessage(result)
          this.getList()
        })
      })
    },
    canCancel (row) {
      return dayjs().startOf('day').subtract(7, 'day').isSameOrBefore(row.createTime)
    },
    isSelectable (row) {
      return row.claimStatus !== 'fullClaim'
    },
    viewCancelList () {
      this.$refs.cancelClaimList.init()
    }
  }
}
</script>

<template>
  <fe-page-main>
    <div class="tabs-inner partition">
      <el-tabs v-model="activeName" type="card" @tab-change="search">
        <el-tab-pane label="收款银行流水" name="notice">
          <fe-search-inner :search-form="searchForm" class="mb-[15px]" @submit-search="search" @clear-search="clearSearch">
            <!--            <el-col :span="6">
              <fe-form-item label="交易流水号">
                <el-input v-model="searchForm.transactionNumber" placeholder="请输入交易流水号" />
              </fe-form-item>
            </el-col>-->
            <el-col :span="6">
              <fe-form-item label="对方户名">
                <el-input v-model="searchForm.otherPartyName" placeholder="请输入对方户名" />
              </fe-form-item>
            </el-col>
            <el-col :span="6">
              <fe-form-item label="对方账号">
                <el-input v-model="searchForm.otherPartyAccount" placeholder="请输入对方账号" />
              </fe-form-item>
            </el-col>
            <el-col :span="6">
              <fe-form-item label="认领状态">
                <el-select v-model="searchForm.claimStatus" placeholder="请选择认领状态">
                  <el-option v-for="item in $utils.getEnableDictStatus('RECEIPT_CLAIM_STATUS')" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </fe-form-item>
            </el-col>
            <el-col :span="6">
              <fe-form-item label="交易日期">
                <el-date-picker v-model="searchPaymentDate" type="daterange" format="YYYY-MM-DD" value-format="x" />
              </fe-form-item>
            </el-col>
          </fe-search-inner>
          <table-inner v-loading="loading.list" :table-data="tableList.records" class="bg-white" show-set-columns :column-option="noticeColumnOptionList" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" row-key="pid" stripe title="信息明细" @change-page-num="changePageNum" @change-page-size="changePageSize" @selection-change="selectionChange">
            <template #btn-inner>
              <!--<el-button type="primary">手动同步</el-button>-->
              <el-button type="primary" @click="viewCancelList">
                拒绝历史
              </el-button>
              <el-button type="danger" @click="del">
                拒绝认领
              </el-button>
              <el-button type="primary" @click="mergeClaim">
                收款认领
              </el-button>
              <el-button type="primary" @click="$router.push({ name: 'repayClaimCreate' })">
                新增收款
              </el-button>
            </template>
            <template #table-columns-before>
              <el-table-column fixed="left" type="selection" :selectable="isSelectable" reserve-selection />
            </template>
            <!--<template #table-columns-after>-->
            <!--  <el-table-column fixed="right" label="操作" width="120">-->
            <!--    <template #default="scope">-->
            <!--      <table-link v-if="scope.row.claimStatus !== 'fullClaim' && scope.row.dataSource === 'settlementPlatform'" :to="{ name: 'repayClaimAdd', query: { pidList: scope.row.pid, type: 'settlementPlatform' } }" type="primary">-->
            <!--        确认-->
            <!--      </table-link>-->
            <!--      <table-link v-if="scope.row.claimStatus !== 'fullClaim' && scope.row.dataSource === 'manualEntry'" :to="{ name: 'repayClaimAdd', query: { pidList: scope.row.pid, type: 'manualEntry' } }" type="primary">-->
            <!--        确认-->
            <!--      </table-link>-->
            <!--      <el-link v-if="scope.row.claimStatus === 'pendingClaim' && scope.row.dataSource === 'manualEntry'" type="danger" @click="del(scope.row)">-->
            <!--        删除-->
            <!--      </el-link>-->
            <!--    </template>-->
            <!--  </el-table-column>-->
            <!--</template>-->
          </table-inner>
        </el-tab-pane>
        <el-tab-pane label="收款认领记录" name="record">
          <fe-search-inner :search-form="searchForm" class="mb-[15px]" @submit-search="search" @clear-search="clearSearch">
            <el-col :span="6">
              <fe-form-item label="付款方名称">
                <el-input v-model="searchForm.payerName" placeholder="请输入付款方名称" />
              </fe-form-item>
            </el-col>
            <el-col :span="6">
              <fe-form-item label="收款方名称">
                <el-input v-model="searchForm.payeeName" placeholder="请输入收款方名称" />
              </fe-form-item>
            </el-col>
            <el-col :span="6">
              <fe-form-item label="认领类型">
                <el-select v-model="searchForm.claimType" placeholder="请选择认领类型">
                  <el-option v-for="item in $utils.getEnableDictStatus('CLAIM_TYPE')" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </fe-form-item>
            </el-col>
            <el-col :span="6">
              <fe-form-item label="实际付款日期">
                <el-date-picker v-model="searchPaymentDate" type="daterange" format="YYYY-MM-DD" value-format="x" />
              </fe-form-item>
            </el-col>
          </fe-search-inner>
          <table-inner v-loading="loading.list" :table-data="tableList.records" show-set-columns :column-option="recordColumnOptionList" :page-num="tableList.current" :page-size="tableList.size" :total="tableList.total" stripe title="信息明细" class="bg-white" @change-page-num="changePageNum" @change-page-size="changePageSize">
            <template #table-columns-after>
              <el-table-column fixed="right" label="操作" width="120">
                <template #default="scope">
                  <table-link :to="{ name: 'repayClaimDetail', query: { pid: scope.row.pid } }" type="primary">
                    详情
                  </table-link>
                  <el-link v-if="canCancel(scope.row)" type="danger" @click="cancel(scope.row)">
                    取消收款
                  </el-link>
                </template>
              </el-table-column>
            </template>
          </table-inner>
        </el-tab-pane>
      </el-tabs>
    </div>
    <cancel-claim-list ref="cancelClaimList" />
  </fe-page-main>
</template>

<style>

</style>
