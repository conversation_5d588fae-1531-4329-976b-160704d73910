// 使用storageType参数来区分是localStorage还是sessionStorage
function getStorage (storageType = 'local') {
  return storageType === 'session'
    ? window.sessionStorage
    : window.localStorage
}

// 保存数据到指定的存储中
function setItem (key, value, storageType = 'local') {
  const storage = getStorage(storageType)
  try {
    storage.setItem(key, JSON.stringify(value))
  } catch (e) {
    console.error('Error saving data to storage:', e)
  }
}

// 从指定的存储中获取数据
function getItem (key, storageType = 'local') {
  const storage = getStorage(storageType)
  try {
    const item = storage.getItem(key)
    return item ? JSON.parse(item) : null
  } catch (e) {
    console.error('Error getting data from storage:', e)
    return null
  }
}

// 从指定的存储中删除数据
function removeItem (key, storageType = 'local') {
  const storage = getStorage(storageType)
  try {
    storage.removeItem(key)
  } catch (e) {
    console.error('Error removing data from storage:', e)
  }
}

// 清空指定的存储
function clear (storageType = 'local') {
  const storage = getStorage(storageType)
  try {
    storage.clear()
  } catch (e) {
    console.error('Error clearing storage:', e)
  }
}

export { setItem, getItem, removeItem, clear }
