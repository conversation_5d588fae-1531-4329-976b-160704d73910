/*
 * @Author: x<PERSON>ijie
 * @Date: 2022-08-15
 * @Description: 校验规则
 */
// 地址长度非必填
export const notRequiredAddress = (rule, value, callback) => {
  if (value) {
    const valueList = value.toUpperCase().split(' ')
    if (valueList[1]) {
      if (valueList[1].length > 50) {
        callback(new Error('详细地址最多输入50个字符'))
      } else {
        callback()
      }
    } else {
      callback()
    }
  } else {
    callback()
  }
}
// 统一社会信用代码若校验必填
export const requiredOrgNo = (rule, value, callback) => {
  const reg = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/
  if (!value) {
    callback(new Error('请输入统一社会信用代码'))
  } else {
    if (!reg.test(value)) {
      callback(new Error('统一社会信用代码格式不正确'))
    } else {
      callback()
    }
  }
}
// 手机号和电话号码校验必填
export const requiredTelphone = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入正确的联系电话'))
  } else {
    if (/\d{3}-\d{8}|\d{4}-\d{7}|^1([3-9])\d{9}$/.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的联系电话'))
    }
  }
}
// 手机号和电话号码校验非必填
export const notRequiredTelphone = (rule, value, callback) => {
  if (value) {
    if (/\d{3}-\d{8}|\d{4}-\d{7}|^1([3-9])\d{9}$/.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的联系电话'))
    }
  } else {
    callback()
  }
}
// 车牌号（含新能源）必填
export const requiredCarNo = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入车牌号'))
  } else {
    const checkCarNo = /^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[a-zA-Z](([DF]((?![IO])[a-zA-Z0-9](?![IO]))[0-9]{4})|([0-9]{5}[DF]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1})$/
    if (!checkCarNo.test(value)) {
      callback(new Error('请输入正确的车牌号'))
    } else {
      callback()
    }
  }
}
// 最大金额必填（注册资本）
export const requiredMaxNumber = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入注册资本'))
  } else {
    if (Number(value) > 9999999999.99) {
      callback(new Error('最大可输入9999999999.99'))
    } else {
      if (!/^\d+([.]{1}[0-9]{1,2})?$/.test(value)) {
        callback(new Error('小数点精确到后两位'))
      } else {
        callback()
      }
    }
  }
}
// 最大金额非必填
export const notRequiredMaxNumber = (rule, value, callback) => {
  if (value) {
    if (Number(value) > 9999999999.99) {
      callback(new Error('最大可输入9999999999.99'))
    } else {
      if (!/^\d+([.]{1}[0-9]{1,2})?$/.test(value)) {
        callback(new Error('小数点精确到后两位'))
      } else {
        callback()
      }
    }
  } else {
    callback()
  }
}
// 海关编码百分比非必填
export const notRequiredMaxPercentage = (rule, value, callback) => {
  if (value) {
    if (Number(value) > 200 || Number(value) < 0) {
      callback(new Error('请输入0~200之间的数值'))
    } else {
      if (!/^\d+([.]{1}[0-9]{1,2})?$/.test(value)) {
        callback(new Error('小数点精确到后两位'))
      } else {
        callback()
      }
    }
  } else {
    callback()
  }
}
